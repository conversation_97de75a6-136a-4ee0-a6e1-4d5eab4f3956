import { <PERSON>mponent, OnInit, ViewChild, ElementRef, AfterViewInit, <PERSON><PERSON>estroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonicModule, ToastController } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { GoalJournalEntry } from '../../../models/goal.model';
import { GoalService } from '../../../services/goal.service';

@Component({
  selector: 'app-journal-entry-detail',
  templateUrl: './journal-entry-detail.page.html',
  styleUrls: ['./journal-entry-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent]
})
export class JournalEntryDetailPage implements OnInit, AfterViewInit, OnD<PERSON>roy {
  @ViewChild('richEditor', { static: false }) richEditor!: ElementRef<HTMLDivElement>;

  goalId: string = '';
  entryId: string = '';
  entry: GoalJournalEntry | null = null;
  isEditing: boolean = false;
  editContent: string = '';
  isLoading: boolean = true;
  activeDropdown: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private goalService: GoalService,
    private toastController: ToastController,
    private sanitizer: DomSanitizer
  ) {}

  ngOnInit() {
    this.goalId = this.route.snapshot.paramMap.get('goalId') || '';
    this.entryId = this.route.snapshot.paramMap.get('entryId') || '';

    if (this.goalId && this.entryId) {
      this.loadJournalEntry();
    } else {
      this.router.navigate(['/goals']);
    }
  }

  ngAfterViewInit() {
    // Initialize rich editor content after view is ready
    if (this.isEditing && this.richEditor) {
      this.richEditor.nativeElement.innerHTML = this.editContent;
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

    // Add click outside handler for dropdowns
    document.addEventListener('click', this.handleClickOutside.bind(this));
  }

  ngOnDestroy() {
    // Clean up event listeners
    document.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    document.removeEventListener('click', this.handleClickOutside.bind(this));
  }

  loadJournalEntry() {
    this.isLoading = true;

    // Get all journal entries for the goal and find the specific one
    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {
      this.entry = entries.find(e => e.id === this.entryId) || null;
      this.isLoading = false;

      if (!this.entry) {
        this.showToast('Journal entry not found');
        this.router.navigate(['/goals', this.goalId]);
      }
    });
  }

  startEditing() {
    if (this.entry) {
      this.editContent = this.entry.content;
      this.isEditing = true;

      // Set content after view updates
      setTimeout(() => {
        if (this.richEditor) {
          this.richEditor.nativeElement.innerHTML = this.editContent;
          this.setupCheckboxListeners();
        }
      }, 0);
    }
  }

  cancelEditing() {
    this.isEditing = false;
    this.editContent = '';
  }

  async saveEntry() {
    if (!this.entry || !this.editContent.trim()) return;

    try {
      await this.goalService.updateJournalEntry(this.entry.id!, { content: this.editContent.trim() });
      this.entry.content = this.editContent.trim();
      this.isEditing = false;
      this.editContent = '';
      this.showToast('Journal entry updated successfully');
    } catch (error) {
      console.error('Error updating journal entry:', error);
      this.showToast('Error updating journal entry');
    }
  }

  async deleteEntry() {
    if (!this.entry) return;

    const confirmed = confirm('Are you sure you want to delete this journal entry? This action cannot be undone.');
    if (!confirmed) return;

    try {
      await this.goalService.deleteJournalEntry(this.entry.id!);
      this.showToast('Journal entry deleted successfully');
      this.router.navigate(['/goals', this.goalId]);
    } catch (error) {
      console.error('Error deleting journal entry:', error);
      this.showToast('Error deleting journal entry');
    }
  }

  goBack() {
    this.router.navigate(['/goals', this.goalId]);
  }

  private async showToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      position: 'bottom'
    });
    toast.present();
  }

  getSafeHtml(content: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  // Rich text formatting methods
  formatText(command: string, value?: string) {
    document.execCommand(command, false, value);
  }

  insertList(ordered: boolean = false) {
    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';
    document.execCommand(command, false);
  }

  onContentInput(event: any) {
    this.editContent = event.target.innerHTML;
    this.setupCheckboxListeners();
  }

  onContentBlur(event: any) {
    this.editContent = event.target.innerHTML;
  }

  setupCheckboxListeners() {
    if (this.richEditor) {
      const checkboxes = this.richEditor.nativeElement.querySelectorAll('.checklist-checkbox');
      checkboxes.forEach((checkbox: any) => {
        // Sync checkbox state with attribute
        checkbox.checked = checkbox.hasAttribute('checked');

        checkbox.removeEventListener('change', this.handleCheckboxChange.bind(this));
        checkbox.addEventListener('change', (event) => this.handleCheckboxChange(event));
      });
    }
  }

  handleCheckboxChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;

    // Update the specific checkbox attribute
    if (checkbox.checked) {
      checkbox.setAttribute('checked', 'checked');
    } else {
      checkbox.removeAttribute('checked');
    }

    // Update content
    if (this.richEditor) {
      this.editContent = this.richEditor.nativeElement.innerHTML;
    }
  }

  // Dropdown management
  toggleDropdown(dropdown: string) {
    this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;
  }

  closeDropdown() {
    this.activeDropdown = null;
  }

  // Advanced formatting methods
  insertCheckList() {
    if (!this.richEditor) return;

    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // Ensure we're inserting inside the editor
      if (!this.richEditor.nativeElement.contains(range.commonAncestorContainer)) {
        // If selection is outside editor, insert at the end
        const newRange = document.createRange();
        newRange.selectNodeContents(this.richEditor.nativeElement);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
        range.setStart(newRange.startContainer, newRange.startOffset);
        range.setEnd(newRange.endContainer, newRange.endOffset);
      }

      const checklistItem = document.createElement('div');
      checklistItem.className = 'checklist-item';
      checklistItem.innerHTML = '<input type="checkbox" class="checklist-checkbox"> <span contenteditable="true">New item</span>';

      range.deleteContents();
      range.insertNode(checklistItem);

      // Add event listener to the new checkbox
      const checkbox = checklistItem.querySelector('.checklist-checkbox') as HTMLInputElement;
      if (checkbox) {
        checkbox.addEventListener('change', (event) => this.handleCheckboxChange(event));
      }

      // Move cursor to the text span
      const textSpan = checklistItem.querySelector('span');
      if (textSpan) {
        const newRange = document.createRange();
        newRange.selectNodeContents(textSpan);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }

      // Update content
      this.editContent = this.richEditor.nativeElement.innerHTML;
    }
  }

  insertLink() {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const selectedText = selection.toString();
      const url = prompt('Enter URL:', 'https://');

      if (url && url.trim()) {
        const link = document.createElement('a');
        link.href = url.trim();
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        link.className = 'notion-link';
        link.textContent = selectedText || url.trim();

        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(link);

        // Move cursor after link
        range.setStartAfter(link);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  }

  insertCode() {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const selectedText = selection.toString();
      if (selectedText) {
        // Inline code
        const code = document.createElement('code');
        code.className = 'notion-code';
        code.textContent = selectedText;

        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(code);
      } else {
        // Code block
        const range = selection.getRangeAt(0);
        const codeBlock = document.createElement('pre');
        codeBlock.className = 'notion-code-block';
        codeBlock.innerHTML = '<code contenteditable="true">// Your code here</code>';

        range.deleteContents();
        range.insertNode(codeBlock);

        // Select the code content
        const codeElement = codeBlock.querySelector('code');
        if (codeElement) {
          const newRange = document.createRange();
          newRange.selectNodeContents(codeElement);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
      }
    }
  }

  insertDivider() {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const divider = document.createElement('hr');
      divider.className = 'notion-divider';

      range.deleteContents();
      range.insertNode(divider);

      // Move cursor after divider
      range.setStartAfter(divider);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }

  setTextColor(color: string) {
    if (color === 'default') {
      this.formatText('removeFormat');
      this.formatText('foreColor', '#ffffff');
    } else {
      this.formatText('foreColor', color);
    }
  }

  setBackgroundColor(color: string) {
    if (color === 'default') {
      this.formatText('removeFormat');
    } else {
      this.formatText('backColor', color);
    }
  }

  // Keyboard shortcuts
  handleKeyboardShortcuts(event: KeyboardEvent) {
    if (!this.isEditing) return;

    const isCtrl = event.ctrlKey || event.metaKey;

    if (isCtrl) {
      switch (event.key.toLowerCase()) {
        case 'b':
          event.preventDefault();
          this.formatText('bold');
          break;
        case 'i':
          event.preventDefault();
          this.formatText('italic');
          break;
        case 'u':
          event.preventDefault();
          this.formatText('underline');
          break;
        case 'z':
          event.preventDefault();
          this.formatText('undo');
          break;
        case 'y':
          event.preventDefault();
          this.formatText('redo');
          break;
        case 's':
          event.preventDefault();
          this.saveEntry();
          break;
      }
    }

    // Escape to close dropdowns
    if (event.key === 'Escape') {
      this.closeDropdown();
    }
  }

  // Click outside handler
  handleClickOutside(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown-container')) {
      this.closeDropdown();
    }
  }
}
