{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _ProfilePage;\nimport { inject } from '@angular/core';\nimport { IonRouterOutlet } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { UserService } from '../../services/user.service';\nimport { GoalService } from '../../services/goal.service';\nimport { of, switchMap, combineLatest, map } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { XpService, EntityType } from '../../services/xp.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/badges\", a0];\nconst _c1 = () => [\"/profile-settings\"];\nfunction ProfilePage_div_8_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.user.profile_picture, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.user.username);\n  }\n}\nfunction ProfilePage_div_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProfilePage_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.user.bio);\n  }\n}\nfunction ProfilePage_div_8_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(1, \"Add Bio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePage_div_8_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 40);\n    i0.ɵɵelement(7, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 42)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", category_r5.progress, \"%\")(\"background-color\", category_r5.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r5.current_xp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r5.required_xp, \" XP needed\");\n  }\n}\nfunction ProfilePage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6);\n    i0.ɵɵtemplate(3, ProfilePage_div_8_img_3_Template, 1, 2, \"img\", 7)(4, ProfilePage_div_8_ng_container_4_Template, 2, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12)(11, \"div\", 13);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 14);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 15);\n    i0.ɵɵtemplate(16, ProfilePage_div_8_div_16_Template, 2, 1, \"div\", 16)(17, ProfilePage_div_8_button_17_Template, 2, 0, \"button\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 18)(19, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfilePage_div_8_Template_form_ngSubmit_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateBio());\n    });\n    i0.ɵɵelementStart(20, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProfilePage_div_8_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedBio, $event) || (ctx_r1.editedBio = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 21)(22, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(23, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 23);\n    i0.ɵɵtext(25, \"Save\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(26, \"div\", 24)(27, \"h2\");\n    i0.ɵɵtext(28, \"XP Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, ProfilePage_div_8_div_29_Template, 13, 8, \"div\", 25);\n    i0.ɵɵelementStart(30, \"div\", 26)(31, \"div\", 27);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 28);\n    i0.ɵɵtext(34, \" Reach required XP in all categories to level up \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 29)(36, \"a\", 30)(37, \"span\", 31);\n    i0.ɵɵtext(38, \"\\uD83C\\uDFC6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(39, \" View Badges \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 32);\n    i0.ɵɵtext(41, \"\\u2699\\uFE0F Settings\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.profile_picture);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.user.profile_picture);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.user.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", ctx_r1.user.username, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Level \", ctx_r1.user.level, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.user.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.bio && ctx_r1.user.bio.trim() !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.user.bio || ctx_r1.user.bio.trim() === \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showBioForm ? \"block\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedBio);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Next Level: \", ctx_r1.nextLevel, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c0, ctx_r1.user.id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(17, _c1));\n  }\n}\nexport class ProfilePage {\n  constructor() {\n    // User data\n    this.user = null;\n    this.userSubscription = null;\n    // XP categories\n    this.categories = [];\n    this.nextLevel = 0;\n    // Bio form\n    this.showBioForm = false;\n    this.editedBio = '';\n    // Goals\n    this.publicGoals = [];\n    this.showAllGoalsModal = false;\n    this.supabaseService = inject(SupabaseService);\n    this.userService = inject(UserService);\n    this.goalService = inject(GoalService);\n    this.xpService = inject(XpService);\n    this.routerOutlet = inject(IonRouterOutlet);\n  }\n  ngOnInit() {\n    // Subscribe to the current user profile from UserService\n    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {\n      if (userProfile) {\n        var _user$level, _user$strength_xp, _user$money_xp, _user$health_xp, _user$knowledge_xp;\n        // Convert UserProfile to User\n        const user = userProfile;\n        // Store the original user data for reference\n        // Ensure all required fields have default values\n        this.user = {\n          ...user,\n          level: (_user$level = user.level) !== null && _user$level !== void 0 ? _user$level : 1,\n          // Use nullish coalescing to handle 0 values correctly\n          strength_xp: (_user$strength_xp = user.strength_xp) !== null && _user$strength_xp !== void 0 ? _user$strength_xp : 0,\n          money_xp: (_user$money_xp = user.money_xp) !== null && _user$money_xp !== void 0 ? _user$money_xp : 0,\n          health_xp: (_user$health_xp = user.health_xp) !== null && _user$health_xp !== void 0 ? _user$health_xp : 0,\n          knowledge_xp: (_user$knowledge_xp = user.knowledge_xp) !== null && _user$knowledge_xp !== void 0 ? _user$knowledge_xp : 0,\n          bio: user.bio || '',\n          title: user.title || '🥚 Beginner'\n        };\n        this.editedBio = this.user.bio || '';\n        this.calculateXpProgress();\n        this.loadPublicGoals();\n        // Ensure edit button visibility is correct on page load\n        setTimeout(() => {\n          var _this$user;\n          const editBioBtn = document.getElementById('edit-bio-btn');\n          if (editBioBtn && (!((_this$user = this.user) !== null && _this$user !== void 0 && _this$user.bio) || this.user.bio.trim() === '')) {\n            editBioBtn.style.display = 'none';\n          }\n        }, 100);\n      } else {\n        console.error('Profile: No user profile data received');\n        // If no profile data, try to get the auth user and ensure it exists\n        this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n          if (!authUser) {\n            return of(null);\n          }\n          // Use the ensureUserExists method to get or create the user\n          return this.userService.ensureUserExists(authUser);\n        })).subscribe();\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  ionViewWillEnter() {\n    // Check if we're coming from another page (not initial load)\n    if (this.routerOutlet.canGoBack()) {\n      // Refresh the user profile\n      this.userService.refreshCurrentUserProfile().then(() => {}).catch(error => {\n        console.error('Profile: Error refreshing profile:', error);\n      });\n    } else {}\n  }\n  calculateXpProgress() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.user) {\n        console.error('Cannot calculate XP progress: user is null');\n        return;\n      }\n      // Make sure user.level is defined and is a number\n      if (typeof _this.user.level !== 'number') {\n        console.error('User level is not a number:', _this.user.level);\n        _this.user.level = 1; // Default to level 1 if not set\n      }\n      // Use the XP service to calculate the progress\n      _this.xpService.calculateXpProgress(_this.user, EntityType.USER).subscribe(result => {\n        if (result) {\n          _this.categories = result.categories;\n          _this.nextLevel = result.next_level;\n        }\n      });\n    })();\n  }\n  toggleBioForm() {\n    this.showBioForm = !this.showBioForm;\n    // Reset the edited bio to the current bio when opening the form\n    if (this.showBioForm && this.user) {\n      this.editedBio = this.user.bio || '';\n    }\n    // When closing the form, ensure edit button visibility is correct\n    if (!this.showBioForm) {\n      setTimeout(() => {\n        var _this$user2, _this$user3;\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!((_this$user2 = this.user) !== null && _this$user2 !== void 0 && _this$user2.bio) || ((_this$user3 = this.user) === null || _this$user3 === void 0 ? void 0 : _this$user3.bio.trim()) === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    }\n  }\n  updateBio() {\n    if (!this.user || !this.user.id) return;\n    // Trim and validate bio\n    const bio = this.editedBio.trim();\n    if (bio.length > 100) {\n      // Show error message\n      return;\n    }\n    // Update user bio (will be set to empty string if only whitespace)\n    this.userService.updateUserBio(this.user.id, bio).then(() => {\n      if (this.user) {\n        // If bio is empty, set it to empty string\n        this.user.bio = bio === '' ? '' : bio;\n      }\n      this.showBioForm = false;\n      // Force DOM update to ensure edit button visibility is correct\n      setTimeout(() => {\n        var _this$user4;\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!((_this$user4 = this.user) !== null && _this$user4 !== void 0 && _this$user4.bio) || this.user.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    });\n  }\n  loadPublicGoals() {\n    var _this$user5;\n    if (!((_this$user5 = this.user) !== null && _this$user5 !== void 0 && _this$user5.id)) return;\n    this.goalService.getPublicGoals(this.user.id).pipe(switchMap(goals => {\n      if (goals.length === 0) {\n        return of([]);\n      }\n      // Get microgoals for each goal\n      const goalObservables = goals.map(goal => this.goalService.getMicroGoals(goal.id).pipe(map(microgoals => ({\n        ...goal,\n        microgoals\n      }))));\n      return combineLatest(goalObservables);\n    })).subscribe({\n      next: goalsWithMicrogoals => {\n        this.publicGoals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('Error loading public goals:', error);\n      }\n    });\n  }\n  get displayedGoals() {\n    return this.publicGoals.slice(0, 3);\n  }\n  openAllGoalsModal() {\n    this.showAllGoalsModal = true;\n  }\n  closeAllGoalsModal() {\n    this.showAllGoalsModal = false;\n  }\n}\n_ProfilePage = ProfilePage;\n_ProfilePage.ɵfac = function ProfilePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ProfilePage)();\n};\n_ProfilePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _ProfilePage,\n  selectors: [[\"app-profile\"]],\n  decls: 10,\n  vars: 1,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [1, \"profile-container\"], [1, \"profile-header\"], [1, \"profile-picture\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-username\"], [1, \"profile-level\"], [1, \"level-badge\"], [1, \"profile-title\"], [1, \"profile-bio-container\"], [\"class\", \"profile-bio\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"add-bio-btn\", \"class\", \"edit-bio-btn\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"bio-form\", 1, \"bio-form\"], [3, \"ngSubmit\"], [\"type\", \"text\", \"name\", \"bio\", \"maxlength\", \"100\", \"placeholder\", \"Add a short bio (max 100 characters)\", 3, \"ngModelChange\", \"ngModel\"], [1, \"bio-form-buttons\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"save-btn\"], [1, \"xp-section\"], [\"class\", \"category-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"next-level-info\"], [1, \"next-level-text\"], [1, \"next-level-requirements\"], [1, \"button-container\"], [1, \"badges-button\", 3, \"routerLink\"], [2, \"margin-right\", \"5px\"], [1, \"settings-button\", 3, \"routerLink\"], [3, \"src\", \"alt\"], [1, \"profile-bio\", 3, \"click\"], [\"id\", \"add-bio-btn\", 1, \"edit-bio-btn\", 3, \"click\"], [1, \"category-card\"], [1, \"category-header\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"xp-text\"]],\n  template: function ProfilePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Profile\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(8, ProfilePage_div_8_Template, 42, 18, \"div\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(9, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.user);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent],\n  styles: [\".quest-message[_ngcontent-%COMP%] {\\n  color: #FF9500;\\n  font-weight: 500;\\n  font-size: small;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent-color);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  \\n\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent-color);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n\\n\\n\\n.schedule-container[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.days-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.day-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.day-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  cursor: pointer;\\n  margin-bottom: 0;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 2px;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.quests[_ngcontent-%COMP%], .side-quests[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n}\\n\\n.quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.quest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.progress[_ngcontent-%COMP%], .progress-time[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n}\\n\\n.quest-streak[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.side-quests[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 32px;\\n}\\n\\n.quests[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 4px 0;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 4px;\\n  background: var(--inactive-date);\\n  outline: none;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: var(--accent-color);\\n  cursor: pointer;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: var(--accent-color);\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) 50%, var(--bg-tertiary) 50%, var(--bg-tertiary) 100%);\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text);\\n  margin-top: 2px;\\n}\\n\\n[_nghost-%COMP%] {\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n  --active-date: #4169E1;\\n  --inactive-date: #2C2C2E;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  scrollbar-width: none;\\n  overflow-y: auto;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.scroll-y[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.week-calendar[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.days[_ngcontent-%COMP%], .dates[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  gap: 8px;\\n}\\n\\n.days[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  margin: 0 auto;\\n  font-size: 14px;\\n  background-color: var(--inactive-date);\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  position: relative;\\n}\\n\\n.date-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  fill: transparent;\\n  stroke-width: 2.5;\\n  stroke-linecap: round;\\n  transform-origin: center;\\n  transform: rotate(-90deg);\\n  transition: stroke-dasharray 0.3s ease;\\n}\\n\\n\\n\\n.date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: var(--accent-color);\\n  stroke-opacity: 0.9;\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   .progress-circle.low[_ngcontent-%COMP%] {\\n  stroke: var(--accent-color);\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  stroke-width: 3;\\n}\\n\\n\\n\\n.date.selected[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #78a8f3;\\n  stroke-opacity: 0.7;\\n}\\n\\n.date.active[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #78a8f3;\\n  \\n\\n  \\n\\n  stroke-opacity: 0.7;\\n}\\n\\n.date-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.date.active[_ngcontent-%COMP%] {\\n  background-color: var(--active-date);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -4px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 4px;\\n  height: 4px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n}\\n\\n.date[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.date.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 16px;\\n}\\n\\n.side-quests[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 90%;\\n  height: 1px;\\n  background: linear-gradient(to right, transparent, #4B0082, transparent);\\n}\\n\\n.calendar[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  padding: 10px;\\n  background: var(--bg-secondary);\\n  border-radius: 8px;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 10px;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 5px;\\n  text-align: center;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  margin: 0 auto;\\n}\\n\\n.day-number[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.day-number.selected[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.day-number.today[_ngcontent-%COMP%] {\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: none;\\n  background: var(--bg-tertiary);\\n  color: var(--text-primary);\\n  border-radius: 4px;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  margin-right: 16px;\\n}\\n\\n\\n\\ninput[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, \\ninput[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button {\\n  opacity: 0.3;\\n}\\n\\n.profile-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  margin-bottom: 80px;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.profile-picture[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background-color: var(--card-bg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 32px;\\n  margin-right: 20px;\\n  overflow: hidden;\\n}\\n\\n.profile-picture[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.profile-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n\\n.profile-username[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--secondary-text);\\n  margin-bottom: 5px;\\n}\\n\\n.profile-bio[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-secondary, #a0a3b1);\\n  margin-bottom: 10px;\\n  font-style: italic;\\n  max-width: 300px;\\n  cursor: pointer;\\n}\\n\\n.profile-bio-container[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  padding-left: 100px;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: var(--accent-color, #4169e1);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(65, 105, 225, 0.1);\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .profile-bio:empty + #edit-bio-btn {\\n  display: none;\\n}\\n\\n.bio-form[_ngcontent-%COMP%] {\\n  margin: 0 auto 20px;\\n  max-width: 400px;\\n}\\n\\n.bio-form[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border-radius: 8px;\\n  border: 1px solid var(--border, #26272e);\\n  background-color: var(--surface, #16171c);\\n  color: var(--text, #ffffff);\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n}\\n\\n.bio-form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color, #4169e1);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: var(--text, #ffffff);\\n  border: 1px solid var(--border, #26272e);\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n\\n.profile-level[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 5px;\\n}\\n\\n.level-badge[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n  border-radius: 12px;\\n  padding: 2px 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-right: 10px;\\n}\\n\\n.profile-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--accent-color);\\n}\\n\\n.xp-section[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.xp-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  border-radius: 12px;\\n  padding: 15px;\\n  margin-bottom: 15px;\\n}\\n\\n.category-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 10px;\\n}\\n\\n.category-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background-color: var(--bg-color);\\n  border-radius: 5px;\\n  margin-bottom: 8px;\\n  overflow: hidden;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  border-radius: 5px;\\n}\\n\\n.xp-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.next-level-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n  padding: 15px;\\n  background-color: var(--card-bg);\\n  border-radius: 12px;\\n}\\n\\n.next-level-text[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n}\\n\\n.next-level-requirements[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  margin-top: 15px;\\n  display: inline-block;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.back-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n  gap: 10px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-top: 10px;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  color: var(--accent-color);\\n  border: 1px solid var(--accent-color);\\n  border-radius: 20px;\\n  padding: 5px 12px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.action-button.danger[_ngcontent-%COMP%] {\\n  color: var(--danger-color);\\n  border-color: var(--danger-color);\\n}\\n\\n.settings-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  background-color: var(--accent-color);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 10px 20px;\\n  font-size: 16px;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.badges-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-right: 10px;\\n  padding: 8px 16px;\\n  background-color: #1c1c1e;\\n  color: white;\\n  border: 1px solid #4d7bff;\\n  border-radius: 20px;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 30px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "IonRouterOutlet", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "UserService", "GoalService", "of", "switchMap", "combineLatest", "map", "NavigationComponent", "SupabaseService", "XpService", "EntityType", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "user", "profile_picture", "ɵɵsanitizeUrl", "username", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementStart", "ɵɵlistener", "ProfilePage_div_8_div_16_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "toggleBioForm", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "bio", "ProfilePage_div_8_button_17_Template_button_click_0_listener", "_r4", "category_r5", "icon", "name", "ɵɵstyleProp", "progress", "color", "ɵɵtextInterpolate1", "current_xp", "required_xp", "ɵɵtemplate", "ProfilePage_div_8_img_3_Template", "ProfilePage_div_8_ng_container_4_Template", "ProfilePage_div_8_div_16_Template", "ProfilePage_div_8_button_17_Template", "ProfilePage_div_8_Template_form_ngSubmit_19_listener", "_r1", "updateBio", "ɵɵtwoWayListener", "ProfilePage_div_8_Template_input_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "editedBio", "ProfilePage_div_8_Template_button_click_22_listener", "ProfilePage_div_8_div_29_Template", "level", "title", "trim", "showBioForm", "ɵɵtwoWayProperty", "categories", "nextLevel", "ɵɵpureFunction1", "_c0", "id", "ɵɵpureFunction0", "_c1", "ProfilePage", "constructor", "userSubscription", "publicGoals", "showAllGoalsModal", "supabaseService", "userService", "goalService", "xpService", "routerOutlet", "ngOnInit", "currentUserProfile$", "subscribe", "userProfile", "_user$level", "_user$strength_xp", "_user$money_xp", "_user$health_xp", "_user$knowledge_xp", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "calculateXpProgress", "loadPublicGoals", "setTimeout", "_this$user", "editBioBtn", "document", "getElementById", "style", "display", "console", "error", "currentUser$", "pipe", "authUser", "ensureUserExists", "ngOnDestroy", "unsubscribe", "ionViewWillEnter", "canGoBack", "refreshCurrentUserProfile", "then", "catch", "_this", "_asyncToGenerator", "USER", "result", "next_level", "_this$user2", "_this$user3", "length", "updateUserBio", "_this$user4", "_this$user5", "getPublicGoals", "goals", "goalObservables", "goal", "getMicroGoals", "microgoals", "next", "goalsWithMicrogoals", "displayedGoals", "slice", "openAllGoalsModal", "closeAllGoalsModal", "selectors", "decls", "vars", "consts", "template", "ProfilePage_Template", "rf", "ctx", "ProfilePage_div_8_Template", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\profile\\profile.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\profile\\profile.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\nimport { IonRouterOutlet } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { UserService } from '../../services/user.service';\nimport { GoalService } from '../../services/goal.service';\nimport { User } from '../../models/user.model';\nimport { Goal, MicroGoal } from '../../models/supabase.models';\nimport { Subscription, of, switchMap, combineLatest, map } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { XpService, EntityType } from '../../services/xp.service';\n\ninterface CategoryDisplay {\n  name: string;\n  icon: string;\n  color: string;\n  current_xp: number;\n  required_xp: number;\n  progress: number;\n}\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.page.html',\n  styleUrls: ['./profile.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]\n})\nexport class ProfilePage implements OnInit {\n  // User data\n  user: User | null = null;\n  userSubscription: Subscription | null = null;\n\n  // XP categories\n  categories: CategoryDisplay[] = [];\n  nextLevel = 0;\n\n  // Bio form\n  showBioForm = false;\n  editedBio = '';\n\n  // Goals\n  publicGoals: (Goal & { microgoals: MicroGoal[] })[] = [];\n  showAllGoalsModal = false;\n\n  private supabaseService = inject(SupabaseService);\n  private userService = inject(UserService);\n  private goalService = inject(GoalService);\n  private xpService = inject(XpService);\n  private routerOutlet = inject(IonRouterOutlet);\n\n  constructor() {}\n\n  ngOnInit() {\n    // Subscribe to the current user profile from UserService\n    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {\n\n      if (userProfile) {\n        // Convert UserProfile to User\n        const user = userProfile as unknown as User;\n\n        // Store the original user data for reference\n\n        // Ensure all required fields have default values\n        this.user = {\n          ...user,\n          level: user.level ?? 1,  // Use nullish coalescing to handle 0 values correctly\n          strength_xp: user.strength_xp ?? 0,\n          money_xp: user.money_xp ?? 0,\n          health_xp: user.health_xp ?? 0,\n          knowledge_xp: user.knowledge_xp ?? 0,\n          bio: user.bio || '',\n          title: user.title || '🥚 Beginner'\n        };\n\n        this.editedBio = this.user.bio || '';\n\n\n\n        this.calculateXpProgress();\n        this.loadPublicGoals();\n\n        // Ensure edit button visibility is correct on page load\n        setTimeout(() => {\n          const editBioBtn = document.getElementById('edit-bio-btn');\n          if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {\n            editBioBtn.style.display = 'none';\n          }\n        }, 100);\n      } else {\n        console.error('Profile: No user profile data received');\n\n        // If no profile data, try to get the auth user and ensure it exists\n        this.supabaseService.currentUser$.pipe(\n          switchMap(authUser => {\n            if (!authUser) {\n              return of(null);\n            }\n\n\n            // Use the ensureUserExists method to get or create the user\n            return this.userService.ensureUserExists(authUser);\n          })\n        ).subscribe();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n\n  ionViewWillEnter() {\n\n    // Check if we're coming from another page (not initial load)\n    if (this.routerOutlet.canGoBack()) {\n\n      // Refresh the user profile\n      this.userService.refreshCurrentUserProfile().then(() => {\n      }).catch(error => {\n        console.error('Profile: Error refreshing profile:', error);\n      });\n    } else {\n    }\n  }\n\n  async calculateXpProgress() {\n    if (!this.user) {\n      console.error('Cannot calculate XP progress: user is null');\n      return;\n    }\n\n\n    // Make sure user.level is defined and is a number\n    if (typeof this.user.level !== 'number') {\n      console.error('User level is not a number:', this.user.level);\n      this.user.level = 1; // Default to level 1 if not set\n    }\n\n    // Use the XP service to calculate the progress\n    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {\n      if (result) {\n        this.categories = result.categories;\n        this.nextLevel = result.next_level;\n      }\n    });\n  }\n\n  toggleBioForm() {\n    this.showBioForm = !this.showBioForm;\n\n    // Reset the edited bio to the current bio when opening the form\n    if (this.showBioForm && this.user) {\n      this.editedBio = this.user.bio || '';\n    }\n\n    // When closing the form, ensure edit button visibility is correct\n    if (!this.showBioForm) {\n      setTimeout(() => {\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!this.user?.bio || this.user?.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    }\n  }\n\n  updateBio() {\n    if (!this.user || !this.user.id) return;\n\n    // Trim and validate bio\n    const bio = this.editedBio.trim();\n    if (bio.length > 100) {\n      // Show error message\n      return;\n    }\n\n    // Update user bio (will be set to empty string if only whitespace)\n    this.userService.updateUserBio(this.user.id, bio).then(() => {\n      if (this.user) {\n        // If bio is empty, set it to empty string\n        this.user.bio = bio === '' ? '' : bio;\n      }\n      this.showBioForm = false;\n\n      // Force DOM update to ensure edit button visibility is correct\n      setTimeout(() => {\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    });\n  }\n\n  loadPublicGoals() {\n    if (!this.user?.id) return;\n\n    this.goalService.getPublicGoals(this.user.id).pipe(\n      switchMap(goals => {\n        if (goals.length === 0) {\n          return of([]);\n        }\n\n        // Get microgoals for each goal\n        const goalObservables = goals.map(goal =>\n          this.goalService.getMicroGoals(goal.id!).pipe(\n            map(microgoals => ({\n              ...goal,\n              microgoals\n            }))\n          )\n        );\n\n        return combineLatest(goalObservables);\n      })\n    ).subscribe({\n      next: goalsWithMicrogoals => {\n        this.publicGoals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('Error loading public goals:', error);\n      }\n    });\n  }\n\n  get displayedGoals() {\n    return this.publicGoals.slice(0, 3);\n  }\n\n  openAllGoalsModal() {\n    this.showAllGoalsModal = true;\n  }\n\n  closeAllGoalsModal() {\n    this.showAllGoalsModal = false;\n  }\n}\n", "\r\n    <div class=\"container\">\r\n        <header>\r\n            <div class=\"logo\">\r\n                <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n                <span>Upshift</span>\r\n            </div>\r\n            <h1>Profile</h1>\r\n        </header>\r\n\r\n        <div class=\"profile-container\" *ngIf=\"user\">\r\n            <div class=\"profile-header\">\r\n                <div class=\"profile-picture\">\r\n                    <img *ngIf=\"user.profile_picture\" [src]=\"user.profile_picture\" [alt]=\"user.username\">\r\n                    <ng-container *ngIf=\"!user.profile_picture\">👤</ng-container>\r\n                </div>\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-name\">{{ user.name }}</div>\r\n                    <div class=\"profile-username\">&#64;{{ user.username }}</div>\r\n\r\n                    <div class=\"profile-level\">\r\n                        <div class=\"level-badge\">Level {{ user.level }}</div>\r\n                        <div class=\"profile-title\">{{ user.title }}</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"profile-bio-container\">\r\n                <div class=\"profile-bio\" *ngIf=\"user.bio && user.bio.trim() !== ''\" (click)=\"toggleBioForm()\">{{ user.bio }}</div>\r\n                <button *ngIf=\"!user.bio || user.bio.trim() === ''\" id=\"add-bio-btn\" class=\"edit-bio-btn\" (click)=\"toggleBioForm()\">Add Bio</button>\r\n\r\n            </div>\r\n\r\n            <div id=\"bio-form\" class=\"bio-form\" [style.display]=\"showBioForm ? 'block' : 'none'\">\r\n                <form (ngSubmit)=\"updateBio()\">\r\n                    <input type=\"text\" name=\"bio\" maxlength=\"100\" placeholder=\"Add a short bio (max 100 characters)\" [(ngModel)]=\"editedBio\">\r\n                    <div class=\"bio-form-buttons\">\r\n                        <button type=\"button\" class=\"cancel-btn\" (click)=\"toggleBioForm()\">Cancel</button>\r\n                        <button type=\"submit\" class=\"save-btn\">Save</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n\r\n            <div class=\"xp-section\">\r\n                <h2>XP Progress</h2>\r\n\r\n                <div class=\"category-card\" *ngFor=\"let category of categories\">\r\n                    <div class=\"category-header\">\r\n                        <div class=\"category-icon\">{{ category.icon }}</div>\r\n                        <div class=\"category-name\">{{ category.name }}</div>\r\n                    </div>\r\n                    <div class=\"progress-container\">\r\n                        <div class=\"progress-bar\" [style.width.%]=\"category.progress\" [style.background-color]=\"category.color\"></div>\r\n                    </div>\r\n                    <div class=\"xp-text\">\r\n                        <span>{{ category.current_xp }} XP</span>\r\n                        <span>{{ category.required_xp }} XP needed</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"next-level-info\">\r\n                    <div class=\"next-level-text\">Next Level: {{ nextLevel }}</div>\r\n                    <div class=\"next-level-requirements\">\r\n                        Reach required XP in all categories to level up\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"button-container\">\r\n                    <a [routerLink]=\"['/badges', user.id]\" class=\"badges-button\">\r\n                        <span style=\"margin-right: 5px;\">🏆</span> View Badges\r\n                    </a>\r\n                    <a [routerLink]=\"['/profile-settings']\" class=\"settings-button\">⚙️ Settings</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,6BAA6B;AAGzD,SAAuBC,EAAE,EAAEC,SAAS,EAAEC,aAAa,EAAEC,GAAG,QAAQ,MAAM;AACtE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;;;;;;;;;;ICA7CC,EAAA,CAAAC,SAAA,cAAqF;;;;IAAtBD,EAA7B,CAAAE,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,EAAAL,EAAA,CAAAM,aAAA,CAA4B,QAAAH,MAAA,CAAAC,IAAA,CAAAG,QAAA,CAAsB;;;;;IACpFP,EAAA,CAAAQ,uBAAA,GAA4C;IAAAR,EAAA,CAAAS,MAAA,mBAAE;;;;;;;IAalDT,EAAA,CAAAU,cAAA,cAA8F;IAA1BV,EAAA,CAAAW,UAAA,mBAAAC,uDAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,GAAc;IAAAT,EAAA,CAAAkB,YAAA,EAAM;;;;IAApBlB,EAAA,CAAAmB,SAAA,EAAc;IAAdnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAc;;;;;;IAC5GrB,EAAA,CAAAU,cAAA,iBAAoH;IAA1BV,EAAA,CAAAW,UAAA,mBAAAW,6DAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAApB,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,cAAO;IAAAT,EAAA,CAAAkB,YAAA,EAAS;;;;;IAmB5HlB,EAFR,CAAAU,cAAA,cAA+D,cAC9B,cACE;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IACpDlB,EAAA,CAAAU,cAAA,cAA2B;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAClDT,EADkD,CAAAkB,YAAA,EAAM,EAClD;IACNlB,EAAA,CAAAU,cAAA,cAAgC;IAC5BV,EAAA,CAAAC,SAAA,cAA8G;IAClHD,EAAA,CAAAkB,YAAA,EAAM;IAEFlB,EADJ,CAAAU,cAAA,cAAqB,WACX;IAAAV,EAAA,CAAAS,MAAA,IAA4B;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IACzClB,EAAA,CAAAU,cAAA,YAAM;IAAAV,EAAA,CAAAS,MAAA,IAAoC;IAElDT,EAFkD,CAAAkB,YAAA,EAAO,EAC/C,EACJ;;;;IAV6BlB,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,iBAAA,CAAAI,WAAA,CAAAC,IAAA,CAAmB;IACnBzB,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,iBAAA,CAAAI,WAAA,CAAAE,IAAA,CAAmB;IAGpB1B,EAAA,CAAAmB,SAAA,GAAmC;IAACnB,EAApC,CAAA2B,WAAA,UAAAH,WAAA,CAAAI,QAAA,MAAmC,qBAAAJ,WAAA,CAAAK,KAAA,CAA0C;IAGjG7B,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAA8B,kBAAA,KAAAN,WAAA,CAAAO,UAAA,QAA4B;IAC5B/B,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAA8B,kBAAA,KAAAN,WAAA,CAAAQ,WAAA,eAAoC;;;;;;IA3ClDhC,EAFR,CAAAU,cAAA,aAA4C,aACZ,aACK;IAEzBV,EADA,CAAAiC,UAAA,IAAAC,gCAAA,iBAAqF,IAAAC,yCAAA,0BACzC;IAChDnC,EAAA,CAAAkB,YAAA,EAAM;IAEFlB,EADJ,CAAAU,cAAA,aAA0B,cACI;IAAAV,EAAA,CAAAS,MAAA,GAAe;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAC/ClB,EAAA,CAAAU,cAAA,cAA8B;IAAAV,EAAA,CAAAS,MAAA,GAAwB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAGxDlB,EADJ,CAAAU,cAAA,eAA2B,eACE;IAAAV,EAAA,CAAAS,MAAA,IAAsB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IACrDlB,EAAA,CAAAU,cAAA,eAA2B;IAAAV,EAAA,CAAAS,MAAA,IAAgB;IAGvDT,EAHuD,CAAAkB,YAAA,EAAM,EAC/C,EACJ,EACJ;IACNlB,EAAA,CAAAU,cAAA,eAAmC;IAE/BV,EADA,CAAAiC,UAAA,KAAAG,iCAAA,kBAA8F,KAAAC,oCAAA,qBACsB;IAExHrC,EAAA,CAAAkB,YAAA,EAAM;IAGFlB,EADJ,CAAAU,cAAA,eAAqF,gBAClD;IAAzBV,EAAA,CAAAW,UAAA,sBAAA2B,qDAAA;MAAAtC,EAAA,CAAAa,aAAA,CAAA0B,GAAA;MAAA,MAAApC,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAYb,MAAA,CAAAqC,SAAA,EAAW;IAAA,EAAC;IAC1BxC,EAAA,CAAAU,cAAA,iBAAyH;IAAxBV,EAAA,CAAAyC,gBAAA,2BAAAC,2DAAAC,MAAA;MAAA3C,EAAA,CAAAa,aAAA,CAAA0B,GAAA;MAAA,MAAApC,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA4C,kBAAA,CAAAzC,MAAA,CAAA0C,SAAA,EAAAF,MAAA,MAAAxC,MAAA,CAAA0C,SAAA,GAAAF,MAAA;MAAA,OAAA3C,EAAA,CAAAgB,WAAA,CAAA2B,MAAA;IAAA,EAAuB;IAAxH3C,EAAA,CAAAkB,YAAA,EAAyH;IAErHlB,EADJ,CAAAU,cAAA,eAA8B,kBACyC;IAA1BV,EAAA,CAAAW,UAAA,mBAAAmC,oDAAA;MAAA9C,EAAA,CAAAa,aAAA,CAAA0B,GAAA;MAAA,MAAApC,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,cAAM;IAAAT,EAAA,CAAAkB,YAAA,EAAS;IAClFlB,EAAA,CAAAU,cAAA,kBAAuC;IAAAV,EAAA,CAAAS,MAAA,YAAI;IAGvDT,EAHuD,CAAAkB,YAAA,EAAS,EAClD,EACH,EACL;IAGFlB,EADJ,CAAAU,cAAA,eAAwB,UAChB;IAAAV,EAAA,CAAAS,MAAA,mBAAW;IAAAT,EAAA,CAAAkB,YAAA,EAAK;IAEpBlB,EAAA,CAAAiC,UAAA,KAAAc,iCAAA,mBAA+D;IAe3D/C,EADJ,CAAAU,cAAA,eAA6B,eACI;IAAAV,EAAA,CAAAS,MAAA,IAA2B;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAC9DlB,EAAA,CAAAU,cAAA,eAAqC;IACjCV,EAAA,CAAAS,MAAA,yDACJ;IACJT,EADI,CAAAkB,YAAA,EAAM,EACJ;IAIElB,EAFR,CAAAU,cAAA,eAA8B,aACmC,gBACxB;IAAAV,EAAA,CAAAS,MAAA,oBAAE;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IAAClB,EAAA,CAAAS,MAAA,qBAC/C;IAAAT,EAAA,CAAAkB,YAAA,EAAI;IACJlB,EAAA,CAAAU,cAAA,aAAgE;IAAAV,EAAA,CAAAS,MAAA,6BAAW;IAGvFT,EAHuF,CAAAkB,YAAA,EAAI,EAC7E,EACJ,EACJ;;;;IA5DYlB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,CAA0B;IACjBL,EAAA,CAAAmB,SAAA,EAA2B;IAA3BnB,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,CAA2B;IAGhBL,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAAsB,IAAA,CAAe;IACX1B,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAA8B,kBAAA,MAAA3B,MAAA,CAAAC,IAAA,CAAAG,QAAA,KAAwB;IAGzBP,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAA8B,kBAAA,WAAA3B,MAAA,CAAAC,IAAA,CAAA4C,KAAA,KAAsB;IACpBhD,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAA6C,KAAA,CAAgB;IAKzBjD,EAAA,CAAAmB,SAAA,GAAwC;IAAxCnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAAiB,GAAA,IAAAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAA6B,IAAA,UAAwC;IACzDlD,EAAA,CAAAmB,SAAA,EAAyC;IAAzCnB,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAiB,GAAA,IAAAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAA6B,IAAA,UAAyC;IAIlBlD,EAAA,CAAAmB,SAAA,EAAgD;IAAhDnB,EAAA,CAAA2B,WAAA,YAAAxB,MAAA,CAAAgD,WAAA,oBAAgD;IAEqBnD,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAoD,gBAAA,YAAAjD,MAAA,CAAA0C,SAAA,CAAuB;IAW5E7C,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAkD,UAAA,CAAa;IAe5BrD,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA8B,kBAAA,iBAAA3B,MAAA,CAAAmD,SAAA,KAA2B;IAOrDtD,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAuD,eAAA,KAAAC,GAAA,EAAArD,MAAA,CAAAC,IAAA,CAAAqD,EAAA,EAAmC;IAGnCzD,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAA0D,eAAA,KAAAC,GAAA,EAAoC;;;ADvC3D,OAAM,MAAOC,WAAW;EAuBtBC,YAAA;IAtBA;IACA,KAAAzD,IAAI,GAAgB,IAAI;IACxB,KAAA0D,gBAAgB,GAAwB,IAAI;IAE5C;IACA,KAAAT,UAAU,GAAsB,EAAE;IAClC,KAAAC,SAAS,GAAG,CAAC;IAEb;IACA,KAAAH,WAAW,GAAG,KAAK;IACnB,KAAAN,SAAS,GAAG,EAAE;IAEd;IACA,KAAAkB,WAAW,GAA2C,EAAE;IACxD,KAAAC,iBAAiB,GAAG,KAAK;IAEjB,KAAAC,eAAe,GAAGjF,MAAM,CAACa,eAAe,CAAC;IACzC,KAAAqE,WAAW,GAAGlF,MAAM,CAACM,WAAW,CAAC;IACjC,KAAA6E,WAAW,GAAGnF,MAAM,CAACO,WAAW,CAAC;IACjC,KAAA6E,SAAS,GAAGpF,MAAM,CAACc,SAAS,CAAC;IAC7B,KAAAuE,YAAY,GAAGrF,MAAM,CAACC,eAAe,CAAC;EAE/B;EAEfqF,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,gBAAgB,GAAG,IAAI,CAACI,WAAW,CAACK,mBAAmB,CAACC,SAAS,CAACC,WAAW,IAAG;MAEnF,IAAIA,WAAW,EAAE;QAAA,IAAAC,WAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,kBAAA;QACf;QACA,MAAM1E,IAAI,GAAGqE,WAA8B;QAE3C;QAEA;QACA,IAAI,CAACrE,IAAI,GAAG;UACV,GAAGA,IAAI;UACP4C,KAAK,GAAA0B,WAAA,GAAEtE,IAAI,CAAC4C,KAAK,cAAA0B,WAAA,cAAAA,WAAA,GAAI,CAAC;UAAG;UACzBK,WAAW,GAAAJ,iBAAA,GAAEvE,IAAI,CAAC2E,WAAW,cAAAJ,iBAAA,cAAAA,iBAAA,GAAI,CAAC;UAClCK,QAAQ,GAAAJ,cAAA,GAAExE,IAAI,CAAC4E,QAAQ,cAAAJ,cAAA,cAAAA,cAAA,GAAI,CAAC;UAC5BK,SAAS,GAAAJ,eAAA,GAAEzE,IAAI,CAAC6E,SAAS,cAAAJ,eAAA,cAAAA,eAAA,GAAI,CAAC;UAC9BK,YAAY,GAAAJ,kBAAA,GAAE1E,IAAI,CAAC8E,YAAY,cAAAJ,kBAAA,cAAAA,kBAAA,GAAI,CAAC;UACpCzD,GAAG,EAAEjB,IAAI,CAACiB,GAAG,IAAI,EAAE;UACnB4B,KAAK,EAAE7C,IAAI,CAAC6C,KAAK,IAAI;SACtB;QAED,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACzC,IAAI,CAACiB,GAAG,IAAI,EAAE;QAIpC,IAAI,CAAC8D,mBAAmB,EAAE;QAC1B,IAAI,CAACC,eAAe,EAAE;QAEtB;QACAC,UAAU,CAAC,MAAK;UAAA,IAAAC,UAAA;UACd,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;UAC1D,IAAIF,UAAU,KAAK,GAAAD,UAAA,GAAC,IAAI,CAAClF,IAAI,cAAAkF,UAAA,eAATA,UAAA,CAAWjE,GAAG,KAAI,IAAI,CAACjB,IAAI,CAACiB,GAAG,CAAC6B,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;YAClEqC,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;UACnC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;QAEvD;QACA,IAAI,CAAC5B,eAAe,CAAC6B,YAAY,CAACC,IAAI,CACpCtG,SAAS,CAACuG,QAAQ,IAAG;UACnB,IAAI,CAACA,QAAQ,EAAE;YACb,OAAOxG,EAAE,CAAC,IAAI,CAAC;UACjB;UAGA;UACA,OAAO,IAAI,CAAC0E,WAAW,CAAC+B,gBAAgB,CAACD,QAAQ,CAAC;QACpD,CAAC,CAAC,CACH,CAACxB,SAAS,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA0B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACqC,WAAW,EAAE;IACrC;EACF;EAEAC,gBAAgBA,CAAA;IAEd;IACA,IAAI,IAAI,CAAC/B,YAAY,CAACgC,SAAS,EAAE,EAAE;MAEjC;MACA,IAAI,CAACnC,WAAW,CAACoC,yBAAyB,EAAE,CAACC,IAAI,CAAC,MAAK,CACvD,CAAC,CAAC,CAACC,KAAK,CAACX,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,MAAM,CACP;EACF;EAEMV,mBAAmBA,CAAA;IAAA,IAAAsB,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI,CAACD,KAAI,CAACrG,IAAI,EAAE;QACdwF,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;MAGA;MACA,IAAI,OAAOY,KAAI,CAACrG,IAAI,CAAC4C,KAAK,KAAK,QAAQ,EAAE;QACvC4C,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEY,KAAI,CAACrG,IAAI,CAAC4C,KAAK,CAAC;QAC7DyD,KAAI,CAACrG,IAAI,CAAC4C,KAAK,GAAG,CAAC,CAAC,CAAC;MACvB;MAEA;MACAyD,KAAI,CAACrC,SAAS,CAACe,mBAAmB,CAACsB,KAAI,CAACrG,IAAI,EAAEL,UAAU,CAAC4G,IAAI,CAAC,CAACnC,SAAS,CAACoC,MAAM,IAAG;QAChF,IAAIA,MAAM,EAAE;UACVH,KAAI,CAACpD,UAAU,GAAGuD,MAAM,CAACvD,UAAU;UACnCoD,KAAI,CAACnD,SAAS,GAAGsD,MAAM,CAACC,UAAU;QACpC;MACF,CAAC,CAAC;IAAC;EACL;EAEA5F,aAAaA,CAAA;IACX,IAAI,CAACkC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC;IACA,IAAI,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC/C,IAAI,EAAE;MACjC,IAAI,CAACyC,SAAS,GAAG,IAAI,CAACzC,IAAI,CAACiB,GAAG,IAAI,EAAE;IACtC;IAEA;IACA,IAAI,CAAC,IAAI,CAAC8B,WAAW,EAAE;MACrBkC,UAAU,CAAC,MAAK;QAAA,IAAAyB,WAAA,EAAAC,WAAA;QACd,MAAMxB,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC1D,IAAIF,UAAU,KAAK,GAAAuB,WAAA,GAAC,IAAI,CAAC1G,IAAI,cAAA0G,WAAA,eAATA,WAAA,CAAWzF,GAAG,KAAI,EAAA0F,WAAA,OAAI,CAAC3G,IAAI,cAAA2G,WAAA,uBAATA,WAAA,CAAW1F,GAAG,CAAC6B,IAAI,EAAE,MAAK,EAAE,CAAC,EAAE;UACnEqC,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;QACnC;MACF,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAnD,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACpC,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACqD,EAAE,EAAE;IAEjC;IACA,MAAMpC,GAAG,GAAG,IAAI,CAACwB,SAAS,CAACK,IAAI,EAAE;IACjC,IAAI7B,GAAG,CAAC2F,MAAM,GAAG,GAAG,EAAE;MACpB;MACA;IACF;IAEA;IACA,IAAI,CAAC9C,WAAW,CAAC+C,aAAa,CAAC,IAAI,CAAC7G,IAAI,CAACqD,EAAE,EAAEpC,GAAG,CAAC,CAACkF,IAAI,CAAC,MAAK;MAC1D,IAAI,IAAI,CAACnG,IAAI,EAAE;QACb;QACA,IAAI,CAACA,IAAI,CAACiB,GAAG,GAAGA,GAAG,KAAK,EAAE,GAAG,EAAE,GAAGA,GAAG;MACvC;MACA,IAAI,CAAC8B,WAAW,GAAG,KAAK;MAExB;MACAkC,UAAU,CAAC,MAAK;QAAA,IAAA6B,WAAA;QACd,MAAM3B,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC1D,IAAIF,UAAU,KAAK,GAAA2B,WAAA,GAAC,IAAI,CAAC9G,IAAI,cAAA8G,WAAA,eAATA,WAAA,CAAW7F,GAAG,KAAI,IAAI,CAACjB,IAAI,CAACiB,GAAG,CAAC6B,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;UAClEqC,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;QACnC;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC;EACJ;EAEAP,eAAeA,CAAA;IAAA,IAAA+B,WAAA;IACb,IAAI,GAAAA,WAAA,GAAC,IAAI,CAAC/G,IAAI,cAAA+G,WAAA,eAATA,WAAA,CAAW1D,EAAE,GAAE;IAEpB,IAAI,CAACU,WAAW,CAACiD,cAAc,CAAC,IAAI,CAAChH,IAAI,CAACqD,EAAE,CAAC,CAACsC,IAAI,CAChDtG,SAAS,CAAC4H,KAAK,IAAG;MAChB,IAAIA,KAAK,CAACL,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOxH,EAAE,CAAC,EAAE,CAAC;MACf;MAEA;MACA,MAAM8H,eAAe,GAAGD,KAAK,CAAC1H,GAAG,CAAC4H,IAAI,IACpC,IAAI,CAACpD,WAAW,CAACqD,aAAa,CAACD,IAAI,CAAC9D,EAAG,CAAC,CAACsC,IAAI,CAC3CpG,GAAG,CAAC8H,UAAU,KAAK;QACjB,GAAGF,IAAI;QACPE;OACD,CAAC,CAAC,CACJ,CACF;MAED,OAAO/H,aAAa,CAAC4H,eAAe,CAAC;IACvC,CAAC,CAAC,CACH,CAAC9C,SAAS,CAAC;MACVkD,IAAI,EAAEC,mBAAmB,IAAG;QAC1B,IAAI,CAAC5D,WAAW,GAAG4D,mBAAmB;MACxC,CAAC;MACD9B,KAAK,EAAEA,KAAK,IAAG;QACbD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEA,IAAI+B,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC7D,WAAW,CAAC8D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACrC;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAAC9D,iBAAiB,GAAG,IAAI;EAC/B;EAEA+D,kBAAkBA,CAAA;IAChB,IAAI,CAAC/D,iBAAiB,GAAG,KAAK;EAChC;;eAlNWJ,WAAW;;mCAAXA,YAAW;AAAA;;QAAXA,YAAW;EAAAoE,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MC5BZtI,EAFR,CAAAU,cAAA,aAAuB,aACX,aACc;MACdV,EAAA,CAAAC,SAAA,aAA6D;MAC7DD,EAAA,CAAAU,cAAA,WAAM;MAAAV,EAAA,CAAAS,MAAA,cAAO;MACjBT,EADiB,CAAAkB,YAAA,EAAO,EAClB;MACNlB,EAAA,CAAAU,cAAA,SAAI;MAAAV,EAAA,CAAAS,MAAA,cAAO;MACfT,EADe,CAAAkB,YAAA,EAAK,EACX;MAETlB,EAAA,CAAAiC,UAAA,IAAAuG,0BAAA,mBAA4C;MAgEhDxI,EAAA,CAAAkB,YAAA,EAAM;MAIVlB,EAAA,CAAAC,SAAA,qBAAiC;;;MApEOD,EAAA,CAAAmB,SAAA,GAAU;MAAVnB,EAAA,CAAAE,UAAA,SAAAqI,GAAA,CAAAnI,IAAA,CAAU;;;iBDmBtChB,WAAW,EAAAqJ,EAAA,CAAAC,0BAAA,EAAExJ,YAAY,EAAAyJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE1J,WAAW,EAAA2J,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAN,EAAA,CAAAO,MAAA,EAAEhK,YAAY,EAAAiK,EAAA,CAAAC,UAAA,EAAE3J,mBAAmB;EAAA4J,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}