{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _TimeTrackerUnifiedService;\nimport { inject } from '@angular/core';\nimport { from, of, catchError, map, switchMap } from 'rxjs';\nimport { SupabaseService } from './supabase.service';\nimport * as i0 from \"@angular/core\";\nexport class TimeTrackerUnifiedService {\n  constructor() {\n    this.supabaseService = inject(SupabaseService);\n  }\n  /**\n   * Get all activity types\n   */\n  getActivityTypes() {\n    return from(this.supabaseService.getClient().from('activity_types').select('*').eq('is_active', true).order('order', {\n      ascending: true\n    })).pipe(map(response => {\n      if (response.error) {\n        console.error('TimeTrackerService: Error getting activity types:', response.error);\n        return [];\n      }\n      return response.data;\n    }), catchError(error => {\n      console.error('TimeTrackerService: Error getting activity types:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Get or create a day tracking entry for a user and date\n   */\n  getDayTracking(userId, date) {\n    if (!userId || !date) {\n      console.error('TimeTrackerService: Missing userId or date');\n      return of({\n        id: '',\n        user_id: userId,\n        date: date\n      });\n    }\n    return from(this.supabaseService.getClient().from('day_tracking').select('*').eq('user_id', userId).eq('date', date).single()).pipe(switchMap(response => {\n      if (response.error && response.error.code === 'PGRST116') {\n        // Not found error\n        return from(this.supabaseService.getClient().from('day_tracking').insert({\n          user_id: userId,\n          date: date\n        }).select().single()).pipe(map(insertResponse => {\n          if (insertResponse.error) {\n            console.error('TimeTrackerService: Error creating day tracking:', insertResponse.error);\n            return {\n              id: '',\n              user_id: userId,\n              date: date\n            };\n          }\n          return insertResponse.data;\n        }), catchError(error => {\n          console.error('TimeTrackerService: Error creating day tracking:', error);\n          return of({\n            id: '',\n            user_id: userId,\n            date: date\n          });\n        }));\n      }\n      if (response.error) {\n        console.error('TimeTrackerService: Error getting day tracking:', response.error);\n        return of({\n          id: '',\n          user_id: userId,\n          date: date\n        });\n      }\n      return of(response.data);\n    }), catchError(error => {\n      console.error('TimeTrackerService: Error in getDayTracking:', error);\n      return of({\n        id: '',\n        user_id: userId,\n        date: date\n      });\n    }));\n  }\n  /**\n   * Get activities for a day tracking entry\n   */\n  getActivitiesForDayTracking(dayTrackingId) {\n    if (!dayTrackingId) {\n      console.error('TimeTrackerService: Missing dayTrackingId');\n      return of([]);\n    }\n    return from(this.supabaseService.getClient().from('activities').select('*').eq('day_tracking_id', dayTrackingId).order('name')).pipe(map(response => {\n      if (response.error) {\n        console.error('TimeTrackerService: Error getting activities:', response.error);\n        return [];\n      }\n      return response.data;\n    }), catchError(error => {\n      console.error('TimeTrackerService: Error getting activities:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Get activities for a user and date\n   */\n  getActivities(userId, date) {\n    if (!userId || !date) {\n      console.error('TimeTrackerService: Missing userId or date');\n      return of([]);\n    }\n    return this.getDayTracking(userId, date).pipe(switchMap(dayTracking => {\n      if (!dayTracking.id) {\n        return of([]);\n      }\n      return this.getActivitiesForDayTracking(dayTracking.id);\n    }));\n  }\n  /**\n   * Get activities for a user within a date range\n   */\n  getActivitiesForDateRange(userId, startDate, endDate) {\n    if (!userId || !startDate || !endDate) {\n      console.error('TimeTrackerService: Missing userId, startDate or endDate');\n      return of([]);\n    }\n    return from(this.supabaseService.getClient().from('day_tracking').select(`\n          id,\n          date,\n          activities (\n            id,\n            day_tracking_id,\n            name,\n            emoji,\n            hours,\n            minutes,\n            is_custom\n          )\n        `).eq('user_id', userId).gte('date', startDate).lte('date', endDate)).pipe(map(response => {\n      if (response.error) {\n        console.error('TimeTrackerService: Error getting activities for date range:', response.error);\n        return [];\n      }\n      // Flatten activities from all days\n      const allActivities = [];\n      response.data.forEach(dayTracking => {\n        if (dayTracking.activities) {\n          allActivities.push(...dayTracking.activities);\n        }\n      });\n      return allActivities;\n    }), catchError(error => {\n      console.error('TimeTrackerService: Error getting activities for date range:', error);\n      return of([]);\n    }));\n  }\n  /**\n   * Create a new activity\n   */\n  createActivity(userId, date, name, emoji, hours, minutes, isCustom) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // First, get or create day tracking\n        const {\n          data: dayTracking,\n          error: dayTrackingError\n        } = yield _this.supabaseService.getClient().from('day_tracking').select('id').eq('user_id', userId).eq('date', date).single();\n        let dayTrackingId;\n        if (dayTrackingError && dayTrackingError.code === 'PGRST116') {\n          // Not found error\n          // Create new day tracking\n          const {\n            data: newDayTracking,\n            error: newDayTrackingError\n          } = yield _this.supabaseService.getClient().from('day_tracking').insert({\n            user_id: userId,\n            date: date\n          }).select('id').single();\n          if (newDayTrackingError) {\n            console.error('TimeTrackerService: Error creating day tracking:', newDayTrackingError);\n            return Promise.reject(newDayTrackingError);\n          }\n          dayTrackingId = newDayTracking.id;\n        } else if (dayTrackingError) {\n          console.error('TimeTrackerService: Error getting day tracking:', dayTrackingError);\n          return Promise.reject(dayTrackingError);\n        } else {\n          dayTrackingId = dayTracking.id;\n        }\n        // Now create the activity\n        const {\n          data: newActivity,\n          error: activityError\n        } = yield _this.supabaseService.getClient().from('activities').insert({\n          day_tracking_id: dayTrackingId,\n          name: name,\n          emoji: emoji,\n          hours: hours,\n          minutes: minutes,\n          is_custom: isCustom\n        }).select('id').single();\n        if (activityError) {\n          console.error('TimeTrackerService: Error creating activity:', activityError);\n          return Promise.reject(activityError);\n        }\n        // Calculate total time\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        // We don't need to update day_tracking with totals anymore\n        // Totals are calculated on the fly from activities\n        return {\n          id: newActivity.id,\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        console.error('TimeTrackerService: Error creating activity:', error);\n        return Promise.reject(error);\n      }\n    })();\n  }\n  /**\n   * Update an activity\n   */\n  updateActivity(activityId, hours, minutes) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // First get the activity to get the day_tracking_id\n        const {\n          data: activity,\n          error: activityError\n        } = yield _this2.supabaseService.getClient().from('activities').select('day_tracking_id').eq('id', activityId).single();\n        if (activityError) {\n          console.error('TimeTrackerService: Error getting activity:', activityError);\n          return Promise.reject(activityError);\n        }\n        const dayTrackingId = activity.day_tracking_id;\n        // Update the activity\n        const {\n          error: updateError\n        } = yield _this2.supabaseService.getClient().from('activities').update({\n          hours,\n          minutes\n        }).eq('id', activityId);\n        if (updateError) {\n          console.error('TimeTrackerService: Error updating activity:', updateError);\n          return Promise.reject(updateError);\n        }\n        // Calculate total time\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this2.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        // We don't need to update day_tracking with totals anymore\n        // Totals are calculated on the fly from activities\n        return {\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        console.error('TimeTrackerService: Error updating activity:', error);\n        return Promise.reject(error);\n      }\n    })();\n  }\n  /**\n   * Delete an activity\n   */\n  deleteActivity(activityId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // First get the activity to get the day_tracking_id\n        const {\n          data: activity,\n          error: activityError\n        } = yield _this3.supabaseService.getClient().from('activities').select('day_tracking_id').eq('id', activityId).single();\n        if (activityError) {\n          console.error('TimeTrackerService: Error getting activity:', activityError);\n          return Promise.reject(activityError);\n        }\n        const dayTrackingId = activity.day_tracking_id;\n        // Delete the activity\n        const {\n          error: deleteError\n        } = yield _this3.supabaseService.getClient().from('activities').delete().eq('id', activityId);\n        if (deleteError) {\n          console.error('TimeTrackerService: Error deleting activity:', deleteError);\n          return Promise.reject(deleteError);\n        }\n        // Calculate total time\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this3.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        // We don't need to update day_tracking with totals anymore\n        // Totals are calculated on the fly from activities\n        return {\n          total_hours: totalHours.toFixed(1),\n          remaining_hours: remainingHours.toFixed(1)\n        };\n      } catch (error) {\n        console.error('TimeTrackerService: Error deleting activity:', error);\n        return Promise.reject(error);\n      }\n    })();\n  }\n  /**\n   * Get total time for a day tracking entry\n   */\n  getTotalTime(dayTrackingId) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data: activities,\n          error: activitiesError\n        } = yield _this4.supabaseService.getClient().from('activities').select('hours, minutes').eq('day_tracking_id', dayTrackingId);\n        if (activitiesError) {\n          console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n          return Promise.reject(activitiesError);\n        }\n        const totalMinutes = activities.reduce((total, act) => {\n          return total + act.hours * 60 + act.minutes;\n        }, 0);\n        const totalHours = totalMinutes / 60;\n        const remainingHours = Math.max(0, 24 - totalHours);\n        return {\n          total_hours: totalHours,\n          remaining_hours: remainingHours\n        };\n      } catch (error) {\n        console.error('TimeTrackerService: Error getting total time:', error);\n        return Promise.reject(error);\n      }\n    })();\n  }\n  /**\n   * Create a new activity type\n   */\n  createActivityType(activityType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          data,\n          error\n        } = yield _this5.supabaseService.getClient().from('activity_types').insert(activityType).select('id').single();\n        if (error) {\n          console.error('TimeTrackerService: Error creating activity type:', error);\n          return Promise.reject(error);\n        }\n        return data.id;\n      } catch (error) {\n        console.error('TimeTrackerService: Error creating activity type:', error);\n        return Promise.reject(error);\n      }\n    })();\n  }\n}\n_TimeTrackerUnifiedService = TimeTrackerUnifiedService;\n_TimeTrackerUnifiedService.ɵfac = function TimeTrackerUnifiedService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TimeTrackerUnifiedService)();\n};\n_TimeTrackerUnifiedService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _TimeTrackerUnifiedService,\n  factory: _TimeTrackerUnifiedService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["inject", "from", "of", "catchError", "map", "switchMap", "SupabaseService", "TimeTrackerUnifiedService", "constructor", "supabaseService", "getActivityTypes", "getClient", "select", "eq", "order", "ascending", "pipe", "response", "error", "console", "data", "getDayTracking", "userId", "date", "id", "user_id", "single", "code", "insert", "insertResponse", "getActivitiesForDayTracking", "dayTrackingId", "getActivities", "dayTracking", "getActivitiesForDateRange", "startDate", "endDate", "gte", "lte", "allActivities", "for<PERSON>ach", "activities", "push", "createActivity", "name", "emoji", "hours", "minutes", "isCustom", "_this", "_asyncToGenerator", "dayTrackingError", "newDayTracking", "newDayTrackingError", "Promise", "reject", "newActivity", "activityError", "day_tracking_id", "is_custom", "activitiesError", "totalMinutes", "reduce", "total", "act", "totalHours", "remainingHours", "Math", "max", "total_hours", "toFixed", "remaining_hours", "updateActivity", "activityId", "_this2", "activity", "updateError", "update", "deleteActivity", "_this3", "deleteError", "delete", "getTotalTime", "_this4", "createActivityType", "activityType", "_this5", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\services\\time-tracker-unified.service.ts"], "sourcesContent": ["import { Injectable, inject } from '@angular/core';\nimport { Activity, ActivityType, DayTracking } from '../models/activity.model';\nimport { Observable, from, of, catchError, map, switchMap } from 'rxjs';\nimport { SupabaseService } from './supabase.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TimeTrackerUnifiedService {\n  private supabaseService = inject(SupabaseService);\n\n  /**\n   * Get all activity types\n   */\n  getActivityTypes(): Observable<ActivityType[]> {\n\n    return from(\n      this.supabaseService.getClient()\n        .from('activity_types')\n        .select('*')\n        .eq('is_active', true)\n        .order('order', { ascending: true })\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          console.error('TimeTrackerService: Error getting activity types:', response.error);\n          return [];\n        }\n\n        return response.data as ActivityType[];\n      }),\n      catchError(error => {\n        console.error('TimeTrackerService: Error getting activity types:', error);\n        return of([]);\n      })\n    );\n  }\n\n  /**\n   * Get or create a day tracking entry for a user and date\n   */\n  getDayTracking(userId: string, date: string): Observable<DayTracking> {\n\n    if (!userId || !date) {\n      console.error('TimeTrackerService: Missing userId or date');\n      return of({ id: '', user_id: userId, date: date });\n    }\n\n    return from(\n      this.supabaseService.getClient()\n        .from('day_tracking')\n        .select('*')\n        .eq('user_id', userId)\n        .eq('date', date)\n        .single()\n    ).pipe(\n      switchMap(response => {\n        if (response.error && response.error.code === 'PGRST116') { // Not found error\n          return from(\n            this.supabaseService.getClient()\n              .from('day_tracking')\n              .insert({ user_id: userId, date: date })\n              .select()\n              .single()\n          ).pipe(\n            map(insertResponse => {\n              if (insertResponse.error) {\n                console.error('TimeTrackerService: Error creating day tracking:', insertResponse.error);\n                return { id: '', user_id: userId, date: date };\n              }\n\n              return insertResponse.data as DayTracking;\n            }),\n            catchError(error => {\n              console.error('TimeTrackerService: Error creating day tracking:', error);\n              return of({ id: '', user_id: userId, date: date });\n            })\n          );\n        }\n\n        if (response.error) {\n          console.error('TimeTrackerService: Error getting day tracking:', response.error);\n          return of({ id: '', user_id: userId, date: date });\n        }\n\n        return of(response.data as DayTracking);\n      }),\n      catchError(error => {\n        console.error('TimeTrackerService: Error in getDayTracking:', error);\n        return of({ id: '', user_id: userId, date: date });\n      })\n    );\n  }\n\n  /**\n   * Get activities for a day tracking entry\n   */\n  getActivitiesForDayTracking(dayTrackingId: string): Observable<Activity[]> {\n\n    if (!dayTrackingId) {\n      console.error('TimeTrackerService: Missing dayTrackingId');\n      return of([]);\n    }\n\n    return from(\n      this.supabaseService.getClient()\n        .from('activities')\n        .select('*')\n        .eq('day_tracking_id', dayTrackingId)\n        .order('name')\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          console.error('TimeTrackerService: Error getting activities:', response.error);\n          return [];\n        }\n\n        return response.data as Activity[];\n      }),\n      catchError(error => {\n        console.error('TimeTrackerService: Error getting activities:', error);\n        return of([]);\n      })\n    );\n  }\n\n  /**\n   * Get activities for a user and date\n   */\n  getActivities(userId: string, date: string): Observable<Activity[]> {\n\n    if (!userId || !date) {\n      console.error('TimeTrackerService: Missing userId or date');\n      return of([]);\n    }\n\n    return this.getDayTracking(userId, date).pipe(\n      switchMap(dayTracking => {\n        if (!dayTracking.id) {\n          return of([]);\n        }\n\n        return this.getActivitiesForDayTracking(dayTracking.id);\n      })\n    );\n  }\n\n  /**\n   * Get activities for a user within a date range\n   */\n  getActivitiesForDateRange(userId: string, startDate: string, endDate: string): Observable<Activity[]> {\n    if (!userId || !startDate || !endDate) {\n      console.error('TimeTrackerService: Missing userId, startDate or endDate');\n      return of([]);\n    }\n\n    return from(\n      this.supabaseService.getClient()\n        .from('day_tracking')\n        .select(`\n          id,\n          date,\n          activities (\n            id,\n            day_tracking_id,\n            name,\n            emoji,\n            hours,\n            minutes,\n            is_custom\n          )\n        `)\n        .eq('user_id', userId)\n        .gte('date', startDate)\n        .lte('date', endDate)\n    ).pipe(\n      map(response => {\n        if (response.error) {\n          console.error('TimeTrackerService: Error getting activities for date range:', response.error);\n          return [];\n        }\n\n        // Flatten activities from all days\n        const allActivities: Activity[] = [];\n        response.data.forEach((dayTracking: any) => {\n          if (dayTracking.activities) {\n            allActivities.push(...dayTracking.activities);\n          }\n        });\n\n        return allActivities;\n      }),\n      catchError(error => {\n        console.error('TimeTrackerService: Error getting activities for date range:', error);\n        return of([]);\n      })\n    );\n  }\n\n  /**\n   * Create a new activity\n   */\n  async createActivity(\n    userId: string,\n    date: string,\n    name: string,\n    emoji: string,\n    hours: number,\n    minutes: number,\n    isCustom: boolean\n  ): Promise<{ id: string, total_hours: string, remaining_hours: string }> {\n\n    try {\n      // First, get or create day tracking\n      const { data: dayTracking, error: dayTrackingError } = await this.supabaseService.getClient()\n        .from('day_tracking')\n        .select('id')\n        .eq('user_id', userId)\n        .eq('date', date)\n        .single();\n\n      let dayTrackingId: string;\n\n      if (dayTrackingError && dayTrackingError.code === 'PGRST116') { // Not found error\n        // Create new day tracking\n        const { data: newDayTracking, error: newDayTrackingError } = await this.supabaseService.getClient()\n          .from('day_tracking')\n          .insert({ user_id: userId, date: date })\n          .select('id')\n          .single();\n\n        if (newDayTrackingError) {\n          console.error('TimeTrackerService: Error creating day tracking:', newDayTrackingError);\n          return Promise.reject(newDayTrackingError);\n        }\n\n        dayTrackingId = newDayTracking.id;\n      } else if (dayTrackingError) {\n        console.error('TimeTrackerService: Error getting day tracking:', dayTrackingError);\n        return Promise.reject(dayTrackingError);\n      } else {\n        dayTrackingId = dayTracking.id;\n      }\n\n      // Now create the activity\n      const { data: newActivity, error: activityError } = await this.supabaseService.getClient()\n        .from('activities')\n        .insert({\n          day_tracking_id: dayTrackingId,\n          name: name,\n          emoji: emoji,\n          hours: hours,\n          minutes: minutes,\n          is_custom: isCustom\n        })\n        .select('id')\n        .single();\n\n      if (activityError) {\n        console.error('TimeTrackerService: Error creating activity:', activityError);\n        return Promise.reject(activityError);\n      }\n\n\n      // Calculate total time\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('hours, minutes')\n        .eq('day_tracking_id', dayTrackingId);\n\n      if (activitiesError) {\n        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n        return Promise.reject(activitiesError);\n      }\n\n      const totalMinutes = activities.reduce((total, act) => {\n        return total + (act.hours * 60) + act.minutes;\n      }, 0);\n\n      const totalHours = totalMinutes / 60;\n      const remainingHours = Math.max(0, 24 - totalHours);\n\n      // We don't need to update day_tracking with totals anymore\n      // Totals are calculated on the fly from activities\n\n      return {\n        id: newActivity.id,\n        total_hours: totalHours.toFixed(1),\n        remaining_hours: remainingHours.toFixed(1)\n      };\n    } catch (error) {\n      console.error('TimeTrackerService: Error creating activity:', error);\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Update an activity\n   */\n  async updateActivity(activityId: string, hours: number, minutes: number): Promise<{ total_hours: string, remaining_hours: string }> {\n\n    try {\n      // First get the activity to get the day_tracking_id\n      const { data: activity, error: activityError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('day_tracking_id')\n        .eq('id', activityId)\n        .single();\n\n      if (activityError) {\n        console.error('TimeTrackerService: Error getting activity:', activityError);\n        return Promise.reject(activityError);\n      }\n\n      const dayTrackingId = activity.day_tracking_id;\n\n      // Update the activity\n      const { error: updateError } = await this.supabaseService.getClient()\n        .from('activities')\n        .update({ hours, minutes })\n        .eq('id', activityId);\n\n      if (updateError) {\n        console.error('TimeTrackerService: Error updating activity:', updateError);\n        return Promise.reject(updateError);\n      }\n\n\n      // Calculate total time\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('hours, minutes')\n        .eq('day_tracking_id', dayTrackingId);\n\n      if (activitiesError) {\n        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n        return Promise.reject(activitiesError);\n      }\n\n      const totalMinutes = activities.reduce((total, act) => {\n        return total + (act.hours * 60) + act.minutes;\n      }, 0);\n\n      const totalHours = totalMinutes / 60;\n      const remainingHours = Math.max(0, 24 - totalHours);\n\n      // We don't need to update day_tracking with totals anymore\n      // Totals are calculated on the fly from activities\n\n      return {\n        total_hours: totalHours.toFixed(1),\n        remaining_hours: remainingHours.toFixed(1)\n      };\n    } catch (error) {\n      console.error('TimeTrackerService: Error updating activity:', error);\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Delete an activity\n   */\n  async deleteActivity(activityId: string): Promise<{ total_hours: string, remaining_hours: string }> {\n\n    try {\n      // First get the activity to get the day_tracking_id\n      const { data: activity, error: activityError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('day_tracking_id')\n        .eq('id', activityId)\n        .single();\n\n      if (activityError) {\n        console.error('TimeTrackerService: Error getting activity:', activityError);\n        return Promise.reject(activityError);\n      }\n\n      const dayTrackingId = activity.day_tracking_id;\n\n      // Delete the activity\n      const { error: deleteError } = await this.supabaseService.getClient()\n        .from('activities')\n        .delete()\n        .eq('id', activityId);\n\n      if (deleteError) {\n        console.error('TimeTrackerService: Error deleting activity:', deleteError);\n        return Promise.reject(deleteError);\n      }\n\n\n      // Calculate total time\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('hours, minutes')\n        .eq('day_tracking_id', dayTrackingId);\n\n      if (activitiesError) {\n        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n        return Promise.reject(activitiesError);\n      }\n\n      const totalMinutes = activities.reduce((total, act) => {\n        return total + (act.hours * 60) + act.minutes;\n      }, 0);\n\n      const totalHours = totalMinutes / 60;\n      const remainingHours = Math.max(0, 24 - totalHours);\n\n      // We don't need to update day_tracking with totals anymore\n      // Totals are calculated on the fly from activities\n\n      return {\n        total_hours: totalHours.toFixed(1),\n        remaining_hours: remainingHours.toFixed(1)\n      };\n    } catch (error) {\n      console.error('TimeTrackerService: Error deleting activity:', error);\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Get total time for a day tracking entry\n   */\n  async getTotalTime(dayTrackingId: string): Promise<{ total_hours: number, remaining_hours: number }> {\n\n    try {\n      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()\n        .from('activities')\n        .select('hours, minutes')\n        .eq('day_tracking_id', dayTrackingId);\n\n      if (activitiesError) {\n        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);\n        return Promise.reject(activitiesError);\n      }\n\n      const totalMinutes = activities.reduce((total, act) => {\n        return total + (act.hours * 60) + act.minutes;\n      }, 0);\n\n      const totalHours = totalMinutes / 60;\n      const remainingHours = Math.max(0, 24 - totalHours);\n\n      return {\n        total_hours: totalHours,\n        remaining_hours: remainingHours\n      };\n    } catch (error) {\n      console.error('TimeTrackerService: Error getting total time:', error);\n      return Promise.reject(error);\n    }\n  }\n\n  /**\n   * Create a new activity type\n   */\n  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {\n\n    try {\n      const { data, error } = await this.supabaseService.getClient()\n        .from('activity_types')\n        .insert(activityType)\n        .select('id')\n        .single();\n\n      if (error) {\n        console.error('TimeTrackerService: Error creating activity type:', error);\n        return Promise.reject(error);\n      }\n\n      return data.id;\n    } catch (error) {\n      console.error('TimeTrackerService: Error creating activity type:', error);\n      return Promise.reject(error);\n    }\n  }\n}\n"], "mappings": ";;AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAElD,SAAqBC,IAAI,EAAEC,EAAE,EAAEC,UAAU,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AACvE,SAASC,eAAe,QAAQ,oBAAoB;;AAKpD,OAAM,MAAOC,yBAAyB;EAHtCC,YAAA;IAIU,KAAAC,eAAe,GAAGT,MAAM,CAACM,eAAe,CAAC;;EAEjD;;;EAGAI,gBAAgBA,CAAA;IAEd,OAAOT,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,gBAAgB,CAAC,CACtBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CACrBC,KAAK,CAAC,OAAO,EAAE;MAAEC,SAAS,EAAE;IAAI,CAAE,CAAC,CACvC,CAACC,IAAI,CACJZ,GAAG,CAACa,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClBC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAED,QAAQ,CAACC,KAAK,CAAC;QAClF,OAAO,EAAE;MACX;MAEA,OAAOD,QAAQ,CAACG,IAAsB;IACxC,CAAC,CAAC,EACFjB,UAAU,CAACe,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAmB,cAAcA,CAACC,MAAc,EAAEC,IAAY;IAEzC,IAAI,CAACD,MAAM,IAAI,CAACC,IAAI,EAAE;MACpBJ,OAAO,CAACD,KAAK,CAAC,4CAA4C,CAAC;MAC3D,OAAOhB,EAAE,CAAC;QAAEsB,EAAE,EAAE,EAAE;QAAEC,OAAO,EAAEH,MAAM;QAAEC,IAAI,EAAEA;MAAI,CAAE,CAAC;IACpD;IAEA,OAAOtB,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,cAAc,CAAC,CACpBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAES,MAAM,CAAC,CACrBT,EAAE,CAAC,MAAM,EAAEU,IAAI,CAAC,CAChBG,MAAM,EAAE,CACZ,CAACV,IAAI,CACJX,SAAS,CAACY,QAAQ,IAAG;MACnB,IAAIA,QAAQ,CAACC,KAAK,IAAID,QAAQ,CAACC,KAAK,CAACS,IAAI,KAAK,UAAU,EAAE;QAAE;QAC1D,OAAO1B,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,cAAc,CAAC,CACpB2B,MAAM,CAAC;UAAEH,OAAO,EAAEH,MAAM;UAAEC,IAAI,EAAEA;QAAI,CAAE,CAAC,CACvCX,MAAM,EAAE,CACRc,MAAM,EAAE,CACZ,CAACV,IAAI,CACJZ,GAAG,CAACyB,cAAc,IAAG;UACnB,IAAIA,cAAc,CAACX,KAAK,EAAE;YACxBC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEW,cAAc,CAACX,KAAK,CAAC;YACvF,OAAO;cAAEM,EAAE,EAAE,EAAE;cAAEC,OAAO,EAAEH,MAAM;cAAEC,IAAI,EAAEA;YAAI,CAAE;UAChD;UAEA,OAAOM,cAAc,CAACT,IAAmB;QAC3C,CAAC,CAAC,EACFjB,UAAU,CAACe,KAAK,IAAG;UACjBC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,OAAOhB,EAAE,CAAC;YAAEsB,EAAE,EAAE,EAAE;YAAEC,OAAO,EAAEH,MAAM;YAAEC,IAAI,EAAEA;UAAI,CAAE,CAAC;QACpD,CAAC,CAAC,CACH;MACH;MAEA,IAAIN,QAAQ,CAACC,KAAK,EAAE;QAClBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAED,QAAQ,CAACC,KAAK,CAAC;QAChF,OAAOhB,EAAE,CAAC;UAAEsB,EAAE,EAAE,EAAE;UAAEC,OAAO,EAAEH,MAAM;UAAEC,IAAI,EAAEA;QAAI,CAAE,CAAC;MACpD;MAEA,OAAOrB,EAAE,CAACe,QAAQ,CAACG,IAAmB,CAAC;IACzC,CAAC,CAAC,EACFjB,UAAU,CAACe,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,OAAOhB,EAAE,CAAC;QAAEsB,EAAE,EAAE,EAAE;QAAEC,OAAO,EAAEH,MAAM;QAAEC,IAAI,EAAEA;MAAI,CAAE,CAAC;IACpD,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAO,2BAA2BA,CAACC,aAAqB;IAE/C,IAAI,CAACA,aAAa,EAAE;MAClBZ,OAAO,CAACD,KAAK,CAAC,2CAA2C,CAAC;MAC1D,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAOD,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,iBAAiB,EAAEkB,aAAa,CAAC,CACpCjB,KAAK,CAAC,MAAM,CAAC,CACjB,CAACE,IAAI,CACJZ,GAAG,CAACa,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAED,QAAQ,CAACC,KAAK,CAAC;QAC9E,OAAO,EAAE;MACX;MAEA,OAAOD,QAAQ,CAACG,IAAkB;IACpC,CAAC,CAAC,EACFjB,UAAU,CAACe,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;;;EAGA8B,aAAaA,CAACV,MAAc,EAAEC,IAAY;IAExC,IAAI,CAACD,MAAM,IAAI,CAACC,IAAI,EAAE;MACpBJ,OAAO,CAACD,KAAK,CAAC,4CAA4C,CAAC;MAC3D,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAO,IAAI,CAACmB,cAAc,CAACC,MAAM,EAAEC,IAAI,CAAC,CAACP,IAAI,CAC3CX,SAAS,CAAC4B,WAAW,IAAG;MACtB,IAAI,CAACA,WAAW,CAACT,EAAE,EAAE;QACnB,OAAOtB,EAAE,CAAC,EAAE,CAAC;MACf;MAEA,OAAO,IAAI,CAAC4B,2BAA2B,CAACG,WAAW,CAACT,EAAE,CAAC;IACzD,CAAC,CAAC,CACH;EACH;EAEA;;;EAGAU,yBAAyBA,CAACZ,MAAc,EAAEa,SAAiB,EAAEC,OAAe;IAC1E,IAAI,CAACd,MAAM,IAAI,CAACa,SAAS,IAAI,CAACC,OAAO,EAAE;MACrCjB,OAAO,CAACD,KAAK,CAAC,0DAA0D,CAAC;MACzE,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf;IAEA,OAAOD,IAAI,CACT,IAAI,CAACQ,eAAe,CAACE,SAAS,EAAE,CAC7BV,IAAI,CAAC,cAAc,CAAC,CACpBW,MAAM,CAAC;;;;;;;;;;;;SAYP,CAAC,CACDC,EAAE,CAAC,SAAS,EAAES,MAAM,CAAC,CACrBe,GAAG,CAAC,MAAM,EAAEF,SAAS,CAAC,CACtBG,GAAG,CAAC,MAAM,EAAEF,OAAO,CAAC,CACxB,CAACpB,IAAI,CACJZ,GAAG,CAACa,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClBC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAED,QAAQ,CAACC,KAAK,CAAC;QAC7F,OAAO,EAAE;MACX;MAEA;MACA,MAAMqB,aAAa,GAAe,EAAE;MACpCtB,QAAQ,CAACG,IAAI,CAACoB,OAAO,CAAEP,WAAgB,IAAI;QACzC,IAAIA,WAAW,CAACQ,UAAU,EAAE;UAC1BF,aAAa,CAACG,IAAI,CAAC,GAAGT,WAAW,CAACQ,UAAU,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAOF,aAAa;IACtB,CAAC,CAAC,EACFpC,UAAU,CAACe,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,8DAA8D,EAAEA,KAAK,CAAC;MACpF,OAAOhB,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH;EACH;EAEA;;;EAGMyC,cAAcA,CAClBrB,MAAc,EACdC,IAAY,EACZqB,IAAY,EACZC,KAAa,EACbC,KAAa,EACbC,OAAe,EACfC,QAAiB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAGjB,IAAI;QACF;QACA,MAAM;UAAE9B,IAAI,EAAEa,WAAW;UAAEf,KAAK,EAAEiC;QAAgB,CAAE,SAASF,KAAI,CAACxC,eAAe,CAACE,SAAS,EAAE,CAC1FV,IAAI,CAAC,cAAc,CAAC,CACpBW,MAAM,CAAC,IAAI,CAAC,CACZC,EAAE,CAAC,SAAS,EAAES,MAAM,CAAC,CACrBT,EAAE,CAAC,MAAM,EAAEU,IAAI,CAAC,CAChBG,MAAM,EAAE;QAEX,IAAIK,aAAqB;QAEzB,IAAIoB,gBAAgB,IAAIA,gBAAgB,CAACxB,IAAI,KAAK,UAAU,EAAE;UAAE;UAC9D;UACA,MAAM;YAAEP,IAAI,EAAEgC,cAAc;YAAElC,KAAK,EAAEmC;UAAmB,CAAE,SAASJ,KAAI,CAACxC,eAAe,CAACE,SAAS,EAAE,CAChGV,IAAI,CAAC,cAAc,CAAC,CACpB2B,MAAM,CAAC;YAAEH,OAAO,EAAEH,MAAM;YAAEC,IAAI,EAAEA;UAAI,CAAE,CAAC,CACvCX,MAAM,CAAC,IAAI,CAAC,CACZc,MAAM,EAAE;UAEX,IAAI2B,mBAAmB,EAAE;YACvBlC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEmC,mBAAmB,CAAC;YACtF,OAAOC,OAAO,CAACC,MAAM,CAACF,mBAAmB,CAAC;UAC5C;UAEAtB,aAAa,GAAGqB,cAAc,CAAC5B,EAAE;QACnC,CAAC,MAAM,IAAI2B,gBAAgB,EAAE;UAC3BhC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEiC,gBAAgB,CAAC;UAClF,OAAOG,OAAO,CAACC,MAAM,CAACJ,gBAAgB,CAAC;QACzC,CAAC,MAAM;UACLpB,aAAa,GAAGE,WAAW,CAACT,EAAE;QAChC;QAEA;QACA,MAAM;UAAEJ,IAAI,EAAEoC,WAAW;UAAEtC,KAAK,EAAEuC;QAAa,CAAE,SAASR,KAAI,CAACxC,eAAe,CAACE,SAAS,EAAE,CACvFV,IAAI,CAAC,YAAY,CAAC,CAClB2B,MAAM,CAAC;UACN8B,eAAe,EAAE3B,aAAa;UAC9Ba,IAAI,EAAEA,IAAI;UACVC,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBY,SAAS,EAAEX;SACZ,CAAC,CACDpC,MAAM,CAAC,IAAI,CAAC,CACZc,MAAM,EAAE;QAEX,IAAI+B,aAAa,EAAE;UACjBtC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEuC,aAAa,CAAC;UAC5E,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAGA;QACA,MAAM;UAAErC,IAAI,EAAEqB,UAAU;UAAEvB,KAAK,EAAE0C;QAAe,CAAE,SAASX,KAAI,CAACxC,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEkB,aAAa,CAAC;QAEvC,IAAI6B,eAAe,EAAE;UACnBzC,OAAO,CAACD,KAAK,CAAC,qEAAqE,EAAE0C,eAAe,CAAC;UACrG,OAAON,OAAO,CAACC,MAAM,CAACK,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGpB,UAAU,CAACqB,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAAClB,KAAK,GAAG,EAAG,GAAGkB,GAAG,CAACjB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMkB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAEnD;QACA;QAEA,OAAO;UACLzC,EAAE,EAAEgC,WAAW,CAAChC,EAAE;UAClB6C,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAOpD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;MAC9B;IAAC;EACH;EAEA;;;EAGMsD,cAAcA,CAACC,UAAkB,EAAE3B,KAAa,EAAEC,OAAe;IAAA,IAAA2B,MAAA;IAAA,OAAAxB,iBAAA;MAErE,IAAI;QACF;QACA,MAAM;UAAE9B,IAAI,EAAEuD,QAAQ;UAAEzD,KAAK,EAAEuC;QAAa,CAAE,SAASiB,MAAI,CAACjE,eAAe,CAACE,SAAS,EAAE,CACpFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,iBAAiB,CAAC,CACzBC,EAAE,CAAC,IAAI,EAAE4D,UAAU,CAAC,CACpB/C,MAAM,EAAE;QAEX,IAAI+B,aAAa,EAAE;UACjBtC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEuC,aAAa,CAAC;UAC3E,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAEA,MAAM1B,aAAa,GAAG4C,QAAQ,CAACjB,eAAe;QAE9C;QACA,MAAM;UAAExC,KAAK,EAAE0D;QAAW,CAAE,SAASF,MAAI,CAACjE,eAAe,CAACE,SAAS,EAAE,CAClEV,IAAI,CAAC,YAAY,CAAC,CAClB4E,MAAM,CAAC;UAAE/B,KAAK;UAAEC;QAAO,CAAE,CAAC,CAC1BlC,EAAE,CAAC,IAAI,EAAE4D,UAAU,CAAC;QAEvB,IAAIG,WAAW,EAAE;UACfzD,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAE0D,WAAW,CAAC;UAC1E,OAAOtB,OAAO,CAACC,MAAM,CAACqB,WAAW,CAAC;QACpC;QAGA;QACA,MAAM;UAAExD,IAAI,EAAEqB,UAAU;UAAEvB,KAAK,EAAE0C;QAAe,CAAE,SAASc,MAAI,CAACjE,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEkB,aAAa,CAAC;QAEvC,IAAI6B,eAAe,EAAE;UACnBzC,OAAO,CAACD,KAAK,CAAC,qEAAqE,EAAE0C,eAAe,CAAC;UACrG,OAAON,OAAO,CAACC,MAAM,CAACK,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGpB,UAAU,CAACqB,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAAClB,KAAK,GAAG,EAAG,GAAGkB,GAAG,CAACjB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMkB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAEnD;QACA;QAEA,OAAO;UACLI,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAOpD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;MAC9B;IAAC;EACH;EAEA;;;EAGM4D,cAAcA,CAACL,UAAkB;IAAA,IAAAM,MAAA;IAAA,OAAA7B,iBAAA;MAErC,IAAI;QACF;QACA,MAAM;UAAE9B,IAAI,EAAEuD,QAAQ;UAAEzD,KAAK,EAAEuC;QAAa,CAAE,SAASsB,MAAI,CAACtE,eAAe,CAACE,SAAS,EAAE,CACpFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,iBAAiB,CAAC,CACzBC,EAAE,CAAC,IAAI,EAAE4D,UAAU,CAAC,CACpB/C,MAAM,EAAE;QAEX,IAAI+B,aAAa,EAAE;UACjBtC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEuC,aAAa,CAAC;UAC3E,OAAOH,OAAO,CAACC,MAAM,CAACE,aAAa,CAAC;QACtC;QAEA,MAAM1B,aAAa,GAAG4C,QAAQ,CAACjB,eAAe;QAE9C;QACA,MAAM;UAAExC,KAAK,EAAE8D;QAAW,CAAE,SAASD,MAAI,CAACtE,eAAe,CAACE,SAAS,EAAE,CAClEV,IAAI,CAAC,YAAY,CAAC,CAClBgF,MAAM,EAAE,CACRpE,EAAE,CAAC,IAAI,EAAE4D,UAAU,CAAC;QAEvB,IAAIO,WAAW,EAAE;UACf7D,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAE8D,WAAW,CAAC;UAC1E,OAAO1B,OAAO,CAACC,MAAM,CAACyB,WAAW,CAAC;QACpC;QAGA;QACA,MAAM;UAAE5D,IAAI,EAAEqB,UAAU;UAAEvB,KAAK,EAAE0C;QAAe,CAAE,SAASmB,MAAI,CAACtE,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEkB,aAAa,CAAC;QAEvC,IAAI6B,eAAe,EAAE;UACnBzC,OAAO,CAACD,KAAK,CAAC,qEAAqE,EAAE0C,eAAe,CAAC;UACrG,OAAON,OAAO,CAACC,MAAM,CAACK,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGpB,UAAU,CAACqB,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAAClB,KAAK,GAAG,EAAG,GAAGkB,GAAG,CAACjB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMkB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAEnD;QACA;QAEA,OAAO;UACLI,WAAW,EAAEJ,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC;UAClCC,eAAe,EAAEL,cAAc,CAACI,OAAO,CAAC,CAAC;SAC1C;MACH,CAAC,CAAC,OAAOpD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;MAC9B;IAAC;EACH;EAEA;;;EAGMgE,YAAYA,CAACnD,aAAqB;IAAA,IAAAoD,MAAA;IAAA,OAAAjC,iBAAA;MAEtC,IAAI;QACF,MAAM;UAAE9B,IAAI,EAAEqB,UAAU;UAAEvB,KAAK,EAAE0C;QAAe,CAAE,SAASuB,MAAI,CAAC1E,eAAe,CAACE,SAAS,EAAE,CACxFV,IAAI,CAAC,YAAY,CAAC,CAClBW,MAAM,CAAC,gBAAgB,CAAC,CACxBC,EAAE,CAAC,iBAAiB,EAAEkB,aAAa,CAAC;QAEvC,IAAI6B,eAAe,EAAE;UACnBzC,OAAO,CAACD,KAAK,CAAC,qEAAqE,EAAE0C,eAAe,CAAC;UACrG,OAAON,OAAO,CAACC,MAAM,CAACK,eAAe,CAAC;QACxC;QAEA,MAAMC,YAAY,GAAGpB,UAAU,CAACqB,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;UACpD,OAAOD,KAAK,GAAIC,GAAG,CAAClB,KAAK,GAAG,EAAG,GAAGkB,GAAG,CAACjB,OAAO;QAC/C,CAAC,EAAE,CAAC,CAAC;QAEL,MAAMkB,UAAU,GAAGJ,YAAY,GAAG,EAAE;QACpC,MAAMK,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;QAEnD,OAAO;UACLI,WAAW,EAAEJ,UAAU;UACvBM,eAAe,EAAEL;SAClB;MACH,CAAC,CAAC,OAAOhD,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;MAC9B;IAAC;EACH;EAEA;;;EAGMkE,kBAAkBA,CAACC,YAAsC;IAAA,IAAAC,MAAA;IAAA,OAAApC,iBAAA;MAE7D,IAAI;QACF,MAAM;UAAE9B,IAAI;UAAEF;QAAK,CAAE,SAASoE,MAAI,CAAC7E,eAAe,CAACE,SAAS,EAAE,CAC3DV,IAAI,CAAC,gBAAgB,CAAC,CACtB2B,MAAM,CAACyD,YAAY,CAAC,CACpBzE,MAAM,CAAC,IAAI,CAAC,CACZc,MAAM,EAAE;QAEX,IAAIR,KAAK,EAAE;UACTC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UACzE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;QAC9B;QAEA,OAAOE,IAAI,CAACI,EAAE;MAChB,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE,OAAOoC,OAAO,CAACC,MAAM,CAACrC,KAAK,CAAC;MAC9B;IAAC;EACH;;6BArdWX,yBAAyB;;mCAAzBA,0BAAyB;AAAA;;SAAzBA,0BAAyB;EAAAgF,OAAA,EAAzBhF,0BAAyB,CAAAiF,IAAA;EAAAC,UAAA,EAFxB;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}