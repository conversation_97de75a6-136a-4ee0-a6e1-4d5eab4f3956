{"ast": null, "code": "var _TimeTrackerPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\n// @ts-ignore\nimport { Chart, registerables } from 'chart.js';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nconst _c0 = [\"timeChart\"];\nfunction TimeTrackerPage_div_15_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dayName_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dayName_r3);\n  }\n}\nfunction TimeTrackerPage_div_15_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_15_div_9_Template_div_click_0_listener() {\n      const date_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(!date_r5.is_future && ctx_r1.selectDate(date_r5.date));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r5 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", date_r5.is_today)(\"selected\", date_r5.is_selected)(\"disabled\", date_r5.is_future);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", date_r5.day, \" \");\n  }\n}\nfunction TimeTrackerPage_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_15_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeWeek(-1));\n    });\n    i0.ɵɵtext(3, \"\\u2190\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 20);\n    i0.ɵɵtemplate(5, TimeTrackerPage_div_15_div_5_Template, 2, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_15_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeWeek(1));\n    });\n    i0.ɵɵtext(7, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 23);\n    i0.ɵɵtemplate(9, TimeTrackerPage_div_15_div_9_Template, 2, 7, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dayNames);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.weekDates);\n  }\n}\nfunction TimeTrackerPage_h2_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\");\n    i0.ɵɵtext(1, \"Activities\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimeTrackerPage_h2_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\");\n    i0.ɵɵtext(1, \"Weekly Average\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimeTrackerPage_h2_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h2\");\n    i0.ɵɵtext(1, \"Monthly Average\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimeTrackerPage_div_20_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r7.id);\n    i0.ɵɵattribute(\"data-emoji\", type_r7.emoji)(\"data-name\", type_r7.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", type_r7.emoji, \" \", type_r7.name, \" \");\n  }\n}\nfunction TimeTrackerPage_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"select\", 28);\n    i0.ɵɵlistener(\"change\", function TimeTrackerPage_div_20_Template_select_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.handleActivitySelection($event));\n    });\n    i0.ɵɵelementStart(2, \"option\", 29);\n    i0.ɵɵtext(3, \"Select Activity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TimeTrackerPage_div_20_option_4_Template, 2, 5, \"option\", 30);\n    i0.ɵɵelementStart(5, \"option\", 31);\n    i0.ɵɵtext(6, \"\\u2795 Custom Activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33)(9, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.hoursInput, $event) || (ctx_r1.hoursInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"h\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"input\", 35);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.minutesInput, $event) || (ctx_r1.minutesInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"m\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_20_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addActivity());\n    });\n    i0.ɵɵtext(16, \"Add\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 37)(18, \"div\", 38)(19, \"input\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.customEmoji, $event) || (ctx_r1.customEmoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"input\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.customActivityName, $event) || (ctx_r1.customActivityName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 33)(22, \"input\", 41);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.customHoursInput, $event) || (ctx_r1.customHoursInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\");\n    i0.ɵɵtext(24, \"h\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_div_20_Template_input_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.customMinutesInput, $event) || (ctx_r1.customMinutesInput = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27, \"m\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_div_20_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addCustomActivity());\n    });\n    i0.ɵɵtext(29, \"Add\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activityTypes);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showStandardInput ? \"flex\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.hoursInput);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.minutesInput);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showCustomForm ? \"flex\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.customEmoji);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.customActivityName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.customHoursInput);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.customMinutesInput);\n  }\n}\nfunction TimeTrackerPage_ng_container_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"div\", 49)(8, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_ng_container_33_div_1_Template_input_ngModelChange_8_listener($event) {\n      const activity_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(activity_r9.hours, $event) || (activity_r9.hours = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TimeTrackerPage_ng_container_33_div_1_Template_input_change_8_listener() {\n      const activity_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.updateActivity(activity_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"h\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TimeTrackerPage_ng_container_33_div_1_Template_input_ngModelChange_11_listener($event) {\n      const activity_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(activity_r9.minutes, $event) || (activity_r9.minutes = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"change\", function TimeTrackerPage_ng_container_33_div_1_Template_input_change_11_listener() {\n      const activity_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.updateActivity(activity_r9));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"m\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function TimeTrackerPage_ng_container_33_div_1_Template_button_click_14_listener() {\n      const activity_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteActivity(activity_r9.id));\n    });\n    i0.ɵɵtext(15, \"\\u00D7\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r9 = ctx.$implicit;\n    i0.ɵɵattribute(\"data-id\", activity_r9.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r9.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", activity_r9.hours);\n    i0.ɵɵattribute(\"data-activity-id\", activity_r9.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", activity_r9.minutes);\n    i0.ɵɵattribute(\"data-activity-id\", activity_r9.id);\n  }\n}\nfunction TimeTrackerPage_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TimeTrackerPage_ng_container_33_div_1_Template, 16, 7, \"div\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activities);\n  }\n}\nfunction TimeTrackerPage_ng_container_34_div_1_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1, \"per day\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimeTrackerPage_ng_container_34_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1, \"per day\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TimeTrackerPage_ng_container_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 46)(2, \"div\", 47);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"div\", 55)(8, \"span\", 56);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, TimeTrackerPage_ng_container_34_div_1_span_10_Template, 2, 0, \"span\", 57)(11, TimeTrackerPage_ng_container_34_div_1_span_11_Template, 2, 0, \"span\", 57);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r10.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r10.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", activity_r10.hours, \"h \", activity_r10.minutes, \"m\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.viewMode === \"week\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.viewMode === \"month\");\n  }\n}\nfunction TimeTrackerPage_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TimeTrackerPage_ng_container_34_div_1_Template, 12, 6, \"div\", 53);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activities);\n  }\n}\nChart.register(...registerables);\nexport class TimeTrackerPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // View mode\n    this.viewMode = 'day';\n    // Calendar data - starting with Monday\n    this.dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.weekDates = [];\n    this.weekOffset = 0;\n    this.selectedDate = new Date();\n    // Activity data\n    this.activityTypes = [];\n    this.activities = [];\n    this.weekActivities = [];\n    this.monthActivities = [];\n    // Form inputs\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    this.customEmoji = '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    // UI state\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    // Time summary\n    this.totalTrackedHours = '0.0';\n    this.remainingHours = '24.0';\n    // Chart\n    this.timeChart = null;\n    this.supabaseService = inject(SupabaseService);\n    this.timeTrackerService = inject(TimeTrackerUnifiedService);\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadActivityTypes();\n        this.generateWeekDates();\n        this.loadDataForCurrentView();\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeChart();\n    }, 500);\n  }\n  loadActivityTypes() {\n    this.timeTrackerService.getActivityTypes().subscribe(types => {\n      this.activityTypes = types;\n    });\n  }\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n    // Calculate the start of the week (Monday)\n    // For Monday as first day: 1 = Monday, 0 = Sunday (which should be treated as day 7)\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateStr = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      this.weekDates.push({\n        date: dateStr,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture\n      });\n    }\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  changeWeek(offset) {\n    this.weekOffset += offset;\n    this.generateWeekDates();\n  }\n  selectDate(dateStr) {\n    this.selectedDate = new Date(dateStr);\n    this.generateWeekDates();\n    this.loadActivities();\n  }\n  // View mode methods\n  setViewMode(mode) {\n    this.viewMode = mode;\n    this.loadDataForCurrentView();\n  }\n  loadDataForCurrentView() {\n    if (!this.userId) return;\n    switch (this.viewMode) {\n      case 'day':\n        this.loadActivities();\n        break;\n      case 'week':\n        this.loadWeekActivities();\n        break;\n      case 'month':\n        this.loadMonthActivities();\n        break;\n    }\n  }\n  loadWeekActivities() {\n    if (!this.userId) return;\n    const startOfWeek = this.getStartOfWeek(this.selectedDate);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n    this.timeTrackerService.getActivitiesForDateRange(this.userId, this.formatDate(startOfWeek), this.formatDate(endOfWeek)).subscribe(activities => {\n      this.weekActivities = this.calculateAverageActivitiesFromData(activities);\n      this.activities = this.weekActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n  loadMonthActivities() {\n    if (!this.userId) return;\n    const startOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);\n    const endOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);\n    this.timeTrackerService.getActivitiesForDateRange(this.userId, this.formatDate(startOfMonth), this.formatDate(endOfMonth)).subscribe(activities => {\n      this.monthActivities = this.calculateAverageActivitiesFromData(activities);\n      this.activities = this.monthActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n  getStartOfWeek(date) {\n    const currentDay = date.getDay(); // 0 = Sunday, 6 = Saturday\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(date);\n    startOfWeek.setDate(date.getDate() - currentDay + mondayOffset + 7 * this.weekOffset);\n    return startOfWeek;\n  }\n  calculateAverageActivitiesFromData(activities) {\n    // Group activities by name and emoji, and track unique days\n    const activityGroups = {};\n    activities.forEach(activity => {\n      const key = `${activity.name}_${activity.emoji}`;\n      if (!activityGroups[key]) {\n        activityGroups[key] = {\n          activities: [],\n          days: new Set()\n        };\n      }\n      activityGroups[key].activities.push(activity);\n      // Track unique dates when this activity occurred\n      if (activity.date) {\n        activityGroups[key].days.add(activity.date);\n      } else {\n        // Fallback to day_tracking_id if date is not available\n        activityGroups[key].days.add(activity.day_tracking_id);\n      }\n    });\n    // Calculate averages based on days that had activities\n    const averageActivities = [];\n    Object.keys(activityGroups).forEach(key => {\n      const group = activityGroups[key];\n      const totalMinutes = group.activities.reduce((sum, activity) => {\n        return sum + activity.hours * 60 + activity.minutes;\n      }, 0);\n      // Use the number of unique days that had this activity\n      const daysWithActivity = group.days.size;\n      const averageMinutes = totalMinutes / daysWithActivity;\n      const averageHours = Math.floor(averageMinutes / 60);\n      const remainingMinutes = Math.round(averageMinutes % 60);\n      if (averageHours > 0 || remainingMinutes > 0) {\n        averageActivities.push({\n          id: group.activities[0].id,\n          day_tracking_id: group.activities[0].day_tracking_id,\n          name: group.activities[0].name,\n          emoji: group.activities[0].emoji,\n          hours: averageHours,\n          minutes: remainingMinutes,\n          is_custom: group.activities[0].is_custom\n        });\n      }\n    });\n    return averageActivities;\n  }\n  loadActivities() {\n    if (!this.userId) return;\n    const dateStr = this.formatDate(this.selectedDate);\n    const userId = this.userId; // Store in local variable to ensure it's not null\n    // First get the day tracking to get the totals\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\n      // Then get the activities\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe(activities => {\n        this.activities = activities;\n        // Calculate totals manually from activities\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      });\n    });\n  }\n  // Calculate totals manually from activities\n  calculateTotals() {\n    // Calculate total time in hours (including minutes as fraction of hour)\n    const totalHours = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    // Format to 1 decimal place\n    this.totalTrackedHours = totalHours.toFixed(1);\n    // Calculate remaining hours (max 0)\n    const remainingHours = Math.max(0, 24 - totalHours);\n    this.remainingHours = remainingHours.toFixed(1);\n  }\n  handleActivitySelection(event) {\n    const select = event.target;\n    const value = select.value;\n    this.selectedActivityId = value;\n    this.showStandardInput = value !== '' && value !== 'custom';\n    this.showCustomForm = value === 'custom';\n  }\n  addActivity() {\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    // Find the selected activity type\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\n    if (!activityType) return;\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.hoursInput;\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, activityType.name, activityType.emoji, this.hoursInput, this.minutesInput, false).then(result => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          // This will be set on the server\n          name: activityType.name,\n          emoji: activityType.emoji,\n          hours: this.hoursInput,\n          minutes: this.minutesInput,\n          is_custom: false\n        });\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  addCustomActivity() {\n    if (!this.userId || !this.customActivityName.trim()) {\n      alert('Please enter an activity name');\n      return;\n    }\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase());\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.customHoursInput;\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, this.customActivityName.trim(), this.customEmoji, this.customHoursInput, this.customMinutesInput, true).then(result => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          // This will be set on the server\n          name: this.customActivityName.trim(),\n          emoji: this.customEmoji,\n          hours: this.customHoursInput,\n          minutes: this.customMinutesInput,\n          is_custom: true\n        });\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  updateActivity(activity) {\n    if (!activity.id) return;\n    // Validate time inputs\n    if (activity.hours < 0) activity.hours = 0;\n    if (activity.minutes < 0) activity.minutes = 0;\n    if (activity.hours > 23) activity.hours = 23;\n    if (activity.minutes > 59) activity.minutes = 59;\n    // Check if total time would exceed 24 hours\n    const currentHours = activity.hours;\n    const currentMinutes = activity.minutes;\n    // Calculate total time for all activities except this one\n    const otherActivitiesTime = this.activities.filter(a => a.id !== activity.id).reduce((total, a) => total + a.hours + a.minutes / 60, 0);\n    // Add this activity's time\n    const totalTime = otherActivitiesTime + currentHours + currentMinutes / 60;\n    if (totalTime > 24) {\n      alert('Total time cannot exceed 24 hours');\n      // Reset to previous values\n      this.loadActivities();\n      return;\n    }\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\n      // Calculate totals manually\n      this.calculateTotals();\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error updating activity:', error);\n      alert('Error updating activity. Please try again.');\n      this.loadActivities(); // Reload to get the correct state\n    });\n  }\n  deleteActivity(activityId) {\n    if (!activityId) return;\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\n      // Remove from local activities\n      this.activities = this.activities.filter(a => a.id !== activityId);\n      // Calculate totals manually\n      this.calculateTotals();\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error deleting activity:', error);\n      alert('Error deleting activity. Please try again.');\n    });\n  }\n  resetInputs() {\n    // Save the current emoji before resetting\n    const currentEmoji = this.customEmoji;\n    // Reset form values\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    // Keep the last used emoji instead of resetting to default\n    // Only set default emoji if current is empty\n    this.customEmoji = currentEmoji || '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    // Reset the select element to \"Select Activity\"\n    setTimeout(() => {\n      const selectElement = document.getElementById('activitySelect');\n      if (selectElement) {\n        selectElement.value = '';\n        // Directly update the UI state instead of using the event handler\n        this.selectedActivityId = '';\n        this.showStandardInput = false;\n        this.showCustomForm = false;\n      }\n    }, 0);\n  }\n  // We no longer need this method as we get the totals from the server\n  // Keeping it for backward compatibility\n  updateTotals() {\n    // This is now handled by the server\n  }\n  validateTotalTime(hours, minutes) {\n    // Calculate total time for all existing activities\n    const existingTime = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    // Add new time\n    const totalTime = existingTime + hours + minutes / 60;\n    // Check if total time exceeds 24 hours\n    return totalTime <= 24;\n  }\n  initializeChart() {\n    const canvas = document.getElementById('timeChart');\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    // Destroy existing chart if it exists\n    if (this.timeChart) {\n      this.timeChart.destroy();\n      this.timeChart = null;\n    }\n    // Register center text plugin\n    Chart.register({\n      id: 'centerText',\n      beforeDraw: chart => {\n        const ctx = chart.ctx;\n        const width = chart.width;\n        const height = chart.height;\n        ctx.restore();\n        ctx.font = 'bold 20px Inter';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n        // Draw \"24h\" text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillText('24h', width / 2, height / 2);\n        ctx.save();\n      }\n    });\n    try {\n      this.timeChart = new Chart(ctx, {\n        type: 'doughnut',\n        data: {\n          labels: [],\n          datasets: [{\n            data: [],\n            backgroundColor: [],\n            borderWidth: 0,\n            borderRadius: 5,\n            spacing: 2\n          }]\n        },\n        options: {\n          cutout: '75%',\n          radius: '90%',\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              enabled: true,\n              callbacks: {\n                label: function (context) {\n                  const value = context.raw;\n                  const hours = Math.floor(value);\n                  const minutes = Math.round((value - hours) * 60);\n                  return `${hours}h ${minutes}m`;\n                }\n              }\n            }\n          },\n          animation: {\n            animateRotate: true,\n            animateScale: true\n          }\n        }\n      });\n      this.updateChart();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error creating chart:', error);\n    }\n  }\n  updateChart() {\n    if (!this.timeChart) {\n      this.initializeChart();\n      return;\n    }\n    try {\n      const data = this.processActivitiesForChart();\n      this.timeChart.data.labels = data.labels;\n      this.timeChart.data.datasets[0].data = data.values;\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\n      this.timeChart.update();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error updating chart:', error);\n      // If there's an error, try to recreate the chart\n      this.timeChart = null;\n      setTimeout(() => {\n        this.initializeChart();\n      }, 100);\n    }\n  }\n  processActivitiesForChart() {\n    const labels = [];\n    const values = [];\n    this.activities.forEach(activity => {\n      // Include emoji in the label for better visualization\n      labels.push(`${activity.emoji} ${activity.name}`);\n      values.push(activity.hours + activity.minutes / 60);\n    });\n    // Add remaining time if total is less than 24 hours\n    const totalHours = values.reduce((a, b) => a + b, 0);\n    if (totalHours < 24) {\n      labels.push('Remaining');\n      values.push(24 - totalHours);\n    }\n    return {\n      labels,\n      values\n    };\n  }\n  generateColors(count) {\n    const colors = [];\n    // Use colors from activity_types in Supabase\n    this.activities.forEach(activity => {\n      // Find the matching activity type to get its color\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\n      // If we found a matching activity type with a color, use it; otherwise use a default color\n      if (activityType && activityType.color) {\n        colors.push(activityType.color);\n      } else if (activity.is_custom) {\n        // For custom activities, use a specific color\n        colors.push('#2C3E50'); // Royal Blue for custom activities\n      } else {\n        // Default color if no matching activity type or no color defined\n        colors.push('#2C3E50');\n      }\n    });\n    // Add dark color for remaining time\n    if (count > colors.length) {\n      colors.push('#1C1C1E');\n    }\n    return colors;\n  }\n}\n_TimeTrackerPage = TimeTrackerPage;\n_TimeTrackerPage.ɵfac = function TimeTrackerPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _TimeTrackerPage)();\n};\n_TimeTrackerPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _TimeTrackerPage,\n  selectors: [[\"app-time-tracker\"]],\n  viewQuery: function TimeTrackerPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartCanvas = _t.first);\n    }\n  },\n  decls: 36,\n  vars: 15,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"segmented-control\"], [1, \"segment\", 3, \"click\"], [\"class\", \"week-calendar\", 4, \"ngIf\"], [1, \"time-tracking\"], [4, \"ngIf\"], [\"class\", \"activity-input\", 4, \"ngIf\"], [1, \"time-visualization\"], [\"id\", \"timeChart\"], [1, \"time-summary\"], [1, \"total-time\"], [\"id\", \"totalTracked\"], [1, \"remaining-time\"], [\"id\", \"remainingTime\"], [1, \"activity-list\"], [1, \"week-calendar\"], [1, \"calendar-nav\"], [1, \"nav-arrow\", \"prev\", 3, \"click\"], [1, \"days\"], [\"class\", \"day-name\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-arrow\", \"next\", 3, \"click\"], [1, \"dates\"], [\"class\", \"date\", 3, \"active\", \"selected\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"day-name\"], [1, \"date\", 3, \"click\"], [1, \"activity-input\"], [\"id\", \"activitySelect\", 3, \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"value\", \"custom\"], [1, \"time-input-container\"], [1, \"time-inputs\"], [\"type\", \"number\", \"id\", \"hoursInput\", \"min\", \"0\", \"max\", \"23\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"minutesInput\", \"min\", \"0\", \"max\", \"59\", 3, \"ngModelChange\", \"ngModel\"], [3, \"click\"], [\"id\", \"customActivityForm\", 1, \"custom-inline-form\"], [1, \"custom-row\"], [\"type\", \"text\", \"name\", \"emoji\", \"id\", \"emoji\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"id\", \"customActivityName\", \"placeholder\", \"Activity name\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"customHoursInput\", \"min\", \"0\", \"max\", \"23\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"number\", \"id\", \"customMinutesInput\", \"min\", \"0\", \"max\", \"59\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-name-emoji\"], [1, \"activity-icon\"], [1, \"activity-info\"], [1, \"time-input\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"23\", 1, \"hours\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [\"type\", \"number\", \"min\", \"0\", \"max\", \"59\", 1, \"minutes\", 3, \"ngModelChange\", \"change\", \"ngModel\"], [1, \"delete-activity\", 3, \"click\"], [\"class\", \"activity-item readonly\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\", \"readonly\"], [1, \"time-display\"], [1, \"time-value\"], [\"class\", \"average-label\", 4, \"ngIf\"], [1, \"average-label\"]],\n  template: function TimeTrackerPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Time Tracker\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 3)(9, \"button\", 4);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_9_listener() {\n        return ctx.setViewMode(\"month\");\n      });\n      i0.ɵɵtext(10, \" Month \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"button\", 4);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_11_listener() {\n        return ctx.setViewMode(\"week\");\n      });\n      i0.ɵɵtext(12, \" Week \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"button\", 4);\n      i0.ɵɵlistener(\"click\", function TimeTrackerPage_Template_button_click_13_listener() {\n        return ctx.setViewMode(\"day\");\n      });\n      i0.ɵɵtext(14, \" Day \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(15, TimeTrackerPage_div_15_Template, 10, 2, \"div\", 5);\n      i0.ɵɵelementStart(16, \"section\", 6);\n      i0.ɵɵtemplate(17, TimeTrackerPage_h2_17_Template, 2, 0, \"h2\", 7)(18, TimeTrackerPage_h2_18_Template, 2, 0, \"h2\", 7)(19, TimeTrackerPage_h2_19_Template, 2, 0, \"h2\", 7)(20, TimeTrackerPage_div_20_Template, 30, 11, \"div\", 8);\n      i0.ɵɵelementStart(21, \"div\", 9);\n      i0.ɵɵelement(22, \"canvas\", 10);\n      i0.ɵɵelementStart(23, \"div\", 11)(24, \"div\", 12);\n      i0.ɵɵtext(25, \" Total tracked: \");\n      i0.ɵɵelementStart(26, \"span\", 13);\n      i0.ɵɵtext(27);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(28, \"div\", 14);\n      i0.ɵɵtext(29, \" Remaining: \");\n      i0.ɵɵelementStart(30, \"span\", 15);\n      i0.ɵɵtext(31);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(32, \"div\", 16);\n      i0.ɵɵtemplate(33, TimeTrackerPage_ng_container_33_Template, 2, 1, \"ng-container\", 7)(34, TimeTrackerPage_ng_container_34_Template, 2, 1, \"ng-container\", 7);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(35, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(9);\n      i0.ɵɵclassProp(\"active\", ctx.viewMode === \"month\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.viewMode === \"week\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵclassProp(\"active\", ctx.viewMode === \"day\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"day\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"day\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"week\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"month\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"day\");\n      i0.ɵɵadvance(7);\n      i0.ɵɵtextInterpolate1(\"\", ctx.totalTrackedHours, \"h\");\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate1(\"\", ctx.remainingHours, \"h\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"day\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"week\" || ctx.viewMode === \"month\");\n    }\n  },\n  dependencies: [IonicModule, CommonModule, i1.NgForOf, i1.NgIf, FormsModule, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.NumberValueAccessor, i2.NgControlStatus, i2.MinValidator, i2.MaxValidator, i2.NgModel, RouterModule, NavigationComponent, EmojiInputDirective],\n  styles: [\"[_nghost-%COMP%] {\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n  --active-date: #4169E1;\\n  --inactive-date: #2C2C2E;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\n\\n\\n.time-tracking[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n\\n.activity-input[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.activity-input[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n\\n.time-input-container[_ngcontent-%COMP%], \\n#customActivityForm[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  display: none;\\n  gap: 12px;\\n  align-items: center;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  padding: 4px;\\n  border: none;\\n  background: none;\\n  color: var(--text-color);\\n  font-size: 14px;\\n  text-align: center;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n}\\n\\n#customActivityForm[_ngcontent-%COMP%] {\\n  flex-wrap: wrap;\\n}\\n\\n#customActivityForm[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%] {\\n  \\n\\n  padding: 12px;\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n}\\n\\n#customActivityForm[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n}\\n\\n.activity-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], \\n#customActivityForm[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  background-color: var(--accent-color);\\n  color: var(--text-color);\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: opacity 0.2s;\\n}\\n\\n.activity-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover, \\n#customActivityForm[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n\\n.time-visualization[_ngcontent-%COMP%] {\\n  margin: 32px 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 24px;\\n  position: relative;\\n}\\n\\n#timeChart[_ngcontent-%COMP%] {\\n  width: 240px !important;\\n  height: 240px !important;\\n  margin: 0 auto;\\n}\\n\\n.time-summary[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 16px;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 12px;\\n  margin-top: 16px;\\n}\\n\\n.time-summary[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.time-summary[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n  font-weight: 600;\\n  margin-left: 4px;\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-top: 24px;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 12px;\\n  justify-content: space-between;\\n}\\n\\n.activity-name-emoji[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n}\\n\\n.activity-name-emoji[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  min-width: 32px;\\n  text-align: center;\\n}\\n\\n.activity-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.activity-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  margin: 0;\\n  color: var(--text-color);\\n}\\n\\n.delete-activity[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--secondary-text);\\n  font-size: 24px;\\n  cursor: pointer;\\n  padding: 0 4px;\\n  transition: color 0.2s;\\n  opacity: 0.5;\\n  position: relative;\\n  top: -2px;\\n}\\n\\n.delete-activity[_ngcontent-%COMP%]:hover {\\n  color: var(--text-color);\\n  opacity: 1;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 50px;\\n  padding: 4px;\\n  border: none;\\n  background: none;\\n  color: var(--text-color);\\n  font-size: 14px;\\n  text-align: center;\\n}\\n\\n.time-inputs[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n}\\n\\n\\n\\n.activity-item.readonly[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 4px;\\n}\\n\\n.time-value[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n.average-label[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n  font-weight: 400;\\n}\\n\\n#customActivityName[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.activity-input[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n#customActivityForm[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n#customActivityForm[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  \\n\\n  border: 1px solid var(--quest-border);\\n  border-radius: 12px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n  font-size: 16px;\\n}\\n\\n.activity-input[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], \\n#customActivityForm[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  background-color: var(--accent-color);\\n  color: var(--text-color);\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: opacity 0.2s;\\n}\\n\\n\\n\\n.activity-item.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.activity-item.disabled[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  overflow-y: auto;\\n  padding-bottom: 74px;\\n  scrollbar-width: none;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.segmented-control[_ngcontent-%COMP%] {\\n  display: flex;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 12px;\\n  padding: 4px;\\n  margin-bottom: 24px;\\n  gap: 2px;\\n}\\n\\n.segment[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  background: transparent;\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.segment.active[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: var(--text-color);\\n  font-weight: 600;\\n}\\n\\n.segment[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(255, 255, 255, 0.05);\\n  color: var(--text-color);\\n}\\n\\n.week-calendar[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.days[_ngcontent-%COMP%], .dates[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  gap: 8px;\\n}\\n\\n.days[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  margin: 0 auto;\\n  font-size: 14px;\\n  background-color: var(--inactive-date);\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  position: relative;\\n}\\n\\n.date.active[_ngcontent-%COMP%] {\\n  background-color: var(--active-date);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -4px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 4px;\\n  height: 4px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n}\\n\\n.date[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.date.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 16px;\\n}\\n\\n.side-quests[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 90%;\\n  height: 1px;\\n  background: linear-gradient(to right, transparent, #4B0082, transparent);\\n}\\n\\n.calendar[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  padding: 10px;\\n  background: var(--bg-secondary);\\n  border-radius: 8px;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 10px;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 5px;\\n  text-align: center;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  margin: 0 auto;\\n}\\n\\n.day-number[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.day-number.selected[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.day-number.today[_ngcontent-%COMP%] {\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: none;\\n  background: var(--bg-tertiary);\\n  color: var(--text-primary);\\n  border-radius: 4px;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  margin-right: 16px;\\n}\\n\\n\\n\\ninput[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, \\ninput[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button {\\n  opacity: 0.3;\\n}\\n\\n.custom-inline-form[_ngcontent-%COMP%] {\\n  display: none;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  align-items: center;\\n  margin-top: 12px;\\n}\\n\\n.custom-inline-form[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%] {\\n  padding: 12px;\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n  \\n\\n}\\n\\n.emoji-select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  font-size: 18px;\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  color: var(--text-color);\\n}\\n\\n.custom-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  width: 100%;\\n}\\n\\n\\n\\n\\n\\n.custom-row[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 80px;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]   .time-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 60px;\\n  padding: 8px;\\n  border: none;\\n  border-radius: 8px;\\n  background-color: var(--quest-bg);\\n  color: var(--text-color);\\n  font-size: 14px;\\n  font-weight: 500;\\n  text-align: center;\\n  box-shadow: inset 0 0 0 1px var(--quest-border);\\n  transition: all 0.2s;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]   .time-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: inset 0 0 0 1px white;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]   .time-input[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  margin: 0 4px;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 50px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "TimeTrackerUnifiedService", "take", "NavigationComponent", "Chart", "registerables", "SupabaseService", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "dayName_r3", "ɵɵlistener", "TimeTrackerPage_div_15_div_9_Template_div_click_0_listener", "date_r5", "ɵɵrestoreView", "_r4", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "is_future", "selectDate", "date", "ɵɵclassProp", "is_today", "is_selected", "ɵɵtextInterpolate1", "day", "TimeTrackerPage_div_15_Template_button_click_2_listener", "_r1", "changeWeek", "ɵɵtemplate", "TimeTrackerPage_div_15_div_5_Template", "TimeTrackerPage_div_15_Template_button_click_6_listener", "TimeTrackerPage_div_15_div_9_Template", "ɵɵproperty", "dayNames", "weekDates", "type_r7", "id", "ɵɵtextInterpolate2", "emoji", "name", "TimeTrackerPage_div_20_Template_select_change_1_listener", "$event", "_r6", "handleActivitySelection", "TimeTrackerPage_div_20_option_4_Template", "ɵɵtwoWayListener", "TimeTrackerPage_div_20_Template_input_ngModelChange_9_listener", "ɵɵtwoWayBindingSet", "hoursInput", "TimeTrackerPage_div_20_Template_input_ngModelChange_12_listener", "minutesInput", "TimeTrackerPage_div_20_Template_button_click_15_listener", "addActivity", "TimeTrackerPage_div_20_Template_input_ngModelChange_19_listener", "customEmoji", "TimeTrackerPage_div_20_Template_input_ngModelChange_20_listener", "customActivityName", "TimeTrackerPage_div_20_Template_input_ngModelChange_22_listener", "customHoursInput", "TimeTrackerPage_div_20_Template_input_ngModelChange_25_listener", "customMinutesInput", "TimeTrackerPage_div_20_Template_button_click_28_listener", "addCustomActivity", "activityTypes", "ɵɵstyleProp", "showStandardInput", "ɵɵtwoWayProperty", "showCustomForm", "TimeTrackerPage_ng_container_33_div_1_Template_input_ngModelChange_8_listener", "activity_r9", "_r8", "hours", "TimeTrackerPage_ng_container_33_div_1_Template_input_change_8_listener", "updateActivity", "TimeTrackerPage_ng_container_33_div_1_Template_input_ngModelChange_11_listener", "minutes", "TimeTrackerPage_ng_container_33_div_1_Template_input_change_11_listener", "TimeTrackerPage_ng_container_33_div_1_Template_button_click_14_listener", "deleteActivity", "ɵɵelementContainerStart", "TimeTrackerPage_ng_container_33_div_1_Template", "activities", "TimeTrackerPage_ng_container_34_div_1_span_10_Template", "TimeTrackerPage_ng_container_34_div_1_span_11_Template", "activity_r10", "viewMode", "TimeTrackerPage_ng_container_34_div_1_Template", "register", "TimeTrackerPage", "constructor", "userId", "weekOffset", "selectedDate", "Date", "weekActivities", "monthActivities", "selectedActivityId", "totalTrackedHours", "remainingHours", "timeChart", "supabaseService", "timeTrackerService", "ngOnInit", "currentUser$", "pipe", "subscribe", "authUser", "loadActivityTypes", "generateWeekDates", "loadDataForCurrentView", "ngAfterViewInit", "setTimeout", "initializeChart", "getActivityTypes", "types", "today", "currentDay", "getDay", "mondayOffset", "startOfWeek", "setDate", "getDate", "i", "dateStr", "formatDate", "isToday", "isSameDay", "isSelected", "isFuture", "push", "year", "getFullYear", "month", "String", "getMonth", "padStart", "date1", "date2", "offset", "loadActivities", "setViewMode", "mode", "loadWeekActivities", "loadMonthActivities", "getStartOfWeek", "endOfWeek", "getActivitiesForDateRange", "calculateAverageActivitiesFromData", "calculateTotals", "updateChart", "startOfMonth", "endOfMonth", "activityGroups", "for<PERSON>ach", "activity", "key", "days", "Set", "add", "day_tracking_id", "averageActivities", "Object", "keys", "group", "totalMinutes", "reduce", "sum", "daysWithActivity", "size", "averageMinutes", "averageHours", "Math", "floor", "remainingMinutes", "round", "is_custom", "getDayTracking", "getActivities", "totalHours", "total", "toFixed", "max", "event", "select", "target", "value", "alert", "validateTotalTime", "activityType", "find", "type", "existingActivity", "updatedHours", "updatedMinutes", "finalHours", "finalMinutes", "then", "resetInputs", "catch", "error", "console", "createActivity", "result", "trim", "toLowerCase", "currentHours", "currentMinutes", "otherActivitiesTime", "filter", "a", "totalTime", "activityId", "<PERSON><PERSON><PERSON><PERSON>", "selectElement", "document", "getElementById", "updateTotals", "existingTime", "canvas", "ctx", "getContext", "destroy", "beforeDraw", "chart", "width", "height", "restore", "font", "textAlign", "textBaseline", "fillStyle", "fillText", "save", "data", "labels", "datasets", "backgroundColor", "borderWidth", "borderRadius", "spacing", "options", "cutout", "radius", "plugins", "legend", "display", "tooltip", "enabled", "callbacks", "label", "context", "raw", "animation", "animateRotate", "animateScale", "processActivitiesForChart", "values", "generateColors", "length", "update", "b", "count", "colors", "color", "selectors", "viewQuery", "TimeTrackerPage_Query", "rf", "ɵɵelement", "TimeTrackerPage_Template_button_click_9_listener", "TimeTrackerPage_Template_button_click_11_listener", "TimeTrackerPage_Template_button_click_13_listener", "TimeTrackerPage_div_15_Template", "TimeTrackerPage_h2_17_Template", "TimeTrackerPage_h2_18_Template", "TimeTrackerPage_h2_19_Template", "TimeTrackerPage_div_20_Template", "TimeTrackerPage_ng_container_33_Template", "TimeTrackerPage_ng_container_34_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\time-tracker\\time-tracker.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\time-tracker\\time-tracker.page.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { Activity, ActivityType } from '../../models/activity.model';\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\n// @ts-ignore\nimport { Chart, registerables } from 'chart.js';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\n\nChart.register(...registerables);\n\ninterface DateDisplay {\n  date: string;\n  day: number;\n  is_today: boolean;\n  is_selected: boolean;\n  is_future: boolean;\n}\n\n@Component({\n  selector: 'app-time-tracker',\n  templateUrl: './time-tracker.page.html',\n  styleUrls: ['./time-tracker.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]\n})\nexport class TimeTrackerPage implements OnInit, AfterViewInit {\n  // User data\n  userId: string | null = null;\n\n  // View mode\n  viewMode: 'day' | 'week' | 'month' = 'day';\n\n  // Calendar data - starting with Monday\n  dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n  weekDates: DateDisplay[] = [];\n  weekOffset = 0;\n  selectedDate: Date = new Date();\n\n  // Activity data\n  activityTypes: ActivityType[] = [];\n  activities: Activity[] = [];\n  weekActivities: Activity[] = [];\n  monthActivities: Activity[] = [];\n\n  // Form inputs\n  selectedActivityId = '';\n  hoursInput = 0;\n  minutesInput = 0;\n  customActivityName = '';\n  customEmoji = '⚡';\n  customHoursInput = 0;\n  customMinutesInput = 0;\n\n  // UI state\n  showStandardInput = false;\n  showCustomForm = false;\n\n  // Time summary\n  totalTrackedHours = '0.0';\n  remainingHours = '24.0';\n\n  // Chart\n  timeChart: Chart | null = null;\n\n  @ViewChild('timeChart') chartCanvas: ElementRef | undefined;\n\n  private supabaseService = inject(SupabaseService);\n  private timeTrackerService = inject(TimeTrackerUnifiedService);\n\n  constructor() {}\n\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadActivityTypes();\n        this.generateWeekDates();\n        this.loadDataForCurrentView();\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeChart();\n    }, 500);\n  }\n\n  loadActivityTypes() {\n    this.timeTrackerService.getActivityTypes().subscribe((types: ActivityType[]) => {\n      this.activityTypes = types;\n    });\n  }\n\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n\n    // Calculate the start of the week (Monday)\n    // For Monday as first day: 1 = Monday, 0 = Sunday (which should be treated as day 7)\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));\n\n    this.weekDates = [];\n\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n\n      const dateStr = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n\n      this.weekDates.push({\n        date: dateStr,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture\n      });\n    }\n  }\n\n  formatDate(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  isSameDay(date1: Date, date2: Date): boolean {\n    return date1.getFullYear() === date2.getFullYear() &&\n           date1.getMonth() === date2.getMonth() &&\n           date1.getDate() === date2.getDate();\n  }\n\n  changeWeek(offset: number) {\n    this.weekOffset += offset;\n    this.generateWeekDates();\n  }\n\n  selectDate(dateStr: string) {\n    this.selectedDate = new Date(dateStr);\n    this.generateWeekDates();\n    this.loadActivities();\n  }\n\n  // View mode methods\n  setViewMode(mode: 'day' | 'week' | 'month') {\n    this.viewMode = mode;\n    this.loadDataForCurrentView();\n  }\n\n  loadDataForCurrentView() {\n    if (!this.userId) return;\n\n    switch (this.viewMode) {\n      case 'day':\n        this.loadActivities();\n        break;\n      case 'week':\n        this.loadWeekActivities();\n        break;\n      case 'month':\n        this.loadMonthActivities();\n        break;\n    }\n  }\n\n  loadWeekActivities() {\n    if (!this.userId) return;\n\n    const startOfWeek = this.getStartOfWeek(this.selectedDate);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n\n    this.timeTrackerService.getActivitiesForDateRange(\n      this.userId,\n      this.formatDate(startOfWeek),\n      this.formatDate(endOfWeek)\n    ).subscribe((activities: Activity[]) => {\n      this.weekActivities = this.calculateAverageActivitiesFromData(activities);\n      this.activities = this.weekActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n\n  loadMonthActivities() {\n    if (!this.userId) return;\n\n    const startOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);\n    const endOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);\n\n    this.timeTrackerService.getActivitiesForDateRange(\n      this.userId,\n      this.formatDate(startOfMonth),\n      this.formatDate(endOfMonth)\n    ).subscribe((activities: Activity[]) => {\n      this.monthActivities = this.calculateAverageActivitiesFromData(activities);\n      this.activities = this.monthActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n\n  getStartOfWeek(date: Date): Date {\n    const currentDay = date.getDay(); // 0 = Sunday, 6 = Saturday\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(date);\n    startOfWeek.setDate(date.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));\n    return startOfWeek;\n  }\n\n  calculateAverageActivitiesFromData(activities: Activity[]): Activity[] {\n    // Group activities by name and emoji, and track unique days\n    const activityGroups: { [key: string]: { activities: Activity[], days: Set<string> } } = {};\n\n    activities.forEach(activity => {\n      const key = `${activity.name}_${activity.emoji}`;\n      if (!activityGroups[key]) {\n        activityGroups[key] = { activities: [], days: new Set() };\n      }\n      activityGroups[key].activities.push(activity);\n      // Track unique dates when this activity occurred\n      if (activity.date) {\n        activityGroups[key].days.add(activity.date);\n      } else {\n        // Fallback to day_tracking_id if date is not available\n        activityGroups[key].days.add(activity.day_tracking_id);\n      }\n    });\n\n    // Calculate averages based on days that had activities\n    const averageActivities: Activity[] = [];\n\n    Object.keys(activityGroups).forEach(key => {\n      const group = activityGroups[key];\n      const totalMinutes = group.activities.reduce((sum, activity) => {\n        return sum + (activity.hours * 60) + activity.minutes;\n      }, 0);\n\n      // Use the number of unique days that had this activity\n      const daysWithActivity = group.days.size;\n      const averageMinutes = totalMinutes / daysWithActivity;\n      const averageHours = Math.floor(averageMinutes / 60);\n      const remainingMinutes = Math.round(averageMinutes % 60);\n\n      if (averageHours > 0 || remainingMinutes > 0) {\n        averageActivities.push({\n          id: group.activities[0].id,\n          day_tracking_id: group.activities[0].day_tracking_id,\n          name: group.activities[0].name,\n          emoji: group.activities[0].emoji,\n          hours: averageHours,\n          minutes: remainingMinutes,\n          is_custom: group.activities[0].is_custom\n        });\n      }\n    });\n\n    return averageActivities;\n  }\n\n  loadActivities() {\n    if (!this.userId) return;\n\n    const dateStr = this.formatDate(this.selectedDate);\n    const userId = this.userId; // Store in local variable to ensure it's not null\n\n    // First get the day tracking to get the totals\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\n      // Then get the activities\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe((activities: Activity[]) => {\n        this.activities = activities;\n\n        // Calculate totals manually from activities\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      });\n    });\n  }\n\n  // Calculate totals manually from activities\n  calculateTotals() {\n    // Calculate total time in hours (including minutes as fraction of hour)\n    const totalHours = this.activities.reduce((total, activity) => {\n      return total + activity.hours + (activity.minutes / 60);\n    }, 0);\n\n    // Format to 1 decimal place\n    this.totalTrackedHours = totalHours.toFixed(1);\n\n    // Calculate remaining hours (max 0)\n    const remainingHours = Math.max(0, 24 - totalHours);\n    this.remainingHours = remainingHours.toFixed(1);\n\n  }\n\n  handleActivitySelection(event: Event) {\n    const select = event.target as HTMLSelectElement;\n    const value = select.value;\n\n    this.selectedActivityId = value;\n    this.showStandardInput = value !== '' && value !== 'custom';\n    this.showCustomForm = value === 'custom';\n  }\n\n  addActivity() {\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n\n    // Find the selected activity type\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\n    if (!activityType) return;\n\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\n\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.hoursInput;\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\n\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId as string; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n\n      this.timeTrackerService.createActivity(\n        userId,\n        dateStr,\n        activityType.name,\n        activityType.emoji,\n        this.hoursInput,\n        this.minutesInput,\n        false\n      ).then((result) => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '', // This will be set on the server\n          name: activityType.name,\n          emoji: activityType.emoji,\n          hours: this.hoursInput,\n          minutes: this.minutesInput,\n          is_custom: false\n        });\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n\n  addCustomActivity() {\n    if (!this.userId || !this.customActivityName.trim()) {\n      alert('Please enter an activity name');\n      return;\n    }\n\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity =>\n      activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase()\n    );\n\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.customHoursInput;\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\n\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId as string; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n\n      this.timeTrackerService.createActivity(\n        userId,\n        dateStr,\n        this.customActivityName.trim(),\n        this.customEmoji,\n        this.customHoursInput,\n        this.customMinutesInput,\n        true\n      ).then((result) => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '', // This will be set on the server\n          name: this.customActivityName.trim(),\n          emoji: this.customEmoji,\n          hours: this.customHoursInput,\n          minutes: this.customMinutesInput,\n          is_custom: true\n        });\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n\n  updateActivity(activity: Activity) {\n    if (!activity.id) return;\n\n    // Validate time inputs\n    if (activity.hours < 0) activity.hours = 0;\n    if (activity.minutes < 0) activity.minutes = 0;\n    if (activity.hours > 23) activity.hours = 23;\n    if (activity.minutes > 59) activity.minutes = 59;\n\n    // Check if total time would exceed 24 hours\n    const currentHours = activity.hours;\n    const currentMinutes = activity.minutes;\n\n    // Calculate total time for all activities except this one\n    const otherActivitiesTime = this.activities\n      .filter(a => a.id !== activity.id)\n      .reduce((total, a) => total + a.hours + (a.minutes / 60), 0);\n\n    // Add this activity's time\n    const totalTime = otherActivitiesTime + currentHours + (currentMinutes / 60);\n\n    if (totalTime > 24) {\n      alert('Total time cannot exceed 24 hours');\n      // Reset to previous values\n      this.loadActivities();\n      return;\n    }\n\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\n      // Calculate totals manually\n      this.calculateTotals();\n\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error updating activity:', error);\n      alert('Error updating activity. Please try again.');\n      this.loadActivities(); // Reload to get the correct state\n    });\n  }\n\n  deleteActivity(activityId: string) {\n    if (!activityId) return;\n\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\n      // Remove from local activities\n      this.activities = this.activities.filter(a => a.id !== activityId);\n\n      // Calculate totals manually\n      this.calculateTotals();\n\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error deleting activity:', error);\n      alert('Error deleting activity. Please try again.');\n    });\n  }\n\n  resetInputs() {\n    // Save the current emoji before resetting\n    const currentEmoji = this.customEmoji;\n\n    // Reset form values\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    // Keep the last used emoji instead of resetting to default\n    // Only set default emoji if current is empty\n    this.customEmoji = currentEmoji || '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n\n    // Reset the select element to \"Select Activity\"\n    setTimeout(() => {\n      const selectElement = document.getElementById('activitySelect') as HTMLSelectElement;\n      if (selectElement) {\n        selectElement.value = '';\n\n        // Directly update the UI state instead of using the event handler\n        this.selectedActivityId = '';\n        this.showStandardInput = false;\n        this.showCustomForm = false;\n      }\n    }, 0);\n  }\n\n  // We no longer need this method as we get the totals from the server\n  // Keeping it for backward compatibility\n  updateTotals() {\n    // This is now handled by the server\n  }\n\n  validateTotalTime(hours: number, minutes: number): boolean {\n    // Calculate total time for all existing activities\n    const existingTime = this.activities.reduce((total, activity) => {\n      return total + activity.hours + (activity.minutes / 60);\n    }, 0);\n\n    // Add new time\n    const totalTime = existingTime + hours + (minutes / 60);\n\n    // Check if total time exceeds 24 hours\n    return totalTime <= 24;\n  }\n\n  initializeChart() {\n    const canvas = document.getElementById('timeChart') as HTMLCanvasElement;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Destroy existing chart if it exists\n    if (this.timeChart) {\n      this.timeChart.destroy();\n      this.timeChart = null;\n    }\n\n    // Register center text plugin\n    Chart.register({\n      id: 'centerText',\n      beforeDraw: (chart: any) => {\n        const ctx = chart.ctx;\n        const width = chart.width;\n        const height = chart.height;\n\n        ctx.restore();\n        ctx.font = 'bold 20px Inter';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n\n        // Draw \"24h\" text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillText('24h', width / 2, height / 2);\n        ctx.save();\n      }\n    });\n\n    try {\n      this.timeChart = new Chart(ctx, {\n        type: 'doughnut',\n        data: {\n          labels: [],\n          datasets: [{\n            data: [],\n            backgroundColor: [],\n            borderWidth: 0,\n            borderRadius: 5,\n            spacing: 2\n          }]\n        },\n        options: {\n          cutout: '75%',\n          radius: '90%',\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              enabled: true,\n              callbacks: {\n                label: function(context: any) {\n                  const value = context.raw as number;\n                  const hours = Math.floor(value);\n                  const minutes = Math.round((value - hours) * 60);\n                  return `${hours}h ${minutes}m`;\n                }\n              }\n            }\n          },\n          animation: {\n            animateRotate: true,\n            animateScale: true\n          }\n        }\n      });\n\n      this.updateChart();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error creating chart:', error);\n    }\n  }\n\n  updateChart() {\n    if (!this.timeChart) {\n      this.initializeChart();\n      return;\n    }\n\n    try {\n      const data = this.processActivitiesForChart();\n\n      this.timeChart.data.labels = data.labels;\n      this.timeChart.data.datasets[0].data = data.values;\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\n\n      this.timeChart.update();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error updating chart:', error);\n      // If there's an error, try to recreate the chart\n      this.timeChart = null;\n      setTimeout(() => {\n        this.initializeChart();\n      }, 100);\n    }\n  }\n\n  processActivitiesForChart() {\n    const labels: string[] = [];\n    const values: number[] = [];\n\n    this.activities.forEach(activity => {\n      // Include emoji in the label for better visualization\n      labels.push(`${activity.emoji} ${activity.name}`);\n      values.push(activity.hours + (activity.minutes / 60));\n    });\n\n    // Add remaining time if total is less than 24 hours\n    const totalHours = values.reduce((a, b) => a + b, 0);\n    if (totalHours < 24) {\n      labels.push('Remaining');\n      values.push(24 - totalHours);\n    }\n\n    return { labels, values };\n  }\n\n  generateColors(count: number) {\n    const colors: string[] = [];\n\n    // Use colors from activity_types in Supabase\n    this.activities.forEach(activity => {\n      // Find the matching activity type to get its color\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\n\n      // If we found a matching activity type with a color, use it; otherwise use a default color\n      if (activityType && activityType.color) {\n        colors.push(activityType.color);\n      } else if (activity.is_custom) {\n        // For custom activities, use a specific color\n        colors.push('#2C3E50'); // Royal Blue for custom activities\n      } else {\n        // Default color if no matching activity type or no color defined\n        colors.push('#2C3E50');\n      }\n    });\n\n    // Add dark color for remaining time\n    if (count > colors.length) {\n      colors.push('#1C1C1E');\n    }\n\n    return colors;\n  }\n}\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Time Tracker</h1>\r\n    </header>\r\n\r\n    <!-- Segmented Control -->\r\n    <div class=\"segmented-control\">\r\n        <button\r\n            class=\"segment\"\r\n            [class.active]=\"viewMode === 'month'\"\r\n            (click)=\"setViewMode('month')\">\r\n            Month\r\n        </button>\r\n        <button\r\n            class=\"segment\"\r\n            [class.active]=\"viewMode === 'week'\"\r\n            (click)=\"setViewMode('week')\">\r\n            Week\r\n        </button>\r\n        <button\r\n            class=\"segment\"\r\n            [class.active]=\"viewMode === 'day'\"\r\n            (click)=\"setViewMode('day')\">\r\n            Day\r\n        </button>\r\n    </div>\r\n\r\n    <div class=\"week-calendar\" *ngIf=\"viewMode === 'day'\">\r\n        <div class=\"calendar-nav\">\r\n            <button class=\"nav-arrow prev\" (click)=\"changeWeek(-1)\">←</button>\r\n            <div class=\"days\">\r\n                <div class=\"day-name\" *ngFor=\"let dayName of dayNames\">{{ dayName }}</div>\r\n            </div>\r\n            <button class=\"nav-arrow next\" (click)=\"changeWeek(1)\">→</button>\r\n        </div>\r\n        <div class=\"dates\">\r\n            <div *ngFor=\"let date of weekDates\"\r\n                 class=\"date\"\r\n                 [class.active]=\"date.is_today\"\r\n                 [class.selected]=\"date.is_selected\"\r\n                 [class.disabled]=\"date.is_future\"\r\n                 (click)=\"!date.is_future && selectDate(date.date)\">\r\n                {{ date.day }}\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <section class=\"time-tracking\">\r\n        <h2 *ngIf=\"viewMode === 'day'\">Activities</h2>\r\n        <h2 *ngIf=\"viewMode === 'week'\">Weekly Average</h2>\r\n        <h2 *ngIf=\"viewMode === 'month'\">Monthly Average</h2>\r\n\r\n        <div class=\"activity-input\" *ngIf=\"viewMode === 'day'\">\r\n            <select id=\"activitySelect\" (change)=\"handleActivitySelection($event)\">\r\n                <option value=\"\">Select Activity</option>\r\n                <option *ngFor=\"let type of activityTypes\" [value]=\"type.id\" [attr.data-emoji]=\"type.emoji\" [attr.data-name]=\"type.name\">\r\n                    {{ type.emoji }} {{ type.name }}\r\n                </option>\r\n                <option value=\"custom\">➕ Custom Activity</option>\r\n            </select>\r\n\r\n            <!-- Standard time input -->\r\n            <div class=\"time-input-container\" [style.display]=\"showStandardInput ? 'flex' : 'none'\">\r\n                <div class=\"time-inputs\">\r\n                    <input type=\"number\" id=\"hoursInput\" min=\"0\" max=\"23\" [(ngModel)]=\"hoursInput\">\r\n                    <span>h</span>\r\n                    <input type=\"number\" id=\"minutesInput\" min=\"0\" max=\"59\" [(ngModel)]=\"minutesInput\">\r\n                    <span>m</span>\r\n                </div>\r\n                <button (click)=\"addActivity()\">Add</button>\r\n            </div>\r\n\r\n            <!-- Custom activity input -->\r\n            <div id=\"customActivityForm\" class=\"custom-inline-form\" [style.display]=\"showCustomForm ? 'flex' : 'none'\">\r\n                <div class=\"custom-row\">\r\n                    <input type=\"text\" name=\"emoji\" id=\"emoji\" [(ngModel)]=\"customEmoji\" appEmojiInput>\r\n                    <input type=\"text\" id=\"customActivityName\" placeholder=\"Activity name\" [(ngModel)]=\"customActivityName\">\r\n                </div>\r\n                <div class=\"time-inputs\">\r\n                    <input type=\"number\" id=\"customHoursInput\" min=\"0\" max=\"23\" [(ngModel)]=\"customHoursInput\">\r\n                    <span>h</span>\r\n                    <input type=\"number\" id=\"customMinutesInput\" min=\"0\" max=\"59\" [(ngModel)]=\"customMinutesInput\">\r\n                    <span>m</span>\r\n                </div>\r\n                <button (click)=\"addCustomActivity()\">Add</button>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"time-visualization\">\r\n            <canvas id=\"timeChart\"></canvas>\r\n            <div class=\"time-summary\">\r\n                <div class=\"total-time\">\r\n                    Total tracked: <span id=\"totalTracked\">{{ totalTrackedHours }}h</span>\r\n                </div>\r\n                <div class=\"remaining-time\">\r\n                    Remaining: <span id=\"remainingTime\">{{ remainingHours }}h</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"activity-list\">\r\n            <!-- Day view - editable -->\r\n            <ng-container *ngIf=\"viewMode === 'day'\">\r\n                <div *ngFor=\"let activity of activities\" class=\"activity-item\" [attr.data-id]=\"activity.id\">\r\n                    <div class=\"activity-name-emoji\" >\r\n                        <div class=\"activity-icon\">{{ activity.emoji }}</div>\r\n                        <h3 >{{ activity.name }}</h3>\r\n                    </div>\r\n                    <div class=\"activity-info\">\r\n\r\n                        <div class=\"time-input\">\r\n                            <input type=\"number\" min=\"0\" max=\"23\" [(ngModel)]=\"activity.hours\"\r\n                                   class=\"hours\" [attr.data-activity-id]=\"activity.id\" (change)=\"updateActivity(activity)\">\r\n                            <span>h</span>\r\n                            <input type=\"number\" min=\"0\" max=\"59\" [(ngModel)]=\"activity.minutes\"\r\n                                   class=\"minutes\" [attr.data-activity-id]=\"activity.id\" (change)=\"updateActivity(activity)\">\r\n                            <span>m</span>\r\n                        </div>\r\n                        <button class=\"delete-activity\" (click)=\"deleteActivity(activity.id)\" >×</button>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n\r\n            <!-- Week/Month view - read-only -->\r\n            <ng-container *ngIf=\"viewMode === 'week' || viewMode === 'month'\">\r\n                <div *ngFor=\"let activity of activities\" class=\"activity-item readonly\">\r\n                    <div class=\"activity-name-emoji\" >\r\n                        <div class=\"activity-icon\">{{ activity.emoji }}</div>\r\n                        <h3 >{{ activity.name }}</h3>\r\n                    </div>\r\n                    <div class=\"activity-info\">\r\n                        <div class=\"time-display\">\r\n                            <span class=\"time-value\">{{ activity.hours }}h {{ activity.minutes }}m</span>\r\n                            <span class=\"average-label\" *ngIf=\"viewMode === 'week'\">per day</span>\r\n                            <span class=\"average-label\" *ngIf=\"viewMode === 'month'\">per day</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n        </div>\r\n    </section>\r\n</div>\r\n\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";AAAA,SAAkEA,MAAM,QAAQ,eAAe;AAC/F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,6CAA6C;AACvF,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF;AACA,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,mBAAmB,QAAQ,wCAAwC;;;;;;;ICwB5DC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAnBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;;IAKxEN,EAAA,CAAAC,cAAA,cAKwD;IAAnDD,EAAA,CAAAO,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,EAAAN,OAAA,CAAAO,SAAA,IAA4BH,MAAA,CAAAI,UAAA,CAAAR,OAAA,CAAAS,IAAA,CAAqB;IAAA,EAAC;IACnDlB,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAHDH,EAFA,CAAAmB,WAAA,WAAAV,OAAA,CAAAW,QAAA,CAA8B,aAAAX,OAAA,CAAAY,WAAA,CACK,aAAAZ,OAAA,CAAAO,SAAA,CACF;IAElChB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAsB,kBAAA,MAAAb,OAAA,CAAAc,GAAA,MACJ;;;;;;IAdAvB,EAFR,CAAAC,cAAA,cAAsD,cACxB,iBACkC;IAAzBD,EAAA,CAAAO,UAAA,mBAAAiB,wDAAA;MAAAxB,EAAA,CAAAU,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAa,UAAA,EAAY,CAAC,CAAC;IAAA,EAAC;IAAC1B,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClEH,EAAA,CAAAC,cAAA,cAAkB;IACdD,EAAA,CAAA2B,UAAA,IAAAC,qCAAA,kBAAuD;IAC3D5B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAAuD;IAAxBD,EAAA,CAAAO,UAAA,mBAAAsB,wDAAA;MAAA7B,EAAA,CAAAU,aAAA,CAAAe,GAAA;MAAA,MAAAZ,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAa,UAAA,CAAW,CAAC,CAAC;IAAA,EAAC;IAAC1B,EAAA,CAAAE,MAAA,aAAC;IAC5DF,EAD4D,CAAAG,YAAA,EAAS,EAC/D;IACNH,EAAA,CAAAC,cAAA,cAAmB;IACfD,EAAA,CAAA2B,UAAA,IAAAG,qCAAA,kBAKwD;IAIhE9B,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAdgDH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAA+B,UAAA,YAAAlB,MAAA,CAAAmB,QAAA,CAAW;IAKnChC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAA+B,UAAA,YAAAlB,MAAA,CAAAoB,SAAA,CAAY;;;;;IAYtCjC,EAAA,CAAAC,cAAA,SAA+B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAC9CH,EAAA,CAAAC,cAAA,SAAgC;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACnDH,EAAA,CAAAC,cAAA,SAAiC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAK7CH,EAAA,CAAAC,cAAA,iBAAyH;IACrHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFkCH,EAAA,CAAA+B,UAAA,UAAAG,OAAA,CAAAC,EAAA,CAAiB;;IACxDnC,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAoC,kBAAA,MAAAF,OAAA,CAAAG,KAAA,OAAAH,OAAA,CAAAI,IAAA,MACJ;;;;;;IAJJtC,EADJ,CAAAC,cAAA,cAAuD,iBACoB;IAA3CD,EAAA,CAAAO,UAAA,oBAAAgC,yDAAAC,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUF,MAAA,CAAA6B,uBAAA,CAAAF,MAAA,CAA+B;IAAA,EAAC;IAClExC,EAAA,CAAAC,cAAA,iBAAiB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAA2B,UAAA,IAAAgB,wCAAA,qBAAyH;IAGzH3C,EAAA,CAAAC,cAAA,iBAAuB;IAAAD,EAAA,CAAAE,MAAA,6BAAiB;IAC5CF,EAD4C,CAAAG,YAAA,EAAS,EAC5C;IAKDH,EAFR,CAAAC,cAAA,cAAwF,cAC3D,gBAC0D;IAAzBD,EAAA,CAAA4C,gBAAA,2BAAAC,+DAAAL,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAAkC,UAAA,EAAAP,MAAA,MAAA3B,MAAA,CAAAkC,UAAA,GAAAP,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAAwB;IAA9ExC,EAAA,CAAAG,YAAA,EAA+E;IAC/EH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,iBAAmF;IAA3BD,EAAA,CAAA4C,gBAAA,2BAAAI,gEAAAR,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAAoC,YAAA,EAAAT,MAAA,MAAA3B,MAAA,CAAAoC,YAAA,GAAAT,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAA0B;IAAlFxC,EAAA,CAAAG,YAAA,EAAmF;IACnFH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACXF,EADW,CAAAG,YAAA,EAAO,EACZ;IACNH,EAAA,CAAAC,cAAA,kBAAgC;IAAxBD,EAAA,CAAAO,UAAA,mBAAA2C,yDAAA;MAAAlD,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAsC,WAAA,EAAa;IAAA,EAAC;IAACnD,EAAA,CAAAE,MAAA,WAAG;IACvCF,EADuC,CAAAG,YAAA,EAAS,EAC1C;IAKEH,EAFR,CAAAC,cAAA,eAA2G,eAC/E,iBAC+D;IAAxCD,EAAA,CAAA4C,gBAAA,2BAAAQ,gEAAAZ,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAAwC,WAAA,EAAAb,MAAA,MAAA3B,MAAA,CAAAwC,WAAA,GAAAb,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAAyB;IAApExC,EAAA,CAAAG,YAAA,EAAmF;IACnFH,EAAA,CAAAC,cAAA,iBAAwG;IAAjCD,EAAA,CAAA4C,gBAAA,2BAAAU,gEAAAd,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAA0C,kBAAA,EAAAf,MAAA,MAAA3B,MAAA,CAAA0C,kBAAA,GAAAf,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAAgC;IAC3GxC,EADI,CAAAG,YAAA,EAAwG,EACtG;IAEFH,EADJ,CAAAC,cAAA,eAAyB,iBACsE;IAA/BD,EAAA,CAAA4C,gBAAA,2BAAAY,gEAAAhB,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAA4C,gBAAA,EAAAjB,MAAA,MAAA3B,MAAA,CAAA4C,gBAAA,GAAAjB,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAA8B;IAA1FxC,EAAA,CAAAG,YAAA,EAA2F;IAC3FH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,iBAA+F;IAAjCD,EAAA,CAAA4C,gBAAA,2BAAAc,gEAAAlB,MAAA;MAAAxC,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8C,kBAAA,CAAAjC,MAAA,CAAA8C,kBAAA,EAAAnB,MAAA,MAAA3B,MAAA,CAAA8C,kBAAA,GAAAnB,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAAgC;IAA9FxC,EAAA,CAAAG,YAAA,EAA+F;IAC/FH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACXF,EADW,CAAAG,YAAA,EAAO,EACZ;IACNH,EAAA,CAAAC,cAAA,kBAAsC;IAA9BD,EAAA,CAAAO,UAAA,mBAAAqD,yDAAA;MAAA5D,EAAA,CAAAU,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgD,iBAAA,EAAmB;IAAA,EAAC;IAAC7D,EAAA,CAAAE,MAAA,WAAG;IAEjDF,EAFiD,CAAAG,YAAA,EAAS,EAChD,EACJ;;;;IA/B2BH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA+B,UAAA,YAAAlB,MAAA,CAAAiD,aAAA,CAAgB;IAOX9D,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAA+D,WAAA,YAAAlD,MAAA,CAAAmD,iBAAA,mBAAqD;IAEzBhE,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAAkC,UAAA,CAAwB;IAEtB/C,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAAoC,YAAA,CAA0B;IAOlCjD,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAA+D,WAAA,YAAAlD,MAAA,CAAAqD,cAAA,mBAAkD;IAEvDlE,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAAwC,WAAA,CAAyB;IACGrD,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAA0C,kBAAA,CAAgC;IAG3CvD,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAA4C,gBAAA,CAA8B;IAE5BzD,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAiE,gBAAA,YAAApD,MAAA,CAAA8C,kBAAA,CAAgC;;;;;;IAwB1F3D,EAFR,CAAAC,cAAA,cAA4F,cACtD,cACH;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC5BF,EAD4B,CAAAG,YAAA,EAAK,EAC3B;IAIEH,EAHR,CAAAC,cAAA,cAA2B,cAEC,gBAE2E;IADzDD,EAAA,CAAA4C,gBAAA,2BAAAuB,8EAAA3B,MAAA;MAAA,MAAA4B,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAAZ,EAAA,CAAA8C,kBAAA,CAAAsB,WAAA,CAAAE,KAAA,EAAA9B,MAAA,MAAA4B,WAAA,CAAAE,KAAA,GAAA9B,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAA4B;IACPxC,EAAA,CAAAO,UAAA,oBAAAgE,uEAAA;MAAA,MAAAH,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUF,MAAA,CAAA2D,cAAA,CAAAJ,WAAA,CAAwB;IAAA,EAAC;IAD9FpE,EAAA,CAAAG,YAAA,EAC+F;IAC/FH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACdH,EAAA,CAAAC,cAAA,iBACiG;IAD3DD,EAAA,CAAA4C,gBAAA,2BAAA6B,+EAAAjC,MAAA;MAAA,MAAA4B,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAAZ,EAAA,CAAA8C,kBAAA,CAAAsB,WAAA,CAAAM,OAAA,EAAAlC,MAAA,MAAA4B,WAAA,CAAAM,OAAA,GAAAlC,MAAA;MAAA,OAAAxC,EAAA,CAAAe,WAAA,CAAAyB,MAAA;IAAA,EAA8B;IACPxC,EAAA,CAAAO,UAAA,oBAAAoE,wEAAA;MAAA,MAAAP,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAUF,MAAA,CAAA2D,cAAA,CAAAJ,WAAA,CAAwB;IAAA,EAAC;IADhGpE,EAAA,CAAAG,YAAA,EACiG;IACjGH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACXF,EADW,CAAAG,YAAA,EAAO,EACZ;IACNH,EAAA,CAAAC,cAAA,kBAAuE;IAAvCD,EAAA,CAAAO,UAAA,mBAAAqE,wEAAA;MAAA,MAAAR,WAAA,GAAApE,EAAA,CAAAU,aAAA,CAAA2D,GAAA,EAAAzD,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgE,cAAA,CAAAT,WAAA,CAAAjC,EAAA,CAA2B;IAAA,EAAC;IAAEnC,EAAA,CAAAE,MAAA,cAAC;IAEhFF,EAFgF,CAAAG,YAAA,EAAS,EAC/E,EACJ;;;;;IAf6BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA+D,WAAA,CAAA/B,KAAA,CAAoB;IAC1CrC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA+D,WAAA,CAAA9B,IAAA,CAAmB;IAKkBtC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAiE,gBAAA,YAAAG,WAAA,CAAAE,KAAA,CAA4B;;IAG5BtE,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAiE,gBAAA,YAAAG,WAAA,CAAAM,OAAA,CAA8B;;;;;;IAZpF1E,EAAA,CAAA8E,uBAAA,GAAyC;IACrC9E,EAAA,CAAA2B,UAAA,IAAAoD,8CAAA,mBAA4F;;;;;IAAlE/E,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAA+B,UAAA,YAAAlB,MAAA,CAAAmE,UAAA,CAAa;;;;;IA8B3BhF,EAAA,CAAAC,cAAA,eAAwD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtEH,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAP3EH,EAFR,CAAAC,cAAA,cAAwE,cAClC,cACH;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC5BF,EAD4B,CAAAG,YAAA,EAAK,EAC3B;IAGEH,EAFR,CAAAC,cAAA,cAA2B,cACG,eACG;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7EH,EADA,CAAA2B,UAAA,KAAAsD,sDAAA,mBAAwD,KAAAC,sDAAA,mBACC;IAGrElF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;;IAV6BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAA8E,YAAA,CAAA9C,KAAA,CAAoB;IAC1CrC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA8E,YAAA,CAAA7C,IAAA,CAAmB;IAIKtC,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAoC,kBAAA,KAAA+C,YAAA,CAAAb,KAAA,QAAAa,YAAA,CAAAT,OAAA,MAA6C;IACzC1E,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAA+B,UAAA,SAAAlB,MAAA,CAAAuE,QAAA,YAAyB;IACzBpF,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAA+B,UAAA,SAAAlB,MAAA,CAAAuE,QAAA,aAA0B;;;;;IAVvEpF,EAAA,CAAA8E,uBAAA,GAAkE;IAC9D9E,EAAA,CAAA2B,UAAA,IAAA0D,8CAAA,mBAAwE;;;;;IAA9CrF,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAA+B,UAAA,YAAAlB,MAAA,CAAAmE,UAAA,CAAa;;;ADpHvDpF,KAAK,CAAC0F,QAAQ,CAAC,GAAGzF,aAAa,CAAC;AAiBhC,OAAM,MAAO0F,eAAe;EA4C1BC,YAAA;IA3CA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAL,QAAQ,GAA6B,KAAK;IAE1C;IACA,KAAApD,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5D,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAyD,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAS,IAAIC,IAAI,EAAE;IAE/B;IACA,KAAA9B,aAAa,GAAmB,EAAE;IAClC,KAAAkB,UAAU,GAAe,EAAE;IAC3B,KAAAa,cAAc,GAAe,EAAE;IAC/B,KAAAC,eAAe,GAAe,EAAE;IAEhC;IACA,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAhD,UAAU,GAAG,CAAC;IACd,KAAAE,YAAY,GAAG,CAAC;IAChB,KAAAM,kBAAkB,GAAG,EAAE;IACvB,KAAAF,WAAW,GAAG,GAAG;IACjB,KAAAI,gBAAgB,GAAG,CAAC;IACpB,KAAAE,kBAAkB,GAAG,CAAC;IAEtB;IACA,KAAAK,iBAAiB,GAAG,KAAK;IACzB,KAAAE,cAAc,GAAG,KAAK;IAEtB;IACA,KAAA8B,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,MAAM;IAEvB;IACA,KAAAC,SAAS,GAAiB,IAAI;IAItB,KAAAC,eAAe,GAAG/G,MAAM,CAACU,eAAe,CAAC;IACzC,KAAAsG,kBAAkB,GAAGhH,MAAM,CAACK,yBAAyB,CAAC;EAE/C;EAEf4G,QAAQA,CAAA;IACN,IAAI,CAACF,eAAe,CAACG,YAAY,CAACC,IAAI,CACpC7G,IAAI,CAAC,CAAC,CAAC,CACR,CAAC8G,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAChB,MAAM,GAAGgB,QAAQ,CAACtE,EAAE;QACzB,IAAI,CAACuE,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,sBAAsB,EAAE;MAC/B;IACF,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAL,iBAAiBA,CAAA;IACf,IAAI,CAACN,kBAAkB,CAACY,gBAAgB,EAAE,CAACR,SAAS,CAAES,KAAqB,IAAI;MAC7E,IAAI,CAACnD,aAAa,GAAGmD,KAAK;IAC5B,CAAC,CAAC;EACJ;EAEAN,iBAAiBA,CAAA;IACf,MAAMO,KAAK,GAAG,IAAItB,IAAI,EAAE;IACxB,MAAMuB,UAAU,GAAGD,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC;IAEnC;IACA;IACA,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,MAAMG,WAAW,GAAG,IAAI1B,IAAI,CAACsB,KAAK,CAAC;IACnCI,WAAW,CAACC,OAAO,CAACL,KAAK,CAACM,OAAO,EAAE,GAAGL,UAAU,GAAGE,YAAY,GAAI,CAAC,GAAG,IAAI,CAAC3B,UAAW,CAAC;IAExF,IAAI,CAACzD,SAAS,GAAG,EAAE;IAEnB,KAAK,IAAIwF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMvG,IAAI,GAAG,IAAI0E,IAAI,CAAC0B,WAAW,CAAC;MAClCpG,IAAI,CAACqG,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAGC,CAAC,CAAC;MAEvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACzG,IAAI,CAAC;MACrC,MAAM0G,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC3G,IAAI,EAAEgG,KAAK,CAAC;MAC3C,MAAMY,UAAU,GAAG,IAAI,CAACD,SAAS,CAAC3G,IAAI,EAAE,IAAI,CAACyE,YAAY,CAAC;MAC1D,MAAMoC,QAAQ,GAAG7G,IAAI,GAAGgG,KAAK;MAE7B,IAAI,CAACjF,SAAS,CAAC+F,IAAI,CAAC;QAClB9G,IAAI,EAAEwG,OAAO;QACbnG,GAAG,EAAEL,IAAI,CAACsG,OAAO,EAAE;QACnBpG,QAAQ,EAAEwG,OAAO;QACjBvG,WAAW,EAAEyG,UAAU;QACvB9G,SAAS,EAAE+G;OACZ,CAAC;IACJ;EACF;EAEAJ,UAAUA,CAACzG,IAAU;IACnB,MAAM+G,IAAI,GAAG/G,IAAI,CAACgH,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAAClH,IAAI,CAACmH,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAM/G,GAAG,GAAG6G,MAAM,CAAClH,IAAI,CAACsG,OAAO,EAAE,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAI5G,GAAG,EAAE;EAClC;EAEAsG,SAASA,CAACU,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACL,WAAW,EAAE,KAAKM,KAAK,CAACN,WAAW,EAAE,IAC3CK,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACf,OAAO,EAAE,KAAKgB,KAAK,CAAChB,OAAO,EAAE;EAC5C;EAEA9F,UAAUA,CAAC+G,MAAc;IACvB,IAAI,CAAC/C,UAAU,IAAI+C,MAAM;IACzB,IAAI,CAAC9B,iBAAiB,EAAE;EAC1B;EAEA1F,UAAUA,CAACyG,OAAe;IACxB,IAAI,CAAC/B,YAAY,GAAG,IAAIC,IAAI,CAAC8B,OAAO,CAAC;IACrC,IAAI,CAACf,iBAAiB,EAAE;IACxB,IAAI,CAAC+B,cAAc,EAAE;EACvB;EAEA;EACAC,WAAWA,CAACC,IAA8B;IACxC,IAAI,CAACxD,QAAQ,GAAGwD,IAAI;IACpB,IAAI,CAAChC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACnB,MAAM,EAAE;IAElB,QAAQ,IAAI,CAACL,QAAQ;MACnB,KAAK,KAAK;QACR,IAAI,CAACsD,cAAc,EAAE;QACrB;MACF,KAAK,MAAM;QACT,IAAI,CAACG,kBAAkB,EAAE;QACzB;MACF,KAAK,OAAO;QACV,IAAI,CAACC,mBAAmB,EAAE;QAC1B;IACJ;EACF;EAEAD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACpD,MAAM,EAAE;IAElB,MAAM6B,WAAW,GAAG,IAAI,CAACyB,cAAc,CAAC,IAAI,CAACpD,YAAY,CAAC;IAC1D,MAAMqD,SAAS,GAAG,IAAIpD,IAAI,CAAC0B,WAAW,CAAC;IACvC0B,SAAS,CAACzB,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACpB,kBAAkB,CAAC6C,yBAAyB,CAC/C,IAAI,CAACxD,MAAM,EACX,IAAI,CAACkC,UAAU,CAACL,WAAW,CAAC,EAC5B,IAAI,CAACK,UAAU,CAACqB,SAAS,CAAC,CAC3B,CAACxC,SAAS,CAAExB,UAAsB,IAAI;MACrC,IAAI,CAACa,cAAc,GAAG,IAAI,CAACqD,kCAAkC,CAAClE,UAAU,CAAC;MACzE,IAAI,CAACA,UAAU,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC;MACvC,IAAI,CAACsD,eAAe,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEAN,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACrD,MAAM,EAAE;IAElB,MAAM4D,YAAY,GAAG,IAAIzD,IAAI,CAAC,IAAI,CAACD,YAAY,CAACuC,WAAW,EAAE,EAAE,IAAI,CAACvC,YAAY,CAAC0C,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC/F,MAAMiB,UAAU,GAAG,IAAI1D,IAAI,CAAC,IAAI,CAACD,YAAY,CAACuC,WAAW,EAAE,EAAE,IAAI,CAACvC,YAAY,CAAC0C,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAEjG,IAAI,CAACjC,kBAAkB,CAAC6C,yBAAyB,CAC/C,IAAI,CAACxD,MAAM,EACX,IAAI,CAACkC,UAAU,CAAC0B,YAAY,CAAC,EAC7B,IAAI,CAAC1B,UAAU,CAAC2B,UAAU,CAAC,CAC5B,CAAC9C,SAAS,CAAExB,UAAsB,IAAI;MACrC,IAAI,CAACc,eAAe,GAAG,IAAI,CAACoD,kCAAkC,CAAClE,UAAU,CAAC;MAC1E,IAAI,CAACA,UAAU,GAAG,IAAI,CAACc,eAAe,CAAC,CAAC;MACxC,IAAI,CAACqD,eAAe,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEAL,cAAcA,CAAC7H,IAAU;IACvB,MAAMiG,UAAU,GAAGjG,IAAI,CAACkG,MAAM,EAAE,CAAC,CAAC;IAClC,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,MAAMG,WAAW,GAAG,IAAI1B,IAAI,CAAC1E,IAAI,CAAC;IAClCoG,WAAW,CAACC,OAAO,CAACrG,IAAI,CAACsG,OAAO,EAAE,GAAGL,UAAU,GAAGE,YAAY,GAAI,CAAC,GAAG,IAAI,CAAC3B,UAAW,CAAC;IACvF,OAAO4B,WAAW;EACpB;EAEA4B,kCAAkCA,CAAClE,UAAsB;IACvD;IACA,MAAMuE,cAAc,GAAqE,EAAE;IAE3FvE,UAAU,CAACwE,OAAO,CAACC,QAAQ,IAAG;MAC5B,MAAMC,GAAG,GAAG,GAAGD,QAAQ,CAACnH,IAAI,IAAImH,QAAQ,CAACpH,KAAK,EAAE;MAChD,IAAI,CAACkH,cAAc,CAACG,GAAG,CAAC,EAAE;QACxBH,cAAc,CAACG,GAAG,CAAC,GAAG;UAAE1E,UAAU,EAAE,EAAE;UAAE2E,IAAI,EAAE,IAAIC,GAAG;QAAE,CAAE;MAC3D;MACAL,cAAc,CAACG,GAAG,CAAC,CAAC1E,UAAU,CAACgD,IAAI,CAACyB,QAAQ,CAAC;MAC7C;MACA,IAAIA,QAAQ,CAACvI,IAAI,EAAE;QACjBqI,cAAc,CAACG,GAAG,CAAC,CAACC,IAAI,CAACE,GAAG,CAACJ,QAAQ,CAACvI,IAAI,CAAC;MAC7C,CAAC,MAAM;QACL;QACAqI,cAAc,CAACG,GAAG,CAAC,CAACC,IAAI,CAACE,GAAG,CAACJ,QAAQ,CAACK,eAAe,CAAC;MACxD;IACF,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAAe,EAAE;IAExCC,MAAM,CAACC,IAAI,CAACV,cAAc,CAAC,CAACC,OAAO,CAACE,GAAG,IAAG;MACxC,MAAMQ,KAAK,GAAGX,cAAc,CAACG,GAAG,CAAC;MACjC,MAAMS,YAAY,GAAGD,KAAK,CAAClF,UAAU,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEZ,QAAQ,KAAI;QAC7D,OAAOY,GAAG,GAAIZ,QAAQ,CAACnF,KAAK,GAAG,EAAG,GAAGmF,QAAQ,CAAC/E,OAAO;MACvD,CAAC,EAAE,CAAC,CAAC;MAEL;MACA,MAAM4F,gBAAgB,GAAGJ,KAAK,CAACP,IAAI,CAACY,IAAI;MACxC,MAAMC,cAAc,GAAGL,YAAY,GAAGG,gBAAgB;MACtD,MAAMG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,cAAc,GAAG,EAAE,CAAC;MACpD,MAAMI,gBAAgB,GAAGF,IAAI,CAACG,KAAK,CAACL,cAAc,GAAG,EAAE,CAAC;MAExD,IAAIC,YAAY,GAAG,CAAC,IAAIG,gBAAgB,GAAG,CAAC,EAAE;QAC5Cb,iBAAiB,CAAC/B,IAAI,CAAC;UACrB7F,EAAE,EAAE+H,KAAK,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC7C,EAAE;UAC1B2H,eAAe,EAAEI,KAAK,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC8E,eAAe;UACpDxH,IAAI,EAAE4H,KAAK,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC1C,IAAI;UAC9BD,KAAK,EAAE6H,KAAK,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC3C,KAAK;UAChCiC,KAAK,EAAEmG,YAAY;UACnB/F,OAAO,EAAEkG,gBAAgB;UACzBE,SAAS,EAAEZ,KAAK,CAAClF,UAAU,CAAC,CAAC,CAAC,CAAC8F;SAChC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOf,iBAAiB;EAC1B;EAEArB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjD,MAAM,EAAE;IAElB,MAAMiC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAChC,YAAY,CAAC;IAClD,MAAMF,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;IAE5B;IACA,IAAI,CAACW,kBAAkB,CAAC2E,cAAc,CAACtF,MAAM,EAAEiC,OAAO,CAAC,CAAClB,SAAS,CAAC,MAAK;MACrE;MACA,IAAI,CAACJ,kBAAkB,CAAC4E,aAAa,CAACvF,MAAM,EAAEiC,OAAO,CAAC,CAAClB,SAAS,CAAExB,UAAsB,IAAI;QAC1F,IAAI,CAACA,UAAU,GAAGA,UAAU;QAE5B;QACA,IAAI,CAACmE,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAD,eAAeA,CAAA;IACb;IACA,MAAM8B,UAAU,GAAG,IAAI,CAACjG,UAAU,CAACoF,MAAM,CAAC,CAACc,KAAK,EAAEzB,QAAQ,KAAI;MAC5D,OAAOyB,KAAK,GAAGzB,QAAQ,CAACnF,KAAK,GAAImF,QAAQ,CAAC/E,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI,CAACsB,iBAAiB,GAAGiF,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC;IAE9C;IACA,MAAMlF,cAAc,GAAGyE,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;IACnD,IAAI,CAAChF,cAAc,GAAGA,cAAc,CAACkF,OAAO,CAAC,CAAC,CAAC;EAEjD;EAEAzI,uBAAuBA,CAAC2I,KAAY;IAClC,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAA2B;IAChD,MAAMC,KAAK,GAAGF,MAAM,CAACE,KAAK;IAE1B,IAAI,CAACzF,kBAAkB,GAAGyF,KAAK;IAC/B,IAAI,CAACxH,iBAAiB,GAAGwH,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,QAAQ;IAC3D,IAAI,CAACtH,cAAc,GAAGsH,KAAK,KAAK,QAAQ;EAC1C;EAEArI,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACsC,MAAM,IAAI,CAAC,IAAI,CAACM,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,KAAK,QAAQ,EAAE;IACtF,IAAI,IAAI,CAAChD,UAAU,KAAK,CAAC,IAAI,IAAI,CAACE,YAAY,KAAK,CAAC,EAAE;MACpDwI,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAAC3I,UAAU,EAAE,IAAI,CAACE,YAAY,CAAC,EAAE;MAC/DwI,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA;IACA,MAAME,YAAY,GAAG,IAAI,CAAC7H,aAAa,CAAC8H,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC1J,EAAE,KAAK,IAAI,CAAC4D,kBAAkB,CAAC;IACzF,IAAI,CAAC4F,YAAY,EAAE;IAEnB;IACA,MAAMG,gBAAgB,GAAG,IAAI,CAAC9G,UAAU,CAAC4G,IAAI,CAACnC,QAAQ,IAAIA,QAAQ,CAACnH,IAAI,KAAKqJ,YAAY,CAACrJ,IAAI,CAAC;IAE9F,IAAIwJ,gBAAgB,EAAE;MACpB;MACA,MAAMC,YAAY,GAAGD,gBAAgB,CAACxH,KAAK,GAAG,IAAI,CAACvB,UAAU;MAC7D,MAAMiJ,cAAc,GAAGF,gBAAgB,CAACpH,OAAO,GAAG,IAAI,CAACzB,YAAY;MAEnE;MACA,IAAIgJ,UAAU,GAAGF,YAAY,GAAGrB,IAAI,CAACC,KAAK,CAACqB,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIE,YAAY,GAAGF,cAAc,GAAG,EAAE;MAEtC;MACA,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfC,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAAC9F,kBAAkB,CAAC5B,cAAc,CAACsH,gBAAgB,CAAC3J,EAAE,EAAE8J,UAAU,EAAEC,YAAY,CAAC,CAACC,IAAI,CAAC,MAAK;QAC9F;QACAL,gBAAgB,CAACxH,KAAK,GAAG2H,UAAU;QACnCH,gBAAgB,CAACpH,OAAO,GAAGwH,YAAY;QAEvC;QACA,IAAI,CAACE,WAAW,EAAE;QAElB;QACA,IAAI,CAACjD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDb,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMhG,MAAM,GAAG,IAAI,CAACA,MAAgB,CAAC,CAAC;MACtC,MAAMiC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAChC,YAAY,CAAC;MAElD,IAAI,CAACS,kBAAkB,CAACoG,cAAc,CACpC/G,MAAM,EACNiC,OAAO,EACPiE,YAAY,CAACrJ,IAAI,EACjBqJ,YAAY,CAACtJ,KAAK,EAClB,IAAI,CAACU,UAAU,EACf,IAAI,CAACE,YAAY,EACjB,KAAK,CACN,CAACkJ,IAAI,CAAEM,MAAM,IAAI;QAChB;QACA;QACA,IAAI,CAACzH,UAAU,CAACgD,IAAI,CAAC;UACnB7F,EAAE,EAAEsK,MAAM,CAACtK,EAAE;UACb2H,eAAe,EAAE,EAAE;UAAE;UACrBxH,IAAI,EAAEqJ,YAAY,CAACrJ,IAAI;UACvBD,KAAK,EAAEsJ,YAAY,CAACtJ,KAAK;UACzBiC,KAAK,EAAE,IAAI,CAACvB,UAAU;UACtB2B,OAAO,EAAE,IAAI,CAACzB,YAAY;UAC1B6H,SAAS,EAAE;SACZ,CAAC;QAEF;QACA,IAAI,CAACsB,WAAW,EAAE;QAElB;QACA,IAAI,CAACjD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDb,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEA5H,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC4B,MAAM,IAAI,CAAC,IAAI,CAAClC,kBAAkB,CAACmJ,IAAI,EAAE,EAAE;MACnDjB,KAAK,CAAC,+BAA+B,CAAC;MACtC;IACF;IAEA,IAAI,IAAI,CAAChI,gBAAgB,KAAK,CAAC,IAAI,IAAI,CAACE,kBAAkB,KAAK,CAAC,EAAE;MAChE8H,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACjI,gBAAgB,EAAE,IAAI,CAACE,kBAAkB,CAAC,EAAE;MAC3E8H,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA;IACA,MAAMK,gBAAgB,GAAG,IAAI,CAAC9G,UAAU,CAAC4G,IAAI,CAACnC,QAAQ,IACpDA,QAAQ,CAACnH,IAAI,CAACqK,WAAW,EAAE,KAAK,IAAI,CAACpJ,kBAAkB,CAACmJ,IAAI,EAAE,CAACC,WAAW,EAAE,CAC7E;IAED,IAAIb,gBAAgB,EAAE;MACpB;MACA,MAAMC,YAAY,GAAGD,gBAAgB,CAACxH,KAAK,GAAG,IAAI,CAACb,gBAAgB;MACnE,MAAMuI,cAAc,GAAGF,gBAAgB,CAACpH,OAAO,GAAG,IAAI,CAACf,kBAAkB;MAEzE;MACA,IAAIsI,UAAU,GAAGF,YAAY,GAAGrB,IAAI,CAACC,KAAK,CAACqB,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIE,YAAY,GAAGF,cAAc,GAAG,EAAE;MAEtC;MACA,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfC,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAAC9F,kBAAkB,CAAC5B,cAAc,CAACsH,gBAAgB,CAAC3J,EAAE,EAAE8J,UAAU,EAAEC,YAAY,CAAC,CAACC,IAAI,CAAC,MAAK;QAC9F;QACAL,gBAAgB,CAACxH,KAAK,GAAG2H,UAAU;QACnCH,gBAAgB,CAACpH,OAAO,GAAGwH,YAAY;QAEvC;QACA,IAAI,CAACE,WAAW,EAAE;QAElB;QACA,IAAI,CAACjD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDb,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMhG,MAAM,GAAG,IAAI,CAACA,MAAgB,CAAC,CAAC;MACtC,MAAMiC,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAChC,YAAY,CAAC;MAElD,IAAI,CAACS,kBAAkB,CAACoG,cAAc,CACpC/G,MAAM,EACNiC,OAAO,EACP,IAAI,CAACnE,kBAAkB,CAACmJ,IAAI,EAAE,EAC9B,IAAI,CAACrJ,WAAW,EAChB,IAAI,CAACI,gBAAgB,EACrB,IAAI,CAACE,kBAAkB,EACvB,IAAI,CACL,CAACwI,IAAI,CAAEM,MAAM,IAAI;QAChB;QACA;QACA,IAAI,CAACzH,UAAU,CAACgD,IAAI,CAAC;UACnB7F,EAAE,EAAEsK,MAAM,CAACtK,EAAE;UACb2H,eAAe,EAAE,EAAE;UAAE;UACrBxH,IAAI,EAAE,IAAI,CAACiB,kBAAkB,CAACmJ,IAAI,EAAE;UACpCrK,KAAK,EAAE,IAAI,CAACgB,WAAW;UACvBiB,KAAK,EAAE,IAAI,CAACb,gBAAgB;UAC5BiB,OAAO,EAAE,IAAI,CAACf,kBAAkB;UAChCmH,SAAS,EAAE;SACZ,CAAC;QAEF;QACA,IAAI,CAACsB,WAAW,EAAE;QAElB;QACA,IAAI,CAACjD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDb,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAjH,cAAcA,CAACiF,QAAkB;IAC/B,IAAI,CAACA,QAAQ,CAACtH,EAAE,EAAE;IAElB;IACA,IAAIsH,QAAQ,CAACnF,KAAK,GAAG,CAAC,EAAEmF,QAAQ,CAACnF,KAAK,GAAG,CAAC;IAC1C,IAAImF,QAAQ,CAAC/E,OAAO,GAAG,CAAC,EAAE+E,QAAQ,CAAC/E,OAAO,GAAG,CAAC;IAC9C,IAAI+E,QAAQ,CAACnF,KAAK,GAAG,EAAE,EAAEmF,QAAQ,CAACnF,KAAK,GAAG,EAAE;IAC5C,IAAImF,QAAQ,CAAC/E,OAAO,GAAG,EAAE,EAAE+E,QAAQ,CAAC/E,OAAO,GAAG,EAAE;IAEhD;IACA,MAAMkI,YAAY,GAAGnD,QAAQ,CAACnF,KAAK;IACnC,MAAMuI,cAAc,GAAGpD,QAAQ,CAAC/E,OAAO;IAEvC;IACA,MAAMoI,mBAAmB,GAAG,IAAI,CAAC9H,UAAU,CACxC+H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7K,EAAE,KAAKsH,QAAQ,CAACtH,EAAE,CAAC,CACjCiI,MAAM,CAAC,CAACc,KAAK,EAAE8B,CAAC,KAAK9B,KAAK,GAAG8B,CAAC,CAAC1I,KAAK,GAAI0I,CAAC,CAACtI,OAAO,GAAG,EAAG,EAAE,CAAC,CAAC;IAE9D;IACA,MAAMuI,SAAS,GAAGH,mBAAmB,GAAGF,YAAY,GAAIC,cAAc,GAAG,EAAG;IAE5E,IAAII,SAAS,GAAG,EAAE,EAAE;MAClBxB,KAAK,CAAC,mCAAmC,CAAC;MAC1C;MACA,IAAI,CAAC/C,cAAc,EAAE;MACrB;IACF;IAEA,IAAI,CAACtC,kBAAkB,CAAC5B,cAAc,CAACiF,QAAQ,CAACtH,EAAE,EAAEsH,QAAQ,CAACnF,KAAK,EAAEmF,QAAQ,CAAC/E,OAAO,CAAC,CAACyH,IAAI,CAAC,MAAK;MAC9F;MACA,IAAI,CAAChD,eAAe,EAAE;MAEtB;MACA,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;MACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDb,KAAK,CAAC,4CAA4C,CAAC;MACnD,IAAI,CAAC/C,cAAc,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA7D,cAAcA,CAACqI,UAAkB;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,IAAI,CAAC9G,kBAAkB,CAACvB,cAAc,CAACqI,UAAU,CAAC,CAACf,IAAI,CAAC,MAAK;MAC3D;MACA,IAAI,CAACnH,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC+H,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7K,EAAE,KAAK+K,UAAU,CAAC;MAElE;MACA,IAAI,CAAC/D,eAAe,EAAE;MAEtB;MACA,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAACiD,KAAK,CAACC,KAAK,IAAG;MACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDb,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,CAAC;EACJ;EAEAW,WAAWA,CAAA;IACT;IACA,MAAMe,YAAY,GAAG,IAAI,CAAC9J,WAAW;IAErC;IACA,IAAI,CAAC0C,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAAChD,UAAU,GAAG,CAAC;IACnB,IAAI,CAACE,YAAY,GAAG,CAAC;IACrB,IAAI,CAACM,kBAAkB,GAAG,EAAE;IAC5B;IACA;IACA,IAAI,CAACF,WAAW,GAAG8J,YAAY,IAAI,GAAG;IACtC,IAAI,CAAC1J,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACE,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACK,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACE,cAAc,GAAG,KAAK;IAE3B;IACA4C,UAAU,CAAC,MAAK;MACd,MAAMsG,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAsB;MACpF,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAAC5B,KAAK,GAAG,EAAE;QAExB;QACA,IAAI,CAACzF,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAAC/B,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACE,cAAc,GAAG,KAAK;MAC7B;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACA;EACAqJ,YAAYA,CAAA;IACV;EAAA;EAGF7B,iBAAiBA,CAACpH,KAAa,EAAEI,OAAe;IAC9C;IACA,MAAM8I,YAAY,GAAG,IAAI,CAACxI,UAAU,CAACoF,MAAM,CAAC,CAACc,KAAK,EAAEzB,QAAQ,KAAI;MAC9D,OAAOyB,KAAK,GAAGzB,QAAQ,CAACnF,KAAK,GAAImF,QAAQ,CAAC/E,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,MAAMuI,SAAS,GAAGO,YAAY,GAAGlJ,KAAK,GAAII,OAAO,GAAG,EAAG;IAEvD;IACA,OAAOuI,SAAS,IAAI,EAAE;EACxB;EAEAlG,eAAeA,CAAA;IACb,MAAM0G,MAAM,GAAGJ,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAsB;IACxE,IAAI,CAACG,MAAM,EAAE;IAEb,MAAMC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV;IACA,IAAI,IAAI,CAACxH,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAAC0H,OAAO,EAAE;MACxB,IAAI,CAAC1H,SAAS,GAAG,IAAI;IACvB;IAEA;IACAtG,KAAK,CAAC0F,QAAQ,CAAC;MACbnD,EAAE,EAAE,YAAY;MAChB0L,UAAU,EAAGC,KAAU,IAAI;QACzB,MAAMJ,GAAG,GAAGI,KAAK,CAACJ,GAAG;QACrB,MAAMK,KAAK,GAAGD,KAAK,CAACC,KAAK;QACzB,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;QAE3BN,GAAG,CAACO,OAAO,EAAE;QACbP,GAAG,CAACQ,IAAI,GAAG,iBAAiB;QAC5BR,GAAG,CAACS,SAAS,GAAG,QAAQ;QACxBT,GAAG,CAACU,YAAY,GAAG,QAAQ;QAE3B;QACAV,GAAG,CAACW,SAAS,GAAG,SAAS;QACzBX,GAAG,CAACY,QAAQ,CAAC,KAAK,EAAEP,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC;QAC1CN,GAAG,CAACa,IAAI,EAAE;MACZ;KACD,CAAC;IAEF,IAAI;MACF,IAAI,CAACrI,SAAS,GAAG,IAAItG,KAAK,CAAC8N,GAAG,EAAE;QAC9B7B,IAAI,EAAE,UAAU;QAChB2C,IAAI,EAAE;UACJC,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,CAAC;YACTF,IAAI,EAAE,EAAE;YACRG,eAAe,EAAE,EAAE;YACnBC,WAAW,EAAE,CAAC;YACdC,YAAY,EAAE,CAAC;YACfC,OAAO,EAAE;WACV;SACF;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;aACV;YACDC,OAAO,EAAE;cACPC,OAAO,EAAE,IAAI;cACbC,SAAS,EAAE;gBACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;kBAC1B,MAAMjE,KAAK,GAAGiE,OAAO,CAACC,GAAa;kBACnC,MAAMpL,KAAK,GAAGoG,IAAI,CAACC,KAAK,CAACa,KAAK,CAAC;kBAC/B,MAAM9G,OAAO,GAAGgG,IAAI,CAACG,KAAK,CAAC,CAACW,KAAK,GAAGlH,KAAK,IAAI,EAAE,CAAC;kBAChD,OAAO,GAAGA,KAAK,KAAKI,OAAO,GAAG;gBAChC;;;WAGL;UACDiL,SAAS,EAAE;YACTC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE;;;OAGnB,CAAC;MAEF,IAAI,CAACzG,WAAW,EAAE;IACpB,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF;EAEAlD,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAClD,SAAS,EAAE;MACnB,IAAI,CAACa,eAAe,EAAE;MACtB;IACF;IAEA,IAAI;MACF,MAAMyH,IAAI,GAAG,IAAI,CAACsB,yBAAyB,EAAE;MAE7C,IAAI,CAAC5J,SAAS,CAACsI,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxC,IAAI,CAACvI,SAAS,CAACsI,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,GAAGA,IAAI,CAACuB,MAAM;MAClD,IAAI,CAAC7J,SAAS,CAACsI,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACC,eAAe,GAAG,IAAI,CAACqB,cAAc,CAACxB,IAAI,CAACC,MAAM,CAACwB,MAAM,CAAC;MAEzF,IAAI,CAAC/J,SAAS,CAACgK,MAAM,EAAE;IACzB,CAAC,CAAC,OAAO5D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D;MACA,IAAI,CAACpG,SAAS,GAAG,IAAI;MACrBY,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEA+I,yBAAyBA,CAAA;IACvB,MAAMrB,MAAM,GAAa,EAAE;IAC3B,MAAMsB,MAAM,GAAa,EAAE;IAE3B,IAAI,CAAC/K,UAAU,CAACwE,OAAO,CAACC,QAAQ,IAAG;MACjC;MACAgF,MAAM,CAACzG,IAAI,CAAC,GAAGyB,QAAQ,CAACpH,KAAK,IAAIoH,QAAQ,CAACnH,IAAI,EAAE,CAAC;MACjDyN,MAAM,CAAC/H,IAAI,CAACyB,QAAQ,CAACnF,KAAK,GAAImF,QAAQ,CAAC/E,OAAO,GAAG,EAAG,CAAC;IACvD,CAAC,CAAC;IAEF;IACA,MAAMuG,UAAU,GAAG8E,MAAM,CAAC3F,MAAM,CAAC,CAAC4C,CAAC,EAAEmD,CAAC,KAAKnD,CAAC,GAAGmD,CAAC,EAAE,CAAC,CAAC;IACpD,IAAIlF,UAAU,GAAG,EAAE,EAAE;MACnBwD,MAAM,CAACzG,IAAI,CAAC,WAAW,CAAC;MACxB+H,MAAM,CAAC/H,IAAI,CAAC,EAAE,GAAGiD,UAAU,CAAC;IAC9B;IAEA,OAAO;MAAEwD,MAAM;MAAEsB;IAAM,CAAE;EAC3B;EAEAC,cAAcA,CAACI,KAAa;IAC1B,MAAMC,MAAM,GAAa,EAAE;IAE3B;IACA,IAAI,CAACrL,UAAU,CAACwE,OAAO,CAACC,QAAQ,IAAG;MACjC;MACA,MAAMkC,YAAY,GAAG,IAAI,CAAC7H,aAAa,CAAC8H,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACvJ,IAAI,KAAKmH,QAAQ,CAACnH,IAAI,CAAC;MAEjF;MACA,IAAIqJ,YAAY,IAAIA,YAAY,CAAC2E,KAAK,EAAE;QACtCD,MAAM,CAACrI,IAAI,CAAC2D,YAAY,CAAC2E,KAAK,CAAC;MACjC,CAAC,MAAM,IAAI7G,QAAQ,CAACqB,SAAS,EAAE;QAC7B;QACAuF,MAAM,CAACrI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL;QACAqI,MAAM,CAACrI,IAAI,CAAC,SAAS,CAAC;MACxB;IACF,CAAC,CAAC;IAEF;IACA,IAAIoI,KAAK,GAAGC,MAAM,CAACJ,MAAM,EAAE;MACzBI,MAAM,CAACrI,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA,OAAOqI,MAAM;EACf;;mBA9tBW9K,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA,gBAAe;EAAAgL,SAAA;EAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAhD,GAAA;IAAA,IAAAgD,EAAA;;;;;;;;;;;;;MC5BpB1Q,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAA2Q,SAAA,aAA6D;MAC7D3Q,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,mBAAY;MACpBF,EADoB,CAAAG,YAAA,EAAK,EAChB;MAILH,EADJ,CAAAC,cAAA,aAA+B,gBAIQ;MAA/BD,EAAA,CAAAO,UAAA,mBAAAqQ,iDAAA;QAAA,OAASlD,GAAA,CAAA/E,WAAA,CAAY,OAAO,CAAC;MAAA,EAAC;MAC9B3I,EAAA,CAAAE,MAAA,eACJ;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAAC,cAAA,iBAGkC;MAA9BD,EAAA,CAAAO,UAAA,mBAAAsQ,kDAAA;QAAA,OAASnD,GAAA,CAAA/E,WAAA,CAAY,MAAM,CAAC;MAAA,EAAC;MAC7B3I,EAAA,CAAAE,MAAA,cACJ;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAAC,cAAA,iBAGiC;MAA7BD,EAAA,CAAAO,UAAA,mBAAAuQ,kDAAA;QAAA,OAASpD,GAAA,CAAA/E,WAAA,CAAY,KAAK,CAAC;MAAA,EAAC;MAC5B3I,EAAA,CAAAE,MAAA,aACJ;MACJF,EADI,CAAAG,YAAA,EAAS,EACP;MAENH,EAAA,CAAA2B,UAAA,KAAAoP,+BAAA,kBAAsD;MAoBtD/Q,EAAA,CAAAC,cAAA,kBAA+B;MAK3BD,EAJA,CAAA2B,UAAA,KAAAqP,8BAAA,gBAA+B,KAAAC,8BAAA,gBACC,KAAAC,8BAAA,gBACC,KAAAC,+BAAA,mBAEsB;MAoCvDnR,EAAA,CAAAC,cAAA,cAAgC;MAC5BD,EAAA,CAAA2Q,SAAA,kBAAgC;MAE5B3Q,EADJ,CAAAC,cAAA,eAA0B,eACE;MACpBD,EAAA,CAAAE,MAAA,wBAAe;MAAAF,EAAA,CAAAC,cAAA,gBAAwB;MAAAD,EAAA,CAAAE,MAAA,IAAwB;MACnEF,EADmE,CAAAG,YAAA,EAAO,EACpE;MACNH,EAAA,CAAAC,cAAA,eAA4B;MACxBD,EAAA,CAAAE,MAAA,oBAAW;MAAAF,EAAA,CAAAC,cAAA,gBAAyB;MAAAD,EAAA,CAAAE,MAAA,IAAqB;MAGrEF,EAHqE,CAAAG,YAAA,EAAO,EAC9D,EACJ,EACJ;MAENH,EAAA,CAAAC,cAAA,eAA2B;MAwBvBD,EAtBA,CAAA2B,UAAA,KAAAyP,wCAAA,0BAAyC,KAAAC,wCAAA,0BAsByB;MAiB9ErR,EAFQ,CAAAG,YAAA,EAAM,EACA,EACR;MAINH,EAAA,CAAA2Q,SAAA,sBAAiC;;;MAxIrB3Q,EAAA,CAAAI,SAAA,GAAqC;MAArCJ,EAAA,CAAAmB,WAAA,WAAAuM,GAAA,CAAAtI,QAAA,aAAqC;MAMrCpF,EAAA,CAAAI,SAAA,GAAoC;MAApCJ,EAAA,CAAAmB,WAAA,WAAAuM,GAAA,CAAAtI,QAAA,YAAoC;MAMpCpF,EAAA,CAAAI,SAAA,GAAmC;MAAnCJ,EAAA,CAAAmB,WAAA,WAAAuM,GAAA,CAAAtI,QAAA,WAAmC;MAMfpF,EAAA,CAAAI,SAAA,GAAwB;MAAxBJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,WAAwB;MAqB3CpF,EAAA,CAAAI,SAAA,GAAwB;MAAxBJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,WAAwB;MACxBpF,EAAA,CAAAI,SAAA,EAAyB;MAAzBJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,YAAyB;MACzBpF,EAAA,CAAAI,SAAA,EAA0B;MAA1BJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,aAA0B;MAEFpF,EAAA,CAAAI,SAAA,EAAwB;MAAxBJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,WAAwB;MAwCFpF,EAAA,CAAAI,SAAA,GAAwB;MAAxBJ,EAAA,CAAAsB,kBAAA,KAAAoM,GAAA,CAAA1H,iBAAA,MAAwB;MAG3BhG,EAAA,CAAAI,SAAA,GAAqB;MAArBJ,EAAA,CAAAsB,kBAAA,KAAAoM,GAAA,CAAAzH,cAAA,MAAqB;MAOlDjG,EAAA,CAAAI,SAAA,GAAwB;MAAxBJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,WAAwB;MAsBxBpF,EAAA,CAAAI,SAAA,EAAiD;MAAjDJ,EAAA,CAAA+B,UAAA,SAAA2L,GAAA,CAAAtI,QAAA,eAAAsI,GAAA,CAAAtI,QAAA,aAAiD;;;iBDpGhE7F,WAAW,EAAEF,YAAY,EAAAiS,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElS,WAAW,EAAAmS,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,mBAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,YAAA,EAAAN,EAAA,CAAAO,YAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAEzS,YAAY,EAAEG,mBAAmB,EAAEI,mBAAmB;EAAAmS,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}