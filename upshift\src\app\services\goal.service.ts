import { Injectable, inject } from '@angular/core';
import { Goal, GoalJournalEntry, MicroGoal } from '../models/goal.model';
import { Observable, catchError, map, of, from } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class GoalService {
  private supabaseService = inject(SupabaseService);

  // Goal CRUD operations
  getGoals(userId: string): Observable<Goal[]> {

    if (!userId) {
      console.error('GoalService: No userId provided');
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goals')
        .select('*')
        .eq('user_id', userId)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('GoalService: Error getting goals:', response.error);
            return [];
          }
          return response.data as Goal[];
        }),
        catchError(error => {
          console.error('GoalService: Error getting goals:', error);
          return of([]);
        })
      );
    } catch (error) {
      console.error('GoalService: Error getting goals:', error);
      return of([]);
    }
  }

  getGoal(goalId: string): Observable<Goal | null> {

    if (!goalId) {
      console.error('GoalService: No goalId provided');
      return of(null);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goals')
        .select('*')
        .eq('id', goalId)
        .single()
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('GoalService: Error getting goal:', response.error);
            return null;
          }
          return response.data as Goal || null;
        }),
        catchError(error => {
          console.error('GoalService: Error getting goal:', error);
          return of(null);
        })
      );
    } catch (error) {
      console.error('GoalService: Error getting goal:', error);
      return of(null);
    }
  }

  async createGoal(goal: Omit<Goal, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.supabase
        .from('goals')
        .insert(goal)
        .select('id')
        .single();

      if (error) {
        console.error('GoalService: Error creating goal:', error);
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      console.error('GoalService: Error creating goal:', error);
      return Promise.reject(error);
    }
  }

  async updateGoal(goalId: string, data: Partial<Goal>): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goals')
        .update(data)
        .eq('id', goalId);

      if (error) {
        console.error('GoalService: Error updating goal:', error);
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('GoalService: Error updating goal:', error);
      return Promise.reject(error);
    }
  }

  async deleteGoal(goalId: string): Promise<void> {
    try {
      const { error } = await this.supabaseService.supabase
        .from('goals')
        .delete()
        .eq('id', goalId);

      if (error) {
        console.error('GoalService: Error deleting goal:', error);
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('GoalService: Error deleting goal:', error);
      return Promise.reject(error);
    }
  }

  getPublicGoals(userId: string): Observable<Goal[]> {
    if (!userId) {
      console.error('GoalService: No userId provided');
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goals')
        .select('*')
        .eq('user_id', userId)
        .eq('public', true)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('GoalService: Error getting public goals:', response.error);
            return [];
          }
          return response.data as Goal[];
        }),
        catchError(error => {
          console.error('GoalService: Error getting public goals:', error);
          return of([]);
        })
      );
    } catch (error) {
      console.error('GoalService: Error getting public goals:', error);
      return of([]);
    }
  }

  // MicroGoal operations
  getMicroGoals(goalId: string): Observable<MicroGoal[]> {

    if (!goalId) {
      console.error('GoalService: No goalId provided');
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('microgoals')
        .select('*')
        .eq('goal_id', goalId)
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('GoalService: Error getting micro goals:', response.error);
            return [];
          }
          return response.data as MicroGoal[];
        }),
        catchError(error => {
          console.error('GoalService: Error getting micro goals:', error);
          return of([]);
        })
      );
    } catch (error) {
      console.error('GoalService: Error getting micro goals:', error);
      return of([]);
    }
  }

  async createMicroGoal(microGoal: Omit<MicroGoal, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.supabase
        .from('microgoals')
        .insert(microGoal)
        .select('id')
        .single();

      if (error) {
        console.error('GoalService: Error creating micro goal:', error);
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      console.error('GoalService: Error creating micro goal:', error);
      return Promise.reject(error);
    }
  }

  async toggleMicroGoalCompletion(microGoalId: string): Promise<void> {

    try {
      // Get the current micro goal
      const { data: microGoal, error: fetchError } = await this.supabaseService.supabase
        .from('microgoals')
        .select('*')
        .eq('id', microGoalId)
        .single();

      if (fetchError) {
        console.error('GoalService: Error fetching micro goal:', fetchError);
        throw fetchError;
      }

      if (!microGoal) {
        console.error('GoalService: Micro goal not found:', microGoalId);
        throw new Error('Micro goal not found');
      }

      const isCompleted = !microGoal.completed;

      // Update the micro goal
      // First try with completed_at column
      try {
        const { error: updateError } = await this.supabaseService.supabase
          .from('microgoals')
          .update({
            completed: isCompleted,
            completed_at: isCompleted ? new Date() : null
          })
          .eq('id', microGoalId);

        if (updateError) {
          // If error is about missing column, try without completed_at
          if (updateError.message && updateError.message.includes('completed_at')) {
            const { error: fallbackError } = await this.supabaseService.supabase
              .from('microgoals')
              .update({
                completed: isCompleted
              })
              .eq('id', microGoalId);

            if (fallbackError) {
              console.error('GoalService: Error updating micro goal:', fallbackError);
              throw fallbackError;
            }
          } else {
            console.error('GoalService: Error updating micro goal:', updateError);
            throw updateError;
          }
        }
      } catch (error) {
        console.error('GoalService: Error updating micro goal:', error);
        throw error;
      }

      // Error handling is now done in the try-catch block above

    } catch (error) {
      console.error('GoalService: Error toggling micro goal completion:', error);
      throw error;
    }
  }

  async deleteMicroGoal(microGoalId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('microgoals')
        .delete()
        .eq('id', microGoalId);

      if (error) {
        console.error('GoalService: Error deleting micro goal:', error);
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('GoalService: Error deleting micro goal:', error);
      return Promise.reject(error);
    }
  }

  // Journal Entry operations
  getJournalEntries(goalId: string): Observable<GoalJournalEntry[]> {

    if (!goalId) {
      console.error('GoalService: No goalId provided');
      return of([]);
    }

    try {
      return from(this.supabaseService.supabase
        .from('goal_journal_entries')
        .select('*')
        .eq('goal_id', goalId)
        .order('milestone_percentage', { ascending: true })
      ).pipe(
        map(response => {
          if (response.error) {
            console.error('GoalService: Error getting journal entries:', response.error);
            return [];
          }
          return response.data as GoalJournalEntry[];
        }),
        catchError(error => {
          console.error('GoalService: Error getting journal entries:', error);
          return of([]);
        })
      );
    } catch (error) {
      console.error('GoalService: Error getting journal entries:', error);
      return of([]);
    }
  }

  async createJournalEntry(entry: Omit<GoalJournalEntry, 'id' | 'created_at'>): Promise<string> {

    try {
      const newEntry: Omit<GoalJournalEntry, 'id'> = {
        ...entry,
        created_at: new Date()
      };

      // First try with select('id')
      try {
        const { data, error } = await this.supabaseService.supabase
          .from('goal_journal_entries')
          .insert(newEntry)
          .select('id')
          .single();

        if (error) {
          // If error is about missing id column, try without selecting id
          if (error.message && (error.message.includes('id does not exist') || error.message.includes('column goal_journal_entries.id'))) {
            throw new Error('id_column_missing');
          }
          console.error('GoalService: Error creating journal entry:', error);
          return Promise.reject(error);
        }

        return data.id;
      } catch (err: any) {
        // If the error is about missing id column, try without selecting id
        if (err.message === 'id_column_missing') {
          const { error } = await this.supabaseService.supabase
            .from('goal_journal_entries')
            .insert(newEntry);

          if (error) {
            console.error('GoalService: Error creating journal entry without id:', error);
            return Promise.reject(error);
          }

          return 'unknown_id'; // Return a placeholder since we can't get the actual ID
        } else {
          throw err; // Re-throw if it's not the specific error we're handling
        }
      }
    } catch (error) {
      console.error('GoalService: Error creating journal entry:', error);
      return Promise.reject(error);
    }
  }

  async updateJournalEntry(entryId: string, data: Partial<GoalJournalEntry>): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goal_journal_entries')
        .update(data)
        .eq('id', entryId);

      if (error) {
        console.error('GoalService: Error updating journal entry:', error);
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('GoalService: Error updating journal entry:', error);
      return Promise.reject(error);
    }
  }

  async deleteJournalEntry(entryId: string): Promise<void> {

    try {
      const { error } = await this.supabaseService.supabase
        .from('goal_journal_entries')
        .delete()
        .eq('id', entryId);

      if (error) {
        console.error('GoalService: Error deleting journal entry:', error);
        return Promise.reject(error);
      }

      return Promise.resolve();
    } catch (error) {
      console.error('GoalService: Error deleting journal entry:', error);
      return Promise.reject(error);
    }
  }
}
