import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { Activity, ActivityType } from '../../models/activity.model';
import { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';
import { take } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
// @ts-ignore
import { Chart, registerables } from 'chart.js';
import { SupabaseService } from '../../services/supabase.service';
import { EmojiInputDirective } from '../../directives/emoji-input.directive';

Chart.register(...registerables);

interface DateDisplay {
  date: string;
  day: number;
  is_today: boolean;
  is_selected: boolean;
  is_future: boolean;
}

@Component({
  selector: 'app-time-tracker',
  templateUrl: './time-tracker.page.html',
  styleUrls: ['./time-tracker.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]
})
export class TimeTrackerPage implements OnInit, AfterViewInit {
  // User data
  userId: string | null = null;

  // View mode
  viewMode: 'day' | 'week' | 'month' = 'day';

  // Calendar data - starting with Monday
  dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'];
  weekDates: DateDisplay[] = [];
  weekOffset = 0;
  selectedDate: Date = new Date();

  // Activity data
  activityTypes: ActivityType[] = [];
  activities: Activity[] = [];
  weekActivities: Activity[] = [];
  monthActivities: Activity[] = [];

  // Form inputs
  selectedActivityId = '';
  hoursInput = 0;
  minutesInput = 0;
  customActivityName = '';
  customEmoji = '⚡';
  customHoursInput = 0;
  customMinutesInput = 0;

  // UI state
  showStandardInput = false;
  showCustomForm = false;

  // Time summary
  totalTrackedHours = '0.0';
  remainingHours = '24.0';

  // Chart
  timeChart: Chart | null = null;

  @ViewChild('timeChart') chartCanvas: ElementRef | undefined;

  private supabaseService = inject(SupabaseService);
  private timeTrackerService = inject(TimeTrackerUnifiedService);

  constructor() {}

  ngOnInit() {
    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(authUser => {
      if (authUser) {
        this.userId = authUser.id;
        this.loadActivityTypes();
        this.generateWeekDates();
        this.loadDataForCurrentView();
      }
    });
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.initializeChart();
    }, 500);
  }

  loadActivityTypes() {
    this.timeTrackerService.getActivityTypes().subscribe((types: ActivityType[]) => {
      this.activityTypes = types;
    });
  }

  generateWeekDates() {
    const today = new Date();
    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday

    // Calculate the start of the week (Monday)
    // For Monday as first day: 1 = Monday, 0 = Sunday (which should be treated as day 7)
    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));

    this.weekDates = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);

      const dateStr = this.formatDate(date);
      const isToday = this.isSameDay(date, today);
      const isSelected = this.isSameDay(date, this.selectedDate);
      const isFuture = date > today;

      this.weekDates.push({
        date: dateStr,
        day: date.getDate(),
        is_today: isToday,
        is_selected: isSelected,
        is_future: isFuture
      });
    }
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  get currentMonthName(): string {
    return this.monthNames[this.selectedDate.getMonth()];
  }

  changeWeek(offset: number) {
    this.weekOffset += offset;
    this.generateWeekDates();
  }

  selectDate(dateStr: string) {
    this.selectedDate = new Date(dateStr);
    this.generateWeekDates();
    this.loadActivities();
  }

  // View mode methods
  setViewMode(mode: 'day' | 'week' | 'month') {
    console.log('TimeTracker: Setting view mode to', mode);
    this.viewMode = mode;
    this.loadDataForCurrentView();
  }

  loadDataForCurrentView() {
    if (!this.userId) return;

    switch (this.viewMode) {
      case 'day':
        this.loadActivities();
        break;
      case 'week':
        this.loadWeekActivities();
        break;
      case 'month':
        this.loadMonthActivities();
        break;
    }
  }

  loadWeekActivities() {
    if (!this.userId) return;

    const startOfWeek = this.getStartOfWeek(this.selectedDate);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    this.timeTrackerService.getActivitiesForDateRange(
      this.userId,
      this.formatDate(startOfWeek),
      this.formatDate(endOfWeek)
    ).subscribe((activities: Activity[]) => {
      this.weekActivities = this.calculateAverageActivitiesFromData(activities);
      this.activities = this.weekActivities;
      // Don't calculate totals or update chart for week view as it doesn't make mathematical sense
    });
  }

  loadMonthActivities() {
    if (!this.userId) {
      console.log('TimeTracker: No userId for month activities');
      return;
    }

    const startOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);
    const endOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);

    console.log('TimeTracker: Loading month activities from', this.formatDate(startOfMonth), 'to', this.formatDate(endOfMonth));

    this.timeTrackerService.getActivitiesForDateRange(
      this.userId,
      this.formatDate(startOfMonth),
      this.formatDate(endOfMonth)
    ).subscribe((activities: Activity[]) => {
      console.log('TimeTracker: Received month activities:', activities);
      this.monthActivities = this.calculateAverageActivitiesFromData(activities);
      console.log('TimeTracker: Calculated month averages:', this.monthActivities);
      this.activities = this.monthActivities;
      // Don't calculate totals or update chart for month view as it doesn't make mathematical sense
    });
  }

  getStartOfWeek(date: Date): Date {
    const currentDay = date.getDay(); // 0 = Sunday, 6 = Saturday
    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));
    return startOfWeek;
  }

  calculateAverageActivitiesFromData(activities: Activity[]): Activity[] {
    // Group activities by name and emoji, and track unique days
    const activityGroups: { [key: string]: { activities: Activity[], days: Set<string> } } = {};

    activities.forEach(activity => {
      const key = `${activity.name}_${activity.emoji}`;
      if (!activityGroups[key]) {
        activityGroups[key] = { activities: [], days: new Set() };
      }
      activityGroups[key].activities.push(activity);
      // Track unique dates when this activity occurred
      if (activity.date) {
        activityGroups[key].days.add(activity.date);
      } else {
        // Fallback to day_tracking_id if date is not available
        activityGroups[key].days.add(activity.day_tracking_id);
      }
    });

    // Calculate averages based on days that had activities
    const averageActivities: Activity[] = [];

    Object.keys(activityGroups).forEach(key => {
      const group = activityGroups[key];
      const totalMinutes = group.activities.reduce((sum, activity) => {
        return sum + (activity.hours * 60) + activity.minutes;
      }, 0);

      // Use the number of unique days that had this activity
      const daysWithActivity = group.days.size;
      const averageMinutes = totalMinutes / daysWithActivity;
      const averageHours = Math.floor(averageMinutes / 60);
      const remainingMinutes = Math.round(averageMinutes % 60);

      if (averageHours > 0 || remainingMinutes > 0) {
        averageActivities.push({
          id: group.activities[0].id,
          day_tracking_id: group.activities[0].day_tracking_id,
          name: group.activities[0].name,
          emoji: group.activities[0].emoji,
          hours: averageHours,
          minutes: remainingMinutes,
          is_custom: group.activities[0].is_custom
        });
      }
    });

    return averageActivities;
  }

  loadActivities() {
    if (!this.userId) return;

    const dateStr = this.formatDate(this.selectedDate);
    const userId = this.userId; // Store in local variable to ensure it's not null

    // First get the day tracking to get the totals
    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {
      // Then get the activities
      this.timeTrackerService.getActivities(userId, dateStr).subscribe((activities: Activity[]) => {
        this.activities = activities;

        // Calculate totals manually from activities
        this.calculateTotals();

        // Update chart
        this.updateChart();
      });
    });
  }

  // Calculate totals manually from activities
  calculateTotals() {
    // Calculate total time in hours (including minutes as fraction of hour)
    const totalHours = this.activities.reduce((total, activity) => {
      return total + activity.hours + (activity.minutes / 60);
    }, 0);

    // Format to 1 decimal place
    this.totalTrackedHours = totalHours.toFixed(1);

    // Calculate remaining hours (max 0)
    const remainingHours = Math.max(0, 24 - totalHours);
    this.remainingHours = remainingHours.toFixed(1);

  }

  handleActivitySelection(event: Event) {
    const select = event.target as HTMLSelectElement;
    const value = select.value;

    this.selectedActivityId = value;
    this.showStandardInput = value !== '' && value !== 'custom';
    this.showCustomForm = value === 'custom';
  }

  addActivity() {
    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;
    if (this.hoursInput === 0 && this.minutesInput === 0) {
      alert('Please enter a time greater than 0');
      return;
    }

    // Check if total time would exceed 24 hours
    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {
      alert('⏱️ Total time cannot exceed 24 hours');
      return;
    }

    // Find the selected activity type
    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);
    if (!activityType) return;

    // Check if activity already exists for this day
    const existingActivity = this.activities.find(activity => activity.name === activityType.name);

    if (existingActivity) {
      // Update existing activity
      const updatedHours = existingActivity.hours + this.hoursInput;
      const updatedMinutes = existingActivity.minutes + this.minutesInput;

      // Convert excess minutes to hours
      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);
      let finalMinutes = updatedMinutes % 60;

      // Cap at 24 hours
      if (finalHours > 23) {
        finalHours = 23;
        finalMinutes = 59;
      }

      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {
        // Update local activity
        existingActivity.hours = finalHours;
        existingActivity.minutes = finalMinutes;

        // Reset inputs
        this.resetInputs();

        // Calculate totals manually
        this.calculateTotals();

        // Update chart
        this.updateChart();
      }).catch(error => {
        console.error('Error updating activity:', error);
        alert('Error updating activity. Please try again.');
      });
    } else {
      // Create new activity
      const userId = this.userId as string; // Cast to string to satisfy TypeScript
      const dateStr = this.formatDate(this.selectedDate);

      this.timeTrackerService.createActivity(
        userId,
        dateStr,
        activityType.name,
        activityType.emoji,
        this.hoursInput,
        this.minutesInput,
        false
      ).then((result) => {
        // We need the ID from the result for the new activity
        // Add to local activities
        this.activities.push({
          id: result.id,
          day_tracking_id: '', // This will be set on the server
          name: activityType.name,
          emoji: activityType.emoji,
          hours: this.hoursInput,
          minutes: this.minutesInput,
          is_custom: false
        });

        // Reset inputs
        this.resetInputs();

        // Calculate totals manually
        this.calculateTotals();

        // Update chart
        this.updateChart();
      }).catch(error => {
        console.error('Error creating activity:', error);
        alert('Error creating activity. Please try again.');
      });
    }
  }

  addCustomActivity() {
    if (!this.userId || !this.customActivityName.trim()) {
      alert('Please enter an activity name');
      return;
    }

    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {
      alert('Please enter a time greater than 0');
      return;
    }

    // Check if total time would exceed 24 hours
    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {
      alert('⏱️ Total time cannot exceed 24 hours');
      return;
    }

    // Check if activity already exists for this day
    const existingActivity = this.activities.find(activity =>
      activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase()
    );

    if (existingActivity) {
      // Update existing activity
      const updatedHours = existingActivity.hours + this.customHoursInput;
      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;

      // Convert excess minutes to hours
      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);
      let finalMinutes = updatedMinutes % 60;

      // Cap at 24 hours
      if (finalHours > 23) {
        finalHours = 23;
        finalMinutes = 59;
      }

      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {
        // Update local activity
        existingActivity.hours = finalHours;
        existingActivity.minutes = finalMinutes;

        // Reset inputs
        this.resetInputs();

        // Calculate totals manually
        this.calculateTotals();

        // Update chart
        this.updateChart();
      }).catch(error => {
        console.error('Error updating activity:', error);
        alert('Error updating activity. Please try again.');
      });
    } else {
      // Create new activity
      const userId = this.userId as string; // Cast to string to satisfy TypeScript
      const dateStr = this.formatDate(this.selectedDate);

      this.timeTrackerService.createActivity(
        userId,
        dateStr,
        this.customActivityName.trim(),
        this.customEmoji,
        this.customHoursInput,
        this.customMinutesInput,
        true
      ).then((result) => {
        // We need the ID from the result for the new activity
        // Add to local activities
        this.activities.push({
          id: result.id,
          day_tracking_id: '', // This will be set on the server
          name: this.customActivityName.trim(),
          emoji: this.customEmoji,
          hours: this.customHoursInput,
          minutes: this.customMinutesInput,
          is_custom: true
        });

        // Reset inputs
        this.resetInputs();

        // Calculate totals manually
        this.calculateTotals();

        // Update chart
        this.updateChart();
      }).catch(error => {
        console.error('Error creating activity:', error);
        alert('Error creating activity. Please try again.');
      });
    }
  }

  updateActivity(activity: Activity) {
    if (!activity.id) return;

    // Validate time inputs
    if (activity.hours < 0) activity.hours = 0;
    if (activity.minutes < 0) activity.minutes = 0;
    if (activity.hours > 23) activity.hours = 23;
    if (activity.minutes > 59) activity.minutes = 59;

    // Check if total time would exceed 24 hours
    const currentHours = activity.hours;
    const currentMinutes = activity.minutes;

    // Calculate total time for all activities except this one
    const otherActivitiesTime = this.activities
      .filter(a => a.id !== activity.id)
      .reduce((total, a) => total + a.hours + (a.minutes / 60), 0);

    // Add this activity's time
    const totalTime = otherActivitiesTime + currentHours + (currentMinutes / 60);

    if (totalTime > 24) {
      alert('Total time cannot exceed 24 hours');
      // Reset to previous values
      this.loadActivities();
      return;
    }

    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {
      // Calculate totals manually
      this.calculateTotals();

      // Update chart
      this.updateChart();
    }).catch(error => {
      console.error('Error updating activity:', error);
      alert('Error updating activity. Please try again.');
      this.loadActivities(); // Reload to get the correct state
    });
  }

  deleteActivity(activityId: string) {
    if (!activityId) return;

    this.timeTrackerService.deleteActivity(activityId).then(() => {
      // Remove from local activities
      this.activities = this.activities.filter(a => a.id !== activityId);

      // Calculate totals manually
      this.calculateTotals();

      // Update chart
      this.updateChart();
    }).catch(error => {
      console.error('Error deleting activity:', error);
      alert('Error deleting activity. Please try again.');
    });
  }

  resetInputs() {
    // Save the current emoji before resetting
    const currentEmoji = this.customEmoji;

    // Reset form values
    this.selectedActivityId = '';
    this.hoursInput = 0;
    this.minutesInput = 0;
    this.customActivityName = '';
    // Keep the last used emoji instead of resetting to default
    // Only set default emoji if current is empty
    this.customEmoji = currentEmoji || '⚡';
    this.customHoursInput = 0;
    this.customMinutesInput = 0;
    this.showStandardInput = false;
    this.showCustomForm = false;

    // Reset the select element to "Select Activity"
    setTimeout(() => {
      const selectElement = document.getElementById('activitySelect') as HTMLSelectElement;
      if (selectElement) {
        selectElement.value = '';

        // Directly update the UI state instead of using the event handler
        this.selectedActivityId = '';
        this.showStandardInput = false;
        this.showCustomForm = false;
      }
    }, 0);
  }

  // We no longer need this method as we get the totals from the server
  // Keeping it for backward compatibility
  updateTotals() {
    // This is now handled by the server
  }

  validateTotalTime(hours: number, minutes: number): boolean {
    // Calculate total time for all existing activities
    const existingTime = this.activities.reduce((total, activity) => {
      return total + activity.hours + (activity.minutes / 60);
    }, 0);

    // Add new time
    const totalTime = existingTime + hours + (minutes / 60);

    // Check if total time exceeds 24 hours
    return totalTime <= 24;
  }

  initializeChart() {
    const canvas = document.getElementById('timeChart') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Destroy existing chart if it exists
    if (this.timeChart) {
      this.timeChart.destroy();
      this.timeChart = null;
    }

    // Register center text plugin
    Chart.register({
      id: 'centerText',
      beforeDraw: (chart: any) => {
        const ctx = chart.ctx;
        const width = chart.width;
        const height = chart.height;

        ctx.restore();
        ctx.font = 'bold 20px Inter';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Draw "24h" text
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText('24h', width / 2, height / 2);
        ctx.save();
      }
    });

    try {
      this.timeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: [],
          datasets: [{
            data: [],
            backgroundColor: [],
            borderWidth: 0,
            borderRadius: 5,
            spacing: 2
          }]
        },
        options: {
          cutout: '75%',
          radius: '90%',
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function(context: any) {
                  const value = context.raw as number;
                  const hours = Math.floor(value);
                  const minutes = Math.round((value - hours) * 60);
                  return `${hours}h ${minutes}m`;
                }
              }
            }
          },
          animation: {
            animateRotate: true,
            animateScale: true
          }
        }
      });

      this.updateChart();
    } catch (error) {
      console.error('TimeTrackerPage: Error creating chart:', error);
    }
  }

  updateChart() {
    if (!this.timeChart) {
      this.initializeChart();
      return;
    }

    try {
      const data = this.processActivitiesForChart();

      this.timeChart.data.labels = data.labels;
      this.timeChart.data.datasets[0].data = data.values;
      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);

      this.timeChart.update();
    } catch (error) {
      console.error('TimeTrackerPage: Error updating chart:', error);
      // If there's an error, try to recreate the chart
      this.timeChart = null;
      setTimeout(() => {
        this.initializeChart();
      }, 100);
    }
  }

  processActivitiesForChart() {
    const labels: string[] = [];
    const values: number[] = [];

    this.activities.forEach(activity => {
      // Include emoji in the label for better visualization
      labels.push(`${activity.emoji} ${activity.name}`);
      values.push(activity.hours + (activity.minutes / 60));
    });

    // Add remaining time if total is less than 24 hours
    const totalHours = values.reduce((a, b) => a + b, 0);
    if (totalHours < 24) {
      labels.push('Remaining');
      values.push(24 - totalHours);
    }

    return { labels, values };
  }

  generateColors(count: number) {
    const colors: string[] = [];

    // Use colors from activity_types in Supabase
    this.activities.forEach(activity => {
      // Find the matching activity type to get its color
      const activityType = this.activityTypes.find(type => type.name === activity.name);

      // If we found a matching activity type with a color, use it; otherwise use a default color
      if (activityType && activityType.color) {
        colors.push(activityType.color);
      } else if (activity.is_custom) {
        // For custom activities, use a specific color
        colors.push('#2C3E50'); // Royal Blue for custom activities
      } else {
        // Default color if no matching activity type or no color defined
        colors.push('#2C3E50');
      }
    });

    // Add dark color for remaining time
    if (count > colors.length) {
      colors.push('#1C1C1E');
    }

    return colors;
  }
}
