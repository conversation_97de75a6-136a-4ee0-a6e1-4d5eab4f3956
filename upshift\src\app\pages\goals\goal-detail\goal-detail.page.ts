import { Component, OnInit, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { GoalService } from '../../../services/goal.service';
import { Goal, GoalJournalEntry, MicroGoal } from '../../../models/goal.model';
import { take } from 'rxjs';
import { NavigationComponent } from '../../../components/navigation/navigation.component';
import { SupabaseService } from '../../../services/supabase.service';
import { EmojiInputDirective } from '../../../directives/emoji-input.directive';

@Component({
  selector: 'app-goal-detail',
  templateUrl: './goal-detail.page.html',
  styleUrls: ['./goal-detail.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]
})
export class GoalDetailPage implements OnInit, AfterViewInit {
  @ViewChild('journalEditor', { static: false }) journalEditor!: ElementRef<HTMLDivElement>;
  // User data
  userId: string | null = null;

  // Goal data
  goalId: string | null = null;
  goal: Goal | null = null;
  microgoals: MicroGoal[] = [];
  journalEntries: GoalJournalEntry[] = [];

  // UI state
  showEditForm = false;
  editedDescription = '';
  editedName = '';
  editedEmoji = '';
  currentValue = 0;
  progressPercent = 0;
  showJournalModal = false;
  nextMilestone: number | null = null;
  journalContent = '';

  // New microgoal
  newMicrogoalTitle = '';

  private supabaseService = inject(SupabaseService);
  private goalService = inject(GoalService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  constructor() {}

  ngOnInit() {
    // Get the current user
    this.supabaseService.currentUser$.pipe(
      take(1)
    ).subscribe(user => {
      if (user) {
        this.userId = user.id;

        // Get the goal ID from the route
        this.route.paramMap.pipe(
          take(1)
        ).subscribe(params => {
          this.goalId = params.get('id');
          if (this.goalId) {
            this.loadGoal();
            this.loadMicrogoals();
            this.loadJournalEntries();
          }
        });
      }
    });
  }

  ngAfterViewInit() {
    // Initialize journal editor content after view is ready
    if (this.showJournalModal && this.journalEditor) {
      this.journalEditor.nativeElement.innerHTML = this.journalContent;
      this.setupCheckboxListeners();
    }
  }

  loadGoal() {
    if (!this.goalId) return;

    this.goalService.getGoal(this.goalId).pipe(
      take(1)
    ).subscribe(goal => {
      if (goal) {
        this.goal = goal;
        this.editedDescription = goal.description;
        this.editedName = goal.name;
        this.editedEmoji = goal.emoji;
        this.currentValue = goal.current_value;
        this.calculateProgress();
        this.checkForMilestone();
      } else {
        console.error('Goal not found with ID:', this.goalId);
      }
    });
  }

  loadMicrogoals() {
    if (!this.goalId) return;

    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {
      this.microgoals = microgoals;
    });
  }

  loadJournalEntries() {
    if (!this.goalId) return;

    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {
      this.journalEntries = entries;
      // After loading entries, check if we need to show milestone modal
      this.checkForMilestone();
    });
  }

  calculateProgress() {
    if (!this.goal) return;

    this.progressPercent = this.goal.goal_value > 0
      ? Math.min(100, Math.round((this.goal.current_value / this.goal.goal_value) * 100))
      : 0;
  }

  checkForMilestone() {
    if (!this.goal) return;

    // Calculate current progress percentage
    const currentPercent = this.progressPercent;

    // Define milestone percentages
    const milestones = [20, 40, 60, 80, 100];

    // Get all milestone percentages that already have journal entries
    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);

    // Find all milestones that have been reached but don't have journal entries
    const reachedMilestones = milestones.filter(milestone =>
      currentPercent >= milestone &&
      !existingMilestones.includes(milestone)
    );


    // Take the first one (lowest percentage) as the next milestone to show
    if (reachedMilestones.length > 0) {
      this.nextMilestone = reachedMilestones[0];
      this.showJournalModal = true;

      // Set content after view updates
      setTimeout(() => {
        if (this.journalEditor) {
          this.journalEditor.nativeElement.innerHTML = this.journalContent;
          this.setupCheckboxListeners();
        }
      }, 0);
    } else {
      this.nextMilestone = null;
      this.showJournalModal = false;
    }
  }

  updateGoalInfo() {
    if (!this.goalId || !this.goal) return;

    const trimmedName = this.editedName.trim();
    if (!trimmedName) {
      return; // Don't save if name is empty
    }

    // Use emoji as is (EmojiInputDirective already handles validation)
    const finalEmoji = this.editedEmoji || '🎯';

    this.goalService.updateGoal(this.goalId, {
      name: trimmedName,
      emoji: finalEmoji,
      description: this.editedDescription
    }).then(() => {
      if (this.goal) {
        this.goal.name = trimmedName;
        this.goal.emoji = finalEmoji;
        this.goal.description = this.editedDescription;
      }
      this.showEditForm = false;
    }).catch(error => {
      console.error('Error updating goal info:', error);
    });
  }

  cancelEdit() {
    this.showEditForm = false;
    this.editedName = this.goal?.name || '';
    this.editedEmoji = this.goal?.emoji || '';
    this.editedDescription = this.goal?.description || '';
  }

  updateProgress() {
    if (!this.goalId || !this.goal) return;


    this.goalService.updateGoal(this.goalId, {
      current_value: this.currentValue
    }).then(() => {
      if (this.goal) {
        this.goal.current_value = this.currentValue;
        this.calculateProgress();

        // Reload journal entries to ensure we have the latest data before checking milestones
        this.loadJournalEntries();
      }
    }).catch(error => {
      console.error('Error updating progress:', error);
    });
  }

  toggleMicrogoal(microgoal: MicroGoal) {
    if (!microgoal.id) return;

    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {
      // Update the local state
      microgoal.completed = !microgoal.completed;
      microgoal.completed_at = microgoal.completed ? new Date() : undefined;
    });
  }

  deleteMicrogoal(microgoal: MicroGoal) {
    if (!microgoal.id) return;

    this.goalService.deleteMicroGoal(microgoal.id).then(() => {
      // Remove from local array
      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);
    });
  }

  addMicrogoal() {
    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;

    const newMicrogoal: Omit<MicroGoal, 'id'> = {
      goal_id: this.goalId,
      title: this.newMicrogoalTitle.trim(),
      completed: false
    };

    this.goalService.createMicroGoal(newMicrogoal).then(id => {
      // Add to local array
      this.microgoals.push({
        ...newMicrogoal,
        id
      });

      // Clear the input
      this.newMicrogoalTitle = '';
    });
  }

  addJournalEntry() {
    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;

    const newEntry: Omit<GoalJournalEntry, 'id' | 'created_at'> = {
      goal_id: this.goalId,
      milestone_percentage: this.nextMilestone,
      content: this.journalContent.trim()
    };

    this.goalService.createJournalEntry(newEntry).then(id => {

      // Hide the modal and clear the input
      this.showJournalModal = false;
      this.journalContent = '';

      // Reload journal entries from the database to ensure we have the latest data
      this.loadJournalEntries();
    }).catch(error => {
      console.error('Error creating journal entry:', error);
    });
  }

  getJournalPreview(content: string): string {
    // Strip HTML tags and get first 100 characters
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;
  }

  openJournalEntry(entry: GoalJournalEntry) {
    // Navigate to journal entry detail page
    this.router.navigate(['/goals', this.goalId, 'journal', entry.id]);
  }

  // Rich text formatting methods
  formatText(command: string, value?: string) {
    document.execCommand(command, false, value);
  }

  insertList(ordered: boolean = false) {
    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';
    document.execCommand(command, false);
  }

  onJournalContentInput(event: any) {
    this.journalContent = event.target.innerHTML;
    this.setupCheckboxListeners();
  }

  onJournalContentBlur(event: any) {
    this.journalContent = event.target.innerHTML;
  }

  setupCheckboxListeners() {
    if (this.journalEditor) {
      const checkboxes = this.journalEditor.nativeElement.querySelectorAll('.checklist-checkbox');
      checkboxes.forEach((checkbox: any) => {
        // Sync checkbox state with attribute
        checkbox.checked = checkbox.hasAttribute('checked');

        checkbox.removeEventListener('change', this.handleCheckboxChange.bind(this));
        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));
      });
    }
  }

  handleCheckboxChange() {
    if (this.journalEditor) {
      // Update checkbox attributes to match their checked state
      const checkboxes = this.journalEditor.nativeElement.querySelectorAll('.checklist-checkbox');
      checkboxes.forEach((checkbox: any) => {
        if (checkbox.checked) {
          checkbox.setAttribute('checked', 'checked');
        } else {
          checkbox.removeAttribute('checked');
        }
      });
      this.journalContent = this.journalEditor.nativeElement.innerHTML;
    }
  }

  insertQuote() {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      const quote = document.createElement('blockquote');
      quote.className = 'notion-quote';
      quote.innerHTML = 'Quote text here...';

      range.deleteContents();
      range.insertNode(quote);

      // Select the quote content
      const newRange = document.createRange();
      newRange.selectNodeContents(quote);
      selection.removeAllRanges();
      selection.addRange(newRange);
    }
  }

  insertCheckList() {
    if (!this.journalEditor) return;

    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // Ensure we're inserting inside the editor
      if (!this.journalEditor.nativeElement.contains(range.commonAncestorContainer)) {
        // If selection is outside editor, insert at the end
        const newRange = document.createRange();
        newRange.selectNodeContents(this.journalEditor.nativeElement);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
        range.setStart(newRange.startContainer, newRange.startOffset);
        range.setEnd(newRange.endContainer, newRange.endOffset);
      }

      const checklistItem = document.createElement('div');
      checklistItem.className = 'checklist-item';
      checklistItem.innerHTML = '<input type="checkbox" class="checklist-checkbox"> <span contenteditable="true">New item</span>';

      range.deleteContents();
      range.insertNode(checklistItem);

      // Add event listener to the new checkbox
      const checkbox = checklistItem.querySelector('.checklist-checkbox') as HTMLInputElement;
      if (checkbox) {
        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));
      }

      // Move cursor to the text span
      const textSpan = checklistItem.querySelector('span');
      if (textSpan) {
        const newRange = document.createRange();
        newRange.selectNodeContents(textSpan);
        selection.removeAllRanges();
        selection.addRange(newRange);
      }

      // Update content
      this.journalContent = this.journalEditor.nativeElement.innerHTML;
    }
  }

  togglePublic() {
    if (!this.goalId || !this.goal) return;

    const newPublicValue = !this.goal.public;

    this.goalService.updateGoal(this.goalId, {
      public: newPublicValue
    }).then(() => {
      if (this.goal) {
        this.goal.public = newPublicValue;
      }
    }).catch(error => {
      console.error('Error updating goal public status:', error);
    });
  }

  confirmDeleteGoal() {
    if (!this.goalId) return;

    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {
      this.goalService.deleteGoal(this.goalId).then(() => {
        this.router.navigate(['/goals']);
      }).catch(error => {
        console.error('Error deleting goal:', error);
      });
    }
  }


}
