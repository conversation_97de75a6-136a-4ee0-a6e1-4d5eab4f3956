{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _GroupSideQuestComponent;\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { take } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/group-sidequest.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"../../services/supabase.service\";\nimport * as i4 from \"@angular/common\";\nfunction GroupSideQuestComponent_section_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"p\");\n    i0.ɵɵtext(2, \"No side quest available for this group.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GroupSideQuestComponent_section_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function GroupSideQuestComponent_section_0_div_4_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(!ctx_r1.isBeforeJoinDate && !ctx_r1.togglingSideQuest && ctx_r1.toggleSideQuest());\n    });\n    i0.ɵɵelementStart(2, \"div\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 9)(10, \"div\", 10)(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 12);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"completed\", ctx_r1.memberStatus == null ? null : ctx_r1.memberStatus.completed)(\"disabled-quest\", ctx_r1.isBeforeJoinDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.dailyQuest.current_quest.emoji, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dailyQuest.current_quest.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dailyQuest.current_quest.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", (ctx_r1.memberStatus == null ? null : ctx_r1.memberStatus.completed) ? ctx_r1.dailyQuest.current_quest.goal_value : 0, \"/\", ctx_r1.dailyQuest.current_quest.goal_value, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Members: \", ctx_r1.dailyQuest.completed_members_count, \"/\", ctx_r1.dailyQuest.eligible_members_count, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\uD83D\\uDD25\", ctx_r1.dailyQuest.streak, \"d \");\n  }\n}\nfunction GroupSideQuestComponent_section_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 1)(1, \"h2\");\n    i0.ɵɵtext(2, \"Daily Group Side Quest\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GroupSideQuestComponent_section_0_div_3_Template, 3, 0, \"div\", 2)(4, GroupSideQuestComponent_section_0_div_4_Template, 17, 12, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.dailyQuest);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dailyQuest);\n  }\n}\nexport class GroupSideQuestComponent {\n  constructor(groupSideQuestService, toastController, supabaseService) {\n    this.groupSideQuestService = groupSideQuestService;\n    this.toastController = toastController;\n    this.supabaseService = supabaseService;\n    this.groupId = '';\n    this.userId = '';\n    this.joinedDate = '';\n    this.isAdmin = false;\n    this.enableSidequests = true;\n    this.selectedDate = '';\n    this.dailyQuest = null;\n    this.memberStatus = null;\n    this.memberStatuses = [];\n    this.isBeforeJoinDate = false;\n    this.togglingSideQuest = false;\n    this.isTodaySelected = false;\n  }\n  ngOnInit() {\n    if (this.enableSidequests) {\n      // Check if we need to reset member statuses for a new day\n      this.checkAndResetMemberStatuses();\n    }\n  }\n  /**\n   * Check if member statuses need to be reset for a new day\n   * This ensures that completion status is properly tracked each day\n   */\n  checkAndResetMemberStatuses() {\n    // First load the side quest to get its ID\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(take(1)).subscribe({\n      next: sideQuest => {\n        if (!sideQuest) {\n          return;\n        }\n        // Get the current date\n        const today = new Date();\n        const todayStr = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD\n        // Check if the side quest was assigned today\n        if (sideQuest.date_assigned === todayStr) {\n          this.loadSideQuest();\n          return;\n        }\n        // Get all member statuses\n        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(take(1)).subscribe({\n          next: statuses => {\n            // Check if any member status has last_updated different from today\n            const needsReset = statuses.some(s => s.last_updated !== todayStr);\n            if (needsReset) {\n              // Reset all member statuses for the new day\n              from(this.supabaseService.getClient().rpc('reset_group_sidequest_member_statuses', {\n                sidequest_id: sideQuest.id\n              })).subscribe({\n                next: () => {\n                  this.loadSideQuest();\n                },\n                error: error => {\n                  console.error('GroupSideQuestComponent: Error resetting member statuses:', error);\n                  this.loadSideQuest();\n                }\n              });\n            } else {\n              this.loadSideQuest();\n            }\n          },\n          error: error => {\n            console.error('GroupSideQuestComponent: Error getting member statuses for reset check:', error);\n            this.loadSideQuest();\n          }\n        });\n      },\n      error: error => {\n        console.error('GroupSideQuestComponent: Error getting side quest for reset check:', error);\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {\n      // Check if selected date is today\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();\n      selectedDate.setHours(0, 0, 0, 0);\n      this.isTodaySelected = selectedDate.getTime() === today.getTime();\n      // If side quests are disabled, reset the component state\n      if (!this.enableSidequests) {\n        this.dailyQuest = null;\n        this.memberStatus = null;\n        this.isLoading = false;\n        return;\n      }\n      this.loadSideQuest();\n    }\n  }\n  loadSideQuest() {\n    if (!this.groupId || !this.userId) {\n      this.isLoading = false;\n      return;\n    }\n    if (!this.enableSidequests) {\n      this.dailyQuest = null;\n      this.memberStatus = null;\n      this.isLoading = false;\n      return;\n    }\n    this.isLoading = true;\n    // Check if user joined date is before today\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const joinedDate = new Date(this.joinedDate);\n    joinedDate.setHours(0, 0, 0, 0);\n    const eligibleDate = new Date(today);\n    eligibleDate.setDate(today.getDate() - 1);\n    this.isBeforeJoinDate = joinedDate > eligibleDate;\n    // Always ensure a side quest is loaded from the pool\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(take(1)).subscribe({\n      next: sideQuest => {\n        // Convert GroupSideQuest to GroupDailyQuest\n        if (sideQuest) {\n          var _sideQuest$current_qu, _sideQuest$current_qu2, _sideQuest$current_qu3, _sideQuest$current_qu4, _sideQuest$current_qu5, _sideQuest$current_qu6;\n          this.dailyQuest = {\n            id: sideQuest.id,\n            group_id: sideQuest.group_id,\n            streak: sideQuest.streak,\n            completed: sideQuest.completed,\n            value_achieved: sideQuest.value_achieved,\n            date_assigned: sideQuest.date_assigned,\n            last_completed_date: sideQuest.last_completed_date,\n            category: sideQuest.category,\n            current_quest: {\n              id: ((_sideQuest$current_qu = sideQuest.current_quest) === null || _sideQuest$current_qu === void 0 ? void 0 : _sideQuest$current_qu.id) || '',\n              name: ((_sideQuest$current_qu2 = sideQuest.current_quest) === null || _sideQuest$current_qu2 === void 0 ? void 0 : _sideQuest$current_qu2.name) || '',\n              description: (_sideQuest$current_qu3 = sideQuest.current_quest) === null || _sideQuest$current_qu3 === void 0 ? void 0 : _sideQuest$current_qu3.description,\n              goal_value: ((_sideQuest$current_qu4 = sideQuest.current_quest) === null || _sideQuest$current_qu4 === void 0 ? void 0 : _sideQuest$current_qu4.goal_value) || 0,\n              goal_unit: ((_sideQuest$current_qu5 = sideQuest.current_quest) === null || _sideQuest$current_qu5 === void 0 ? void 0 : _sideQuest$current_qu5.goal_unit) || 'count',\n              emoji: ((_sideQuest$current_qu6 = sideQuest.current_quest) === null || _sideQuest$current_qu6 === void 0 ? void 0 : _sideQuest$current_qu6.emoji) || '🎯'\n            },\n            eligible_members_count: 0,\n            completed_members_count: 0\n          };\n        } else {\n          this.dailyQuest = null;\n        }\n        // We'll calculate the eligible and completed members counts when loading member statuses\n        if (sideQuest) {\n          // Get all member statuses\n          this.loadMemberStatuses(sideQuest.id);\n          // Get the current user's status\n          this.loadMemberStatus(sideQuest.id, this.userId);\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('GroupSideQuestComponent: Error loading group side quest:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  loadMemberStatuses(sideQuestId) {\n    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(take(1)).subscribe({\n      next: statuses => {\n        this.memberStatuses = statuses;\n        // Update the counts in the daily quest\n        if (this.dailyQuest) {\n          // Count eligible members (joined before today)\n          const today = new Date();\n          today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison\n          // Get all members with their joined dates\n          from(this.supabaseService.getClient().from('group_members').select('user_id, joined_date, nickname').eq('group_id', this.groupId)).subscribe(response => {\n            if (response.error) {\n              console.error('Error getting group members:', response.error);\n              return;\n            }\n            // Filter eligible members (joined before today)\n            const eligibleMembers = response.data.filter(member => {\n              const joinedDate = new Date(member.joined_date);\n              return joinedDate < today; // Before today, not today\n            });\n            // Get eligible member IDs\n            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\n            // Update the eligible members count - this is the total number of members who joined at least a day ago\n            this.dailyQuest.eligible_members_count = eligibleMembers.length;\n            // Get today's date in YYYY-MM-DD format\n            const todayStr = new Date().toISOString().split('T')[0];\n            // Count completed members among eligible members only\n            // Only count members who have completed the quest TODAY\n            this.dailyQuest.completed_members_count = statuses.filter(s => s.completed &&\n            // Status is completed\n            eligibleMemberIds.includes(s.member_id) &&\n            // Member is eligible (joined at least a day ago)\n            s.last_updated === todayStr // Status was updated today\n            ).length;\n            // Log detailed status information for debugging\n            // statuses.forEach(s => {\n            //   const member = response.data.find(m => m.user_id === s.member_id);\n            //   const isEligible = eligibleMemberIds.includes(s.member_id);\n            //   const todayStr = new Date().toISOString().split('T')[0];\n            //   const isUpdatedToday = s.last_updated === todayStr;\n            // });\n          });\n        }\n      },\n      error: error => {\n        console.error('Error loading member statuses:', error);\n      }\n    });\n  }\n  loadMemberStatus(sideQuestId, userId) {\n    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(take(1)).subscribe({\n      next: status => {\n        if (status) {\n          // Check if the status is from today\n          const today = new Date().toISOString().split('T')[0];\n          if (status.last_updated === today) {\n            // Status is from today, use it\n            this.memberStatus = status;\n          } else {\n            // Status is from a previous day, reset it to today's default\n            this.resetMemberStatusToToday(status, sideQuestId);\n          }\n        } else {\n          // Create a new status for this member\n          this.createMemberStatus(sideQuestId, userId);\n        }\n      },\n      error: error => {\n        console.error('Error loading member status:', error);\n      }\n    });\n  }\n  resetMemberStatusToToday(existingStatus, sideQuestId) {\n    const today = new Date().toISOString().split('T')[0];\n    // Update the existing status to reset it for today\n    from(this.supabaseService.getClient().from('group_sidequest_member_status').update({\n      completed: false,\n      value_achieved: 0,\n      last_updated: today\n    }).eq('id', existingStatus.id).select().single()).pipe(take(1)).subscribe({\n      next: response => {\n        if (response.error) {\n          console.error('Error resetting member status:', response.error);\n          return;\n        }\n        this.memberStatus = response.data;\n        // Reload all member statuses to update counts\n        this.loadMemberStatuses(sideQuestId);\n      },\n      error: error => {\n        console.error('Error resetting member status:', error);\n      }\n    });\n  }\n  createMemberStatus(sideQuestId, userId) {\n    const today = new Date();\n    const dateString = today.toISOString().split('T')[0];\n    const newStatus = {\n      group_quest_id: sideQuestId,\n      member_id: userId,\n      completed: false,\n      value_achieved: 0,\n      last_updated: dateString\n    };\n    from(this.supabaseService.getClient().from('group_sidequest_member_status').insert(newStatus).select().single()).pipe(take(1)).subscribe({\n      next: response => {\n        if (response.error) {\n          console.error('Error creating member status:', response.error);\n          return;\n        }\n        this.memberStatus = response.data;\n        // Reload all member statuses\n        this.loadMemberStatuses(sideQuestId);\n      },\n      error: error => {\n        console.error('Error creating member status:', error);\n      }\n    });\n  }\n  toggleSideQuest() {\n    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {\n      return;\n    }\n    this.togglingSideQuest = true;\n    // Toggle the completion status locally first for immediate feedback\n    const newCompletionStatus = !this.memberStatus.completed;\n    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\n    const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD\n    // Update the local member status immediately\n    if (this.memberStatus) {\n      this.memberStatus.completed = newCompletionStatus;\n      this.memberStatus.value_achieved = newValueAchieved;\n      this.memberStatus.last_updated = today; // Update last_updated to today\n    }\n    // Also update the completed members count for immediate feedback\n    if (this.dailyQuest) {\n      // Only update the count if the user is eligible (joined before today)\n      const today = new Date();\n      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison\n      // Check if the current user is eligible\n      from(this.supabaseService.getClient().from('group_members').select('joined_date').eq('group_id', this.groupId).eq('user_id', this.userId).single()).subscribe(response => {\n        if (response.error) {\n          console.error('Error checking eligibility:', response.error);\n          return;\n        }\n        // Check if the current user is eligible (joined before today)\n        const joinedDate = new Date(response.data.joined_date);\n        const isEligible = joinedDate < today; // Before today, not today\n        // Update the completed members count based on eligibility and completion status\n        if (isEligible) {\n          // For immediate feedback, update the count directly\n          if (newCompletionStatus) {\n            // If completing, increment the count\n            this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\n          } else {\n            // If uncompleting, decrement the count\n            if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\n              this.dailyQuest.completed_members_count--;\n            }\n          }\n        } else {}\n        // We don't need to reload member statuses here anymore\n        // We've already updated the counts locally for immediate feedback\n      });\n    }\n    // Send the update to the server in the background\n    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(take(1)).subscribe({\n      next: updatedStatus => {\n        // Log the difference between local and server status\n        // Update with the server response\n        this.memberStatus = updatedStatus;\n        // Update the streak directly in the UI based on completion status\n        if (this.dailyQuest) {\n          // Check if all eligible members have completed the quest\n          const completedCount = this.dailyQuest.completed_members_count || 0;\n          const eligibleCount = this.dailyQuest.eligible_members_count || 0;\n          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;\n          // Check if the quest was previously completed\n          const wasCompleted = this.dailyQuest.completed;\n          // Update the streak and completed status based on the same logic as in the backend\n          if (allCompleted && !wasCompleted) {\n            // All members have completed the quest, increase streak\n            this.dailyQuest.streak += 1;\n            this.dailyQuest.completed = true;\n          } else if (!allCompleted && wasCompleted) {\n            // Quest was previously completed but now it's not, decrease streak\n            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);\n            this.dailyQuest.completed = false;\n          }\n        }\n        this.togglingSideQuest = false;\n      },\n      error: error => {\n        var _this$memberStatus;\n        console.error('Error toggling side quest:', error);\n        // Store the previous last_updated value to revert to\n        const previousLastUpdated = ((_this$memberStatus = this.memberStatus) === null || _this$memberStatus === void 0 ? void 0 : _this$memberStatus.last_updated) || '';\n        // Revert the local changes on error\n        if (this.memberStatus) {\n          this.memberStatus.completed = !newCompletionStatus;\n          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\n          this.memberStatus.last_updated = previousLastUpdated; // Revert last_updated\n        }\n        // Revert the completed members count locally\n        if (this.dailyQuest) {\n          // Only update the count if the user is eligible\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n          // Check if the current user is eligible\n          from(this.supabaseService.getClient().from('group_members').select('joined_date').eq('group_id', this.groupId).eq('user_id', this.userId).single()).subscribe(response => {\n            if (response.error) return;\n            const joinedDate = new Date(response.data.joined_date);\n            const isEligible = joinedDate < today;\n            // Get today's date in YYYY-MM-DD format\n            const todayStr = new Date().toISOString().split('T')[0];\n            const wasUpdatedToday = previousLastUpdated === todayStr;\n            // Revert the count based on eligibility\n            if (isEligible && this.dailyQuest) {\n              if (!newCompletionStatus && wasUpdatedToday) {\n                // If we were uncompleting, revert by adding 1 back\n                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\n              } else if (newCompletionStatus && !wasUpdatedToday) {\n                // If we were completing, revert by subtracting 1\n                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\n                  this.dailyQuest.completed_members_count--;\n                }\n              }\n            }\n          });\n        }\n        this.togglingSideQuest = false;\n        this.showErrorToast('Error updating side quest. Please try again.');\n      }\n    });\n  }\n  showErrorToast(message) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this.toastController.create({\n        message: message,\n        duration: 3000,\n        position: 'bottom',\n        color: 'danger'\n      });\n      yield toast.present();\n    })();\n  }\n  getProgressPercentage() {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {\n      return 0;\n    }\n    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;\n  }\n  getMemberStatusText() {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {\n      return 'No members';\n    }\n    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;\n  }\n}\n_GroupSideQuestComponent = GroupSideQuestComponent;\n_GroupSideQuestComponent.ɵfac = function GroupSideQuestComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GroupSideQuestComponent)(i0.ɵɵdirectiveInject(i1.GroupSideQuestService), i0.ɵɵdirectiveInject(i2.ToastController), i0.ɵɵdirectiveInject(i3.SupabaseService));\n};\n_GroupSideQuestComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GroupSideQuestComponent,\n  selectors: [[\"app-group-sidequest\"]],\n  inputs: {\n    groupId: \"groupId\",\n    userId: \"userId\",\n    joinedDate: \"joinedDate\",\n    isAdmin: \"isAdmin\",\n    enableSidequests: \"enableSidequests\",\n    selectedDate: \"selectedDate\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 1,\n  consts: [[\"class\", \"daily-side-quest\", 4, \"ngIf\"], [1, \"daily-side-quest\"], [\"class\", \"no-sidequest\", 4, \"ngIf\"], [\"class\", \"quest-list\", 4, \"ngIf\"], [1, \"no-sidequest\"], [1, \"quest-list\"], [1, \"quest-item\", 3, \"click\"], [1, \"quest-icon\"], [1, \"quest-info\"], [1, \"progress-container\"], [1, \"progress-text\", \"values\"], [1, \"members-count\"], [1, \"quest-streak\"]],\n  template: function GroupSideQuestComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, GroupSideQuestComponent_section_0_Template, 5, 2, \"section\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.enableSidequests && ctx.isTodaySelected);\n    }\n  },\n  dependencies: [CommonModule, i4.NgIf, IonicModule],\n  styles: [\".daily-side-quest[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n.daily-side-quest[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-bottom: 15px;\\n  color: #fff;\\n  font-weight: 600;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 32px 0;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--ion-color-medium);\\n  margin: 0;\\n}\\n\\n.no-sidequest[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px 0;\\n}\\n.no-sidequest[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--ion-color-medium);\\n  margin-bottom: 16px;\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: var(--quest-bg, #1C1C1E);\\n  border: 1px solid var(--quest-border, #2C2C2E);\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 10px;\\n  position: relative;\\n}\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color, #4169E1);\\n}\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--accent-color, #4169E1);\\n}\\n.quest-item.disabled-quest[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n  pointer-events: none;\\n  position: relative;\\n}\\n.quest-item.disabled-quest[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.2);\\n  pointer-events: none;\\n  border-radius: 8px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n  margin-right: 12px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0;\\n  margin-bottom: 2px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary-text, #8E8E93);\\n  font-size: 12px;\\n  margin: 0;\\n  margin-bottom: 4px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 4px 0;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text, #8E8E93);\\n  margin-top: 2px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .progress-text.values[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .members-count[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-right: 10px;\\n  font-size: 12px;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .members-count[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.quest-message) {\\n  background-color: rgba(65, 105, 225, 0.2);\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  font-size: 11px;\\n  white-space: nowrap;\\n  display: inline-block;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   .quest-message[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n  font-style: italic;\\n  font-size: 12px;\\n  display: block !important;\\n  margin-top: 5px;\\n  font-weight: bold;\\n  text-align: left;\\n  width: 100%;\\n  white-space: normal;\\n  line-height: 1.2;\\n  background-color: transparent !important;\\n  padding: 0 !important;\\n  border-radius: 0 !important;\\n}\\n.quest-item[_ngcontent-%COMP%]   .quest-streak[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  white-space: nowrap;\\n  color: #ff9500;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["CommonModule", "IonicModule", "take", "from", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GroupSideQuestComponent_section_0_div_4_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "isBeforeJoinDate", "togglingSideQuest", "toggleSideQuest", "ɵɵadvance", "ɵɵclassProp", "memberStatus", "completed", "ɵɵtextInterpolate1", "dailyQuest", "current_quest", "emoji", "ɵɵtextInterpolate", "name", "description", "ɵɵtextInterpolate2", "goal_value", "completed_members_count", "eligible_members_count", "streak", "ɵɵtemplate", "GroupSideQuestComponent_section_0_div_3_Template", "GroupSideQuestComponent_section_0_div_4_Template", "ɵɵproperty", "GroupSideQuestComponent", "constructor", "groupSideQuestService", "toastController", "supabaseService", "groupId", "userId", "joinedDate", "isAdmin", "enableSidequests", "selectedDate", "memberStatuses", "isTodaySelected", "ngOnInit", "checkAndResetMemberStatuses", "ensureGroupHasDailySideQuest", "pipe", "subscribe", "next", "sideQuest", "today", "Date", "todayStr", "toISOString", "split", "date_assigned", "loadSideQuest", "getAllMemberStatuses", "id", "statuses", "needsReset", "some", "s", "last_updated", "getClient", "rpc", "sidequest_id", "error", "console", "ngOnChanges", "changes", "setHours", "getTime", "isLoading", "eligibleDate", "setDate", "getDate", "_sideQuest$current_qu", "_sideQuest$current_qu2", "_sideQuest$current_qu3", "_sideQuest$current_qu4", "_sideQuest$current_qu5", "_sideQuest$current_qu6", "group_id", "value_achieved", "last_completed_date", "category", "goal_unit", "loadMemberStatuses", "loadMemberStatus", "sideQuestId", "select", "eq", "response", "eligibleMembers", "data", "filter", "member", "joined_date", "eligibleMemberIds", "map", "m", "user_id", "length", "includes", "member_id", "getMemberStatus", "status", "resetMemberStatusToToday", "createMemberStatus", "existingStatus", "update", "single", "dateString", "newStatus", "group_quest_id", "insert", "newCompletionStatus", "newValueAchieved", "isEligible", "toggleMemberCompletion", "updatedStatus", "completedCount", "eligibleCount", "allCompleted", "wasCompleted", "Math", "max", "_this$memberStatus", "previousLastUpdated", "wasUpdatedToday", "showErrorToast", "message", "_this", "_asyncToGenerator", "toast", "create", "duration", "position", "color", "present", "getProgressPercentage", "getMemberStatusText", "ɵɵdirectiveInject", "i1", "GroupSideQuestService", "i2", "ToastController", "i3", "SupabaseService", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "GroupSideQuestComponent_Template", "rf", "ctx", "GroupSideQuestComponent_section_0_Template", "i4", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\components\\group-sidequest\\group-sidequest.component.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\components\\group-sidequest\\group-sidequest.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { IonicModule } from '@ionic/angular';\nimport { GroupSideQuestService } from '../../services/group-sidequest.service';\nimport { GroupDailyQuest, GroupSideQuestMemberStatus } from '../../models/group-sidequest.model';\nimport { take } from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { ToastController } from '@ionic/angular';\nimport { SupabaseService } from '../../services/supabase.service';\n\n@Component({\n  selector: 'app-group-sidequest',\n  templateUrl: './group-sidequest.component.html',\n  styleUrls: ['./group-sidequest.component.scss'],\n  standalone: true,\n  imports: [CommonModule, IonicModule]\n})\nexport class GroupSideQuestComponent implements OnInit, OnChanges {\n  @Input() groupId: string = '';\n  @Input() userId: string = '';\n  @Input() joinedDate: string = '';\n  @Input() isAdmin: boolean = false;\n  @Input() enableSidequests: boolean = true;\n  @Input() selectedDate: string = '';\n\n  dailyQuest: GroupDailyQuest | null = null;\n  memberStatus: GroupSideQuestMemberStatus | null = null;\n  memberStatuses: GroupSideQuestMemberStatus[] = [];\n  isBeforeJoinDate: boolean = false;\n\n  togglingSideQuest: boolean = false;\n  isTodaySelected: boolean = false;\n\n  constructor(\n    private groupSideQuestService: GroupSideQuestService,\n    private toastController: ToastController,\n    private supabaseService: SupabaseService\n  ) { }\n\n  ngOnInit() {\n    if (this.enableSidequests) {\n      // Check if we need to reset member statuses for a new day\n      this.checkAndResetMemberStatuses();\n    }\n  }\n\n  /**\n   * Check if member statuses need to be reset for a new day\n   * This ensures that completion status is properly tracked each day\n   */\n  checkAndResetMemberStatuses() {\n    // First load the side quest to get its ID\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(\n      take(1)\n    ).subscribe({\n      next: (sideQuest) => {\n        if (!sideQuest) {\n          return;\n        }\n\n        // Get the current date\n        const today = new Date();\n        const todayStr = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD\n\n        // Check if the side quest was assigned today\n        if (sideQuest.date_assigned === todayStr) {\n          this.loadSideQuest();\n          return;\n        }\n\n\n        // Get all member statuses\n        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(\n          take(1)\n        ).subscribe({\n          next: (statuses) => {\n            // Check if any member status has last_updated different from today\n            const needsReset = statuses.some(s => s.last_updated !== todayStr);\n\n            if (needsReset) {\n\n              // Reset all member statuses for the new day\n              from(\n                this.supabaseService.getClient()\n                  .rpc('reset_group_sidequest_member_statuses', { sidequest_id: sideQuest.id })\n              ).subscribe({\n                next: () => {\n                  this.loadSideQuest();\n                },\n                error: (error) => {\n                  console.error('GroupSideQuestComponent: Error resetting member statuses:', error);\n                  this.loadSideQuest();\n                }\n              });\n            } else {\n              this.loadSideQuest();\n            }\n          },\n          error: (error) => {\n            console.error('GroupSideQuestComponent: Error getting member statuses for reset check:', error);\n            this.loadSideQuest();\n          }\n        });\n      },\n      error: (error) => {\n        console.error('GroupSideQuestComponent: Error getting side quest for reset check:', error);\n      }\n    });\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {\n      // Check if selected date is today\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();\n      selectedDate.setHours(0, 0, 0, 0);\n      this.isTodaySelected = selectedDate.getTime() === today.getTime();\n\n\n      // If side quests are disabled, reset the component state\n      if (!this.enableSidequests) {\n        this.dailyQuest = null;\n        this.memberStatus = null;\n        this.isLoading = false;\n        return;\n      }\n\n      this.loadSideQuest();\n    }\n  }\n\n  loadSideQuest() {\n\n    if (!this.groupId || !this.userId) {\n      this.isLoading = false;\n      return;\n    }\n\n    if (!this.enableSidequests) {\n      this.dailyQuest = null;\n      this.memberStatus = null;\n      this.isLoading = false;\n      return;\n    }\n\n    this.isLoading = true;\n\n    // Check if user joined date is before today\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const joinedDate = new Date(this.joinedDate);\n    joinedDate.setHours(0, 0, 0, 0);\n    const eligibleDate = new Date(today);\n    eligibleDate.setDate(today.getDate() - 1);\n    this.isBeforeJoinDate = joinedDate > eligibleDate;\n\n\n    // Always ensure a side quest is loaded from the pool\n    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(\n      take(1)\n    ).subscribe({\n      next: (sideQuest) => {\n\n        // Convert GroupSideQuest to GroupDailyQuest\n        if (sideQuest) {\n          this.dailyQuest = {\n            id: sideQuest.id,\n            group_id: sideQuest.group_id,\n            streak: sideQuest.streak,\n            completed: sideQuest.completed,\n            value_achieved: sideQuest.value_achieved,\n            date_assigned: sideQuest.date_assigned,\n            last_completed_date: sideQuest.last_completed_date,\n            category: sideQuest.category,\n            current_quest: {\n              id: sideQuest.current_quest?.id || '',\n              name: sideQuest.current_quest?.name || '',\n              description: sideQuest.current_quest?.description,\n              goal_value: sideQuest.current_quest?.goal_value || 0,\n              goal_unit: sideQuest.current_quest?.goal_unit || 'count',\n              emoji: sideQuest.current_quest?.emoji || '🎯'\n            },\n            eligible_members_count: 0,\n            completed_members_count: 0\n          };\n        } else {\n          this.dailyQuest = null;\n        }\n\n        // We'll calculate the eligible and completed members counts when loading member statuses\n\n        if (sideQuest) {\n          // Get all member statuses\n          this.loadMemberStatuses(sideQuest.id);\n\n          // Get the current user's status\n          this.loadMemberStatus(sideQuest.id, this.userId);\n        }\n\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('GroupSideQuestComponent: Error loading group side quest:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n\n  loadMemberStatuses(sideQuestId: string) {\n    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(\n      take(1)\n    ).subscribe({\n      next: (statuses) => {\n        this.memberStatuses = statuses;\n\n        // Update the counts in the daily quest\n        if (this.dailyQuest) {\n          // Count eligible members (joined before today)\n          const today = new Date();\n          today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison\n\n          // Get all members with their joined dates\n          from(\n            this.supabaseService.getClient()\n              .from('group_members')\n              .select('user_id, joined_date, nickname')\n              .eq('group_id', this.groupId)\n          ).subscribe(response => {\n            if (response.error) {\n              console.error('Error getting group members:', response.error);\n              return;\n            }\n\n            // Filter eligible members (joined before today)\n            const eligibleMembers = response.data.filter(member => {\n              const joinedDate = new Date(member.joined_date);\n              return joinedDate < today; // Before today, not today\n            });\n\n            // Get eligible member IDs\n            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);\n\n            // Update the eligible members count - this is the total number of members who joined at least a day ago\n            this.dailyQuest!.eligible_members_count = eligibleMembers.length;\n\n            // Get today's date in YYYY-MM-DD format\n            const todayStr = new Date().toISOString().split('T')[0];\n\n            // Count completed members among eligible members only\n            // Only count members who have completed the quest TODAY\n            this.dailyQuest!.completed_members_count = statuses.filter(s =>\n              s.completed && // Status is completed\n              eligibleMemberIds.includes(s.member_id) && // Member is eligible (joined at least a day ago)\n              s.last_updated === todayStr // Status was updated today\n            ).length;\n\n\n\n            // Log detailed status information for debugging\n            // statuses.forEach(s => {\n            //   const member = response.data.find(m => m.user_id === s.member_id);\n            //   const isEligible = eligibleMemberIds.includes(s.member_id);\n            //   const todayStr = new Date().toISOString().split('T')[0];\n            //   const isUpdatedToday = s.last_updated === todayStr;\n            // });\n          });\n        }\n      },\n      error: (error) => {\n        console.error('Error loading member statuses:', error);\n      }\n    });\n  }\n\n  loadMemberStatus(sideQuestId: string, userId: string) {\n    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(\n      take(1)\n    ).subscribe({\n      next: (status) => {\n        if (status) {\n          // Check if the status is from today\n          const today = new Date().toISOString().split('T')[0];\n\n          if (status.last_updated === today) {\n            // Status is from today, use it\n            this.memberStatus = status;\n          } else {\n            // Status is from a previous day, reset it to today's default\n            this.resetMemberStatusToToday(status, sideQuestId);\n          }\n        } else {\n          // Create a new status for this member\n          this.createMemberStatus(sideQuestId, userId);\n        }\n      },\n      error: (error) => {\n        console.error('Error loading member status:', error);\n      }\n    });\n  }\n\n  resetMemberStatusToToday(existingStatus: GroupSideQuestMemberStatus, sideQuestId: string) {\n    const today = new Date().toISOString().split('T')[0];\n\n    // Update the existing status to reset it for today\n    from(\n      this.supabaseService.getClient()\n        .from('group_sidequest_member_status')\n        .update({\n          completed: false,\n          value_achieved: 0,\n          last_updated: today\n        })\n        .eq('id', existingStatus.id)\n        .select()\n        .single()\n    ).pipe(\n      take(1)\n    ).subscribe({\n      next: (response) => {\n        if (response.error) {\n          console.error('Error resetting member status:', response.error);\n          return;\n        }\n\n        this.memberStatus = response.data as GroupSideQuestMemberStatus;\n\n        // Reload all member statuses to update counts\n        this.loadMemberStatuses(sideQuestId);\n      },\n      error: (error) => {\n        console.error('Error resetting member status:', error);\n      }\n    });\n  }\n\n  createMemberStatus(sideQuestId: string, userId: string) {\n    const today = new Date();\n    const dateString = today.toISOString().split('T')[0];\n\n    const newStatus = {\n      group_quest_id: sideQuestId,\n      member_id: userId,\n      completed: false,\n      value_achieved: 0,\n      last_updated: dateString\n    };\n\n    from(\n      this.supabaseService.getClient()\n        .from('group_sidequest_member_status')\n        .insert(newStatus)\n        .select()\n        .single()\n    ).pipe(\n      take(1)\n    ).subscribe({\n      next: (response) => {\n        if (response.error) {\n          console.error('Error creating member status:', response.error);\n          return;\n        }\n\n        this.memberStatus = response.data as GroupSideQuestMemberStatus;\n\n        // Reload all member statuses\n        this.loadMemberStatuses(sideQuestId);\n      },\n      error: (error) => {\n        console.error('Error creating member status:', error);\n      }\n    });\n  }\n\n  toggleSideQuest() {\n    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {\n      return;\n    }\n\n    this.togglingSideQuest = true;\n\n    // Toggle the completion status locally first for immediate feedback\n    const newCompletionStatus = !this.memberStatus.completed;\n    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;\n    const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD\n\n\n\n    // Update the local member status immediately\n    if (this.memberStatus) {\n      this.memberStatus.completed = newCompletionStatus;\n      this.memberStatus.value_achieved = newValueAchieved;\n      this.memberStatus.last_updated = today; // Update last_updated to today\n    }\n\n    // Also update the completed members count for immediate feedback\n    if (this.dailyQuest) {\n      // Only update the count if the user is eligible (joined before today)\n      const today = new Date();\n      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison\n\n      // Check if the current user is eligible\n      from(\n        this.supabaseService.getClient()\n          .from('group_members')\n          .select('joined_date')\n          .eq('group_id', this.groupId)\n          .eq('user_id', this.userId)\n          .single()\n      ).subscribe(response => {\n        if (response.error) {\n          console.error('Error checking eligibility:', response.error);\n          return;\n        }\n\n        // Check if the current user is eligible (joined before today)\n        const joinedDate = new Date(response.data.joined_date);\n        const isEligible = joinedDate < today; // Before today, not today\n\n        // Update the completed members count based on eligibility and completion status\n        if (isEligible) {\n          // For immediate feedback, update the count directly\n          if (newCompletionStatus) {\n            // If completing, increment the count\n            this.dailyQuest!.completed_members_count = (this.dailyQuest!.completed_members_count || 0) + 1;\n          } else {\n            // If uncompleting, decrement the count\n            if (this.dailyQuest!.completed_members_count && this.dailyQuest!.completed_members_count > 0) {\n              this.dailyQuest!.completed_members_count--;\n            }\n          }\n        } else {\n        }\n\n        // We don't need to reload member statuses here anymore\n        // We've already updated the counts locally for immediate feedback\n      });\n    }\n\n    // Send the update to the server in the background\n    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(\n      take(1)\n    ).subscribe({\n      next: (updatedStatus) => {\n\n        // Log the difference between local and server status\n\n\n        // Update with the server response\n        this.memberStatus = updatedStatus;\n\n        // Update the streak directly in the UI based on completion status\n        if (this.dailyQuest) {\n          // Check if all eligible members have completed the quest\n          const completedCount = this.dailyQuest.completed_members_count || 0;\n          const eligibleCount = this.dailyQuest.eligible_members_count || 0;\n          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;\n\n          // Check if the quest was previously completed\n          const wasCompleted = this.dailyQuest.completed;\n\n\n          // Update the streak and completed status based on the same logic as in the backend\n          if (allCompleted && !wasCompleted) {\n            // All members have completed the quest, increase streak\n            this.dailyQuest.streak += 1;\n            this.dailyQuest.completed = true;\n          } else if (!allCompleted && wasCompleted) {\n            // Quest was previously completed but now it's not, decrease streak\n            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);\n            this.dailyQuest.completed = false;\n          }\n        }\n\n        this.togglingSideQuest = false;\n      },\n      error: (error) => {\n        console.error('Error toggling side quest:', error);\n\n        // Store the previous last_updated value to revert to\n        const previousLastUpdated = this.memberStatus?.last_updated || '';\n\n        // Revert the local changes on error\n        if (this.memberStatus) {\n          this.memberStatus.completed = !newCompletionStatus;\n          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest!.current_quest.goal_value : 0;\n          this.memberStatus.last_updated = previousLastUpdated; // Revert last_updated\n        }\n\n        // Revert the completed members count locally\n        if (this.dailyQuest) {\n          // Only update the count if the user is eligible\n          const today = new Date();\n          today.setHours(0, 0, 0, 0);\n\n          // Check if the current user is eligible\n          from(\n            this.supabaseService.getClient()\n              .from('group_members')\n              .select('joined_date')\n              .eq('group_id', this.groupId)\n              .eq('user_id', this.userId)\n              .single()\n          ).subscribe(response => {\n            if (response.error) return;\n\n            const joinedDate = new Date(response.data.joined_date);\n            const isEligible = joinedDate < today;\n\n            // Get today's date in YYYY-MM-DD format\n            const todayStr = new Date().toISOString().split('T')[0];\n            const wasUpdatedToday = previousLastUpdated === todayStr;\n\n            // Revert the count based on eligibility\n            if (isEligible && this.dailyQuest) {\n              if (!newCompletionStatus && wasUpdatedToday) {\n                // If we were uncompleting, revert by adding 1 back\n                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;\n              } else if (newCompletionStatus && !wasUpdatedToday) {\n                // If we were completing, revert by subtracting 1\n                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {\n                  this.dailyQuest.completed_members_count--;\n                }\n              }\n            }\n          });\n        }\n\n        this.togglingSideQuest = false;\n        this.showErrorToast('Error updating side quest. Please try again.');\n      }\n    });\n  }\n\n  async showErrorToast(message: string) {\n    const toast = await this.toastController.create({\n      message: message,\n      duration: 3000,\n      position: 'bottom',\n      color: 'danger'\n    });\n    await toast.present();\n  }\n\n  getProgressPercentage(): number {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {\n      return 0;\n    }\n\n    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;\n  }\n\n  getMemberStatusText(): string {\n    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {\n      return 'No members';\n    }\n\n    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;\n  }\n}\n", "<section class=\"daily-side-quest\" *ngIf=\"enableSidequests && isTodaySelected\">\n  <h2>Daily Group Side Quest</h2>\n\n  <div class=\"no-sidequest\" *ngIf=\"!dailyQuest\">\n    <p>No side quest available for this group.</p>\n    <!-- Side quests are automatically loaded from the pool, no need for manual creation -->\n  </div>\n\n  <div class=\"quest-list\" *ngIf=\"dailyQuest\">\n    <div class=\"quest-item\"\n         [class.completed]=\"memberStatus?.completed\"\n         [class.disabled-quest]=\"isBeforeJoinDate\"\n         (click)=\"!isBeforeJoinDate && !togglingSideQuest && toggleSideQuest()\">\n      <div class=\"quest-icon\">\n        {{ dailyQuest.current_quest.emoji }}\n      </div>\n      <div class=\"quest-info\">\n        <h3>{{ dailyQuest.current_quest.name }}</h3>\n        <p>{{ dailyQuest.current_quest.description }}</p>\n        <div class=\"progress-container\">\n          <div class=\"progress-text values\">\n            <span>\n              {{ memberStatus?.completed ? dailyQuest.current_quest.goal_value : 0 }}/{{ dailyQuest.current_quest.goal_value }}\n            </span>\n            <span class=\"members-count\">\n              Members: {{ dailyQuest.completed_members_count }}/{{ dailyQuest.eligible_members_count }}\n            </span>\n          </div>\n        </div>\n      </div>\n      <div class=\"quest-streak\">\n        🔥{{ dailyQuest.streak }}d\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": ";;AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAG5C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,IAAI,QAAQ,MAAM;;;;;;;;ICFvBC,EADF,CAAAC,cAAA,aAA8C,QACzC;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAE5CF,EAF4C,CAAAG,YAAA,EAAI,EAE1C;;;;;;IAGJH,EADF,CAAAC,cAAA,aAA2C,aAImC;IAAvED,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,EAAAF,MAAA,CAAAG,gBAAA,KAAAH,MAAA,CAAAI,iBAAA,IAAoDJ,MAAA,CAAAK,eAAA,EAAiB;IAAA,EAAC;IACzEb,EAAA,CAAAC,cAAA,aAAwB;IACtBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,aAAwB,SAClB;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG7CH,EAFJ,CAAAC,cAAA,aAAgC,eACI,YAC1B;IACJD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA4B;IAC1BD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAxBCH,EAAA,CAAAc,SAAA,EAA2C;IAC3Cd,EADA,CAAAe,WAAA,cAAAP,MAAA,CAAAQ,YAAA,kBAAAR,MAAA,CAAAQ,YAAA,CAAAC,SAAA,CAA2C,mBAAAT,MAAA,CAAAG,gBAAA,CACF;IAG1CX,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAkB,kBAAA,MAAAV,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAC,KAAA,MACF;IAEMrB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAsB,iBAAA,CAAAd,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAG,IAAA,CAAmC;IACpCvB,EAAA,CAAAc,SAAA,GAA0C;IAA1Cd,EAAA,CAAAsB,iBAAA,CAAAd,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAI,WAAA,CAA0C;IAIvCxB,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAyB,kBAAA,OAAAjB,MAAA,CAAAQ,YAAA,kBAAAR,MAAA,CAAAQ,YAAA,CAAAC,SAAA,IAAAT,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAM,UAAA,WAAAlB,MAAA,CAAAW,UAAA,CAAAC,aAAA,CAAAM,UAAA,MACF;IAEE1B,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAyB,kBAAA,eAAAjB,MAAA,CAAAW,UAAA,CAAAQ,uBAAA,OAAAnB,MAAA,CAAAW,UAAA,CAAAS,sBAAA,MACF;IAKJ5B,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAkB,kBAAA,kBAAAV,MAAA,CAAAW,UAAA,CAAAU,MAAA,OACF;;;;;IA/BJ7B,EADF,CAAAC,cAAA,iBAA8E,SACxE;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAO/BH,EALA,CAAA8B,UAAA,IAAAC,gDAAA,iBAA8C,IAAAC,gDAAA,mBAKH;IA2B7ChC,EAAA,CAAAG,YAAA,EAAU;;;;IAhCmBH,EAAA,CAAAc,SAAA,GAAiB;IAAjBd,EAAA,CAAAiC,UAAA,UAAAzB,MAAA,CAAAW,UAAA,CAAiB;IAKnBnB,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAiC,UAAA,SAAAzB,MAAA,CAAAW,UAAA,CAAgB;;;ADS3C,OAAM,MAAOe,uBAAuB;EAgBlCC,YACUC,qBAA4C,EAC5CC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAlBhB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,gBAAgB,GAAY,IAAI;IAChC,KAAAC,YAAY,GAAW,EAAE;IAElC,KAAAzB,UAAU,GAA2B,IAAI;IACzC,KAAAH,YAAY,GAAsC,IAAI;IACtD,KAAA6B,cAAc,GAAiC,EAAE;IACjD,KAAAlC,gBAAgB,GAAY,KAAK;IAEjC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAkC,eAAe,GAAY,KAAK;EAM5B;EAEJC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACJ,gBAAgB,EAAE;MACzB;MACA,IAAI,CAACK,2BAA2B,EAAE;IACpC;EACF;EAEA;;;;EAIAA,2BAA2BA,CAAA;IACzB;IACA,IAAI,CAACZ,qBAAqB,CAACa,4BAA4B,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,IAAI,CACxEpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGC,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,EAAE;UACd;QACF;QAEA;QACA,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;QACxB,MAAMC,QAAQ,GAAGF,KAAK,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD;QACA,IAAIL,SAAS,CAACM,aAAa,KAAKH,QAAQ,EAAE;UACxC,IAAI,CAACI,aAAa,EAAE;UACpB;QACF;QAGA;QACA,IAAI,CAACxB,qBAAqB,CAACyB,oBAAoB,CAACR,SAAS,CAACS,EAAE,CAAC,CAACZ,IAAI,CAChEpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;UACVC,IAAI,EAAGW,QAAQ,IAAI;YACjB;YACA,MAAMC,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,KAAKX,QAAQ,CAAC;YAElE,IAAIQ,UAAU,EAAE;cAEd;cACAjE,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BC,GAAG,CAAC,uCAAuC,EAAE;gBAAEC,YAAY,EAAEjB,SAAS,CAACS;cAAE,CAAE,CAAC,CAChF,CAACX,SAAS,CAAC;gBACVC,IAAI,EAAEA,CAAA,KAAK;kBACT,IAAI,CAACQ,aAAa,EAAE;gBACtB,CAAC;gBACDW,KAAK,EAAGA,KAAK,IAAI;kBACfC,OAAO,CAACD,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;kBACjF,IAAI,CAACX,aAAa,EAAE;gBACtB;eACD,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAACA,aAAa,EAAE;YACtB;UACF,CAAC;UACDW,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,yEAAyE,EAAEA,KAAK,CAAC;YAC/F,IAAI,CAACX,aAAa,EAAE;UACtB;SACD,CAAC;MACJ,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oEAAoE,EAAEA,KAAK,CAAC;MAC5F;KACD,CAAC;EACJ;EAEAE,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,SAAS,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,kBAAkB,CAAC,IAAIA,OAAO,CAAC,cAAc,CAAC,EAAE;MACrG;MACA,MAAMpB,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM/B,YAAY,GAAG,IAAI,CAACA,YAAY,GAAG,IAAIW,IAAI,CAAC,IAAI,CAACX,YAAY,CAAC,GAAG,IAAIW,IAAI,EAAE;MACjFX,YAAY,CAAC+B,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,IAAI,CAAC7B,eAAe,GAAGF,YAAY,CAACgC,OAAO,EAAE,KAAKtB,KAAK,CAACsB,OAAO,EAAE;MAGjE;MACA,IAAI,CAAC,IAAI,CAACjC,gBAAgB,EAAE;QAC1B,IAAI,CAACxB,UAAU,GAAG,IAAI;QACtB,IAAI,CAACH,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC6D,SAAS,GAAG,KAAK;QACtB;MACF;MAEA,IAAI,CAACjB,aAAa,EAAE;IACtB;EACF;EAEAA,aAAaA,CAAA;IAEX,IAAI,CAAC,IAAI,CAACrB,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;MACjC,IAAI,CAACqC,SAAS,GAAG,KAAK;MACtB;IACF;IAEA,IAAI,CAAC,IAAI,CAAClC,gBAAgB,EAAE;MAC1B,IAAI,CAACxB,UAAU,GAAG,IAAI;MACtB,IAAI,CAACH,YAAY,GAAG,IAAI;MACxB,IAAI,CAAC6D,SAAS,GAAG,KAAK;MACtB;IACF;IAEA,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB;IACA,MAAMvB,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B,MAAMlC,UAAU,GAAG,IAAIc,IAAI,CAAC,IAAI,CAACd,UAAU,CAAC;IAC5CA,UAAU,CAACkC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B,MAAMG,YAAY,GAAG,IAAIvB,IAAI,CAACD,KAAK,CAAC;IACpCwB,YAAY,CAACC,OAAO,CAACzB,KAAK,CAAC0B,OAAO,EAAE,GAAG,CAAC,CAAC;IACzC,IAAI,CAACrE,gBAAgB,GAAG8B,UAAU,GAAGqC,YAAY;IAGjD;IACA,IAAI,CAAC1C,qBAAqB,CAACa,4BAA4B,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,IAAI,CACxEpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGC,SAAS,IAAI;QAElB;QACA,IAAIA,SAAS,EAAE;UAAA,IAAA4B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UACb,IAAI,CAACnE,UAAU,GAAG;YAChB2C,EAAE,EAAET,SAAS,CAACS,EAAE;YAChByB,QAAQ,EAAElC,SAAS,CAACkC,QAAQ;YAC5B1D,MAAM,EAAEwB,SAAS,CAACxB,MAAM;YACxBZ,SAAS,EAAEoC,SAAS,CAACpC,SAAS;YAC9BuE,cAAc,EAAEnC,SAAS,CAACmC,cAAc;YACxC7B,aAAa,EAAEN,SAAS,CAACM,aAAa;YACtC8B,mBAAmB,EAAEpC,SAAS,CAACoC,mBAAmB;YAClDC,QAAQ,EAAErC,SAAS,CAACqC,QAAQ;YAC5BtE,aAAa,EAAE;cACb0C,EAAE,EAAE,EAAAmB,qBAAA,GAAA5B,SAAS,CAACjC,aAAa,cAAA6D,qBAAA,uBAAvBA,qBAAA,CAAyBnB,EAAE,KAAI,EAAE;cACrCvC,IAAI,EAAE,EAAA2D,sBAAA,GAAA7B,SAAS,CAACjC,aAAa,cAAA8D,sBAAA,uBAAvBA,sBAAA,CAAyB3D,IAAI,KAAI,EAAE;cACzCC,WAAW,GAAA2D,sBAAA,GAAE9B,SAAS,CAACjC,aAAa,cAAA+D,sBAAA,uBAAvBA,sBAAA,CAAyB3D,WAAW;cACjDE,UAAU,EAAE,EAAA0D,sBAAA,GAAA/B,SAAS,CAACjC,aAAa,cAAAgE,sBAAA,uBAAvBA,sBAAA,CAAyB1D,UAAU,KAAI,CAAC;cACpDiE,SAAS,EAAE,EAAAN,sBAAA,GAAAhC,SAAS,CAACjC,aAAa,cAAAiE,sBAAA,uBAAvBA,sBAAA,CAAyBM,SAAS,KAAI,OAAO;cACxDtE,KAAK,EAAE,EAAAiE,sBAAA,GAAAjC,SAAS,CAACjC,aAAa,cAAAkE,sBAAA,uBAAvBA,sBAAA,CAAyBjE,KAAK,KAAI;aAC1C;YACDO,sBAAsB,EAAE,CAAC;YACzBD,uBAAuB,EAAE;WAC1B;QACH,CAAC,MAAM;UACL,IAAI,CAACR,UAAU,GAAG,IAAI;QACxB;QAEA;QAEA,IAAIkC,SAAS,EAAE;UACb;UACA,IAAI,CAACuC,kBAAkB,CAACvC,SAAS,CAACS,EAAE,CAAC;UAErC;UACA,IAAI,CAAC+B,gBAAgB,CAACxC,SAAS,CAACS,EAAE,EAAE,IAAI,CAACtB,MAAM,CAAC;QAClD;QAEA,IAAI,CAACqC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0DAA0D,EAAEA,KAAK,CAAC;QAChF,IAAI,CAACM,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAe,kBAAkBA,CAACE,WAAmB;IACpC,IAAI,CAAC1D,qBAAqB,CAACyB,oBAAoB,CAACiC,WAAW,CAAC,CAAC5C,IAAI,CAC/DpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGW,QAAQ,IAAI;QACjB,IAAI,CAAClB,cAAc,GAAGkB,QAAQ;QAE9B;QACA,IAAI,IAAI,CAAC5C,UAAU,EAAE;UACnB;UACA,MAAMmC,KAAK,GAAG,IAAIC,IAAI,EAAE;UACxBD,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAE5B;UACA5E,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BrE,IAAI,CAAC,eAAe,CAAC,CACrBgG,MAAM,CAAC,gCAAgC,CAAC,CACxCC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACzD,OAAO,CAAC,CAChC,CAACY,SAAS,CAAC8C,QAAQ,IAAG;YACrB,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;cAClBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAE0B,QAAQ,CAAC1B,KAAK,CAAC;cAC7D;YACF;YAEA;YACA,MAAM2B,eAAe,GAAGD,QAAQ,CAACE,IAAI,CAACC,MAAM,CAACC,MAAM,IAAG;cACpD,MAAM5D,UAAU,GAAG,IAAIc,IAAI,CAAC8C,MAAM,CAACC,WAAW,CAAC;cAC/C,OAAO7D,UAAU,GAAGa,KAAK,CAAC,CAAC;YAC7B,CAAC,CAAC;YAEF;YACA,MAAMiD,iBAAiB,GAAGL,eAAe,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;YAE7D;YACA,IAAI,CAACvF,UAAW,CAACS,sBAAsB,GAAGsE,eAAe,CAACS,MAAM;YAEhE;YACA,MAAMnD,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAEvD;YACA;YACA,IAAI,CAACvC,UAAW,CAACQ,uBAAuB,GAAGoC,QAAQ,CAACqC,MAAM,CAAClC,CAAC,IAC1DA,CAAC,CAACjD,SAAS;YAAI;YACfsF,iBAAiB,CAACK,QAAQ,CAAC1C,CAAC,CAAC2C,SAAS,CAAC;YAAI;YAC3C3C,CAAC,CAACC,YAAY,KAAKX,QAAQ,CAAC;aAC7B,CAACmD,MAAM;YAIR;YACA;YACA;YACA;YACA;YACA;YACA;UACF,CAAC,CAAC;QACJ;MACF,CAAC;MACDpC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEAsB,gBAAgBA,CAACC,WAAmB,EAAEtD,MAAc;IAClD,IAAI,CAACJ,qBAAqB,CAAC0E,eAAe,CAAChB,WAAW,EAAEtD,MAAM,CAAC,CAACU,IAAI,CAClEpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAG2D,MAAM,IAAI;QACf,IAAIA,MAAM,EAAE;UACV;UACA,MAAMzD,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAEpD,IAAIqD,MAAM,CAAC5C,YAAY,KAAKb,KAAK,EAAE;YACjC;YACA,IAAI,CAACtC,YAAY,GAAG+F,MAAM;UAC5B,CAAC,MAAM;YACL;YACA,IAAI,CAACC,wBAAwB,CAACD,MAAM,EAAEjB,WAAW,CAAC;UACpD;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACmB,kBAAkB,CAACnB,WAAW,EAAEtD,MAAM,CAAC;QAC9C;MACF,CAAC;MACD+B,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACJ;EAEAyC,wBAAwBA,CAACE,cAA0C,EAAEpB,WAAmB;IACtF,MAAMxC,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpD;IACA3D,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BrE,IAAI,CAAC,+BAA+B,CAAC,CACrCoH,MAAM,CAAC;MACNlG,SAAS,EAAE,KAAK;MAChBuE,cAAc,EAAE,CAAC;MACjBrB,YAAY,EAAEb;KACf,CAAC,CACD0C,EAAE,CAAC,IAAI,EAAEkB,cAAc,CAACpD,EAAE,CAAC,CAC3BiC,MAAM,EAAE,CACRqB,MAAM,EAAE,CACZ,CAAClE,IAAI,CACJpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAG6C,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;UAClBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAE0B,QAAQ,CAAC1B,KAAK,CAAC;UAC/D;QACF;QAEA,IAAI,CAACvD,YAAY,GAAGiF,QAAQ,CAACE,IAAkC;QAE/D;QACA,IAAI,CAACP,kBAAkB,CAACE,WAAW,CAAC;MACtC,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEA0C,kBAAkBA,CAACnB,WAAmB,EAAEtD,MAAc;IACpD,MAAMc,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAM8D,UAAU,GAAG/D,KAAK,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEpD,MAAM4D,SAAS,GAAG;MAChBC,cAAc,EAAEzB,WAAW;MAC3Be,SAAS,EAAErE,MAAM;MACjBvB,SAAS,EAAE,KAAK;MAChBuE,cAAc,EAAE,CAAC;MACjBrB,YAAY,EAAEkD;KACf;IAEDtH,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BrE,IAAI,CAAC,+BAA+B,CAAC,CACrCyH,MAAM,CAACF,SAAS,CAAC,CACjBvB,MAAM,EAAE,CACRqB,MAAM,EAAE,CACZ,CAAClE,IAAI,CACJpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAG6C,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;UAClBC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAE0B,QAAQ,CAAC1B,KAAK,CAAC;UAC9D;QACF;QAEA,IAAI,CAACvD,YAAY,GAAGiF,QAAQ,CAACE,IAAkC;QAE/D;QACA,IAAI,CAACP,kBAAkB,CAACE,WAAW,CAAC;MACtC,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEA1D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC0B,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACrB,UAAU,IAAI,CAAC,IAAI,CAACH,YAAY,IAAI,IAAI,CAACL,gBAAgB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MAC9H;IACF;IAEA,IAAI,CAACA,iBAAiB,GAAG,IAAI;IAE7B;IACA,MAAM6G,mBAAmB,GAAG,CAAC,IAAI,CAACzG,YAAY,CAACC,SAAS;IACxD,MAAMyG,gBAAgB,GAAGD,mBAAmB,GAAG,IAAI,CAACtG,UAAU,CAACC,aAAa,CAACM,UAAU,GAAG,CAAC;IAC3F,MAAM4B,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAItD;IACA,IAAI,IAAI,CAAC1C,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,SAAS,GAAGwG,mBAAmB;MACjD,IAAI,CAACzG,YAAY,CAACwE,cAAc,GAAGkC,gBAAgB;MACnD,IAAI,CAAC1G,YAAY,CAACmD,YAAY,GAAGb,KAAK,CAAC,CAAC;IAC1C;IAEA;IACA,IAAI,IAAI,CAACnC,UAAU,EAAE;MACnB;MACA,MAAMmC,KAAK,GAAG,IAAIC,IAAI,EAAE;MACxBD,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAE5B;MACA5E,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BrE,IAAI,CAAC,eAAe,CAAC,CACrBgG,MAAM,CAAC,aAAa,CAAC,CACrBC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACzD,OAAO,CAAC,CAC5ByD,EAAE,CAAC,SAAS,EAAE,IAAI,CAACxD,MAAM,CAAC,CAC1B4E,MAAM,EAAE,CACZ,CAACjE,SAAS,CAAC8C,QAAQ,IAAG;QACrB,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;UAClBC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAE0B,QAAQ,CAAC1B,KAAK,CAAC;UAC5D;QACF;QAEA;QACA,MAAM9B,UAAU,GAAG,IAAIc,IAAI,CAAC0C,QAAQ,CAACE,IAAI,CAACG,WAAW,CAAC;QACtD,MAAMqB,UAAU,GAAGlF,UAAU,GAAGa,KAAK,CAAC,CAAC;QAEvC;QACA,IAAIqE,UAAU,EAAE;UACd;UACA,IAAIF,mBAAmB,EAAE;YACvB;YACA,IAAI,CAACtG,UAAW,CAACQ,uBAAuB,GAAG,CAAC,IAAI,CAACR,UAAW,CAACQ,uBAAuB,IAAI,CAAC,IAAI,CAAC;UAChG,CAAC,MAAM;YACL;YACA,IAAI,IAAI,CAACR,UAAW,CAACQ,uBAAuB,IAAI,IAAI,CAACR,UAAW,CAACQ,uBAAuB,GAAG,CAAC,EAAE;cAC5F,IAAI,CAACR,UAAW,CAACQ,uBAAuB,EAAE;YAC5C;UACF;QACF,CAAC,MAAM,CACP;QAEA;QACA;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACS,qBAAqB,CAACwF,sBAAsB,CAAC,IAAI,CAAC5G,YAAY,CAAC8C,EAAE,EAAE,IAAI,CAACvB,OAAO,CAAC,CAACW,IAAI,CACxFpD,IAAI,CAAC,CAAC,CAAC,CACR,CAACqD,SAAS,CAAC;MACVC,IAAI,EAAGyE,aAAa,IAAI;QAEtB;QAGA;QACA,IAAI,CAAC7G,YAAY,GAAG6G,aAAa;QAEjC;QACA,IAAI,IAAI,CAAC1G,UAAU,EAAE;UACnB;UACA,MAAM2G,cAAc,GAAG,IAAI,CAAC3G,UAAU,CAACQ,uBAAuB,IAAI,CAAC;UACnE,MAAMoG,aAAa,GAAG,IAAI,CAAC5G,UAAU,CAACS,sBAAsB,IAAI,CAAC;UACjE,MAAMoG,YAAY,GAAGF,cAAc,KAAKC,aAAa,IAAIA,aAAa,GAAG,CAAC;UAE1E;UACA,MAAME,YAAY,GAAG,IAAI,CAAC9G,UAAU,CAACF,SAAS;UAG9C;UACA,IAAI+G,YAAY,IAAI,CAACC,YAAY,EAAE;YACjC;YACA,IAAI,CAAC9G,UAAU,CAACU,MAAM,IAAI,CAAC;YAC3B,IAAI,CAACV,UAAU,CAACF,SAAS,GAAG,IAAI;UAClC,CAAC,MAAM,IAAI,CAAC+G,YAAY,IAAIC,YAAY,EAAE;YACxC;YACA,IAAI,CAAC9G,UAAU,CAACU,MAAM,GAAGqG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAChH,UAAU,CAACU,MAAM,GAAG,CAAC,CAAC;YAChE,IAAI,CAACV,UAAU,CAACF,SAAS,GAAG,KAAK;UACnC;QACF;QAEA,IAAI,CAACL,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACD2D,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAA6D,kBAAA;QACf5D,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAElD;QACA,MAAM8D,mBAAmB,GAAG,EAAAD,kBAAA,OAAI,CAACpH,YAAY,cAAAoH,kBAAA,uBAAjBA,kBAAA,CAAmBjE,YAAY,KAAI,EAAE;QAEjE;QACA,IAAI,IAAI,CAACnD,YAAY,EAAE;UACrB,IAAI,CAACA,YAAY,CAACC,SAAS,GAAG,CAACwG,mBAAmB;UAClD,IAAI,CAACzG,YAAY,CAACwE,cAAc,GAAG,CAACiC,mBAAmB,GAAG,IAAI,CAACtG,UAAW,CAACC,aAAa,CAACM,UAAU,GAAG,CAAC;UACvG,IAAI,CAACV,YAAY,CAACmD,YAAY,GAAGkE,mBAAmB,CAAC,CAAC;QACxD;QAEA;QACA,IAAI,IAAI,CAAClH,UAAU,EAAE;UACnB;UACA,MAAMmC,KAAK,GAAG,IAAIC,IAAI,EAAE;UACxBD,KAAK,CAACqB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAE1B;UACA5E,IAAI,CACF,IAAI,CAACuC,eAAe,CAAC8B,SAAS,EAAE,CAC7BrE,IAAI,CAAC,eAAe,CAAC,CACrBgG,MAAM,CAAC,aAAa,CAAC,CACrBC,EAAE,CAAC,UAAU,EAAE,IAAI,CAACzD,OAAO,CAAC,CAC5ByD,EAAE,CAAC,SAAS,EAAE,IAAI,CAACxD,MAAM,CAAC,CAC1B4E,MAAM,EAAE,CACZ,CAACjE,SAAS,CAAC8C,QAAQ,IAAG;YACrB,IAAIA,QAAQ,CAAC1B,KAAK,EAAE;YAEpB,MAAM9B,UAAU,GAAG,IAAIc,IAAI,CAAC0C,QAAQ,CAACE,IAAI,CAACG,WAAW,CAAC;YACtD,MAAMqB,UAAU,GAAGlF,UAAU,GAAGa,KAAK;YAErC;YACA,MAAME,QAAQ,GAAG,IAAID,IAAI,EAAE,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM4E,eAAe,GAAGD,mBAAmB,KAAK7E,QAAQ;YAExD;YACA,IAAImE,UAAU,IAAI,IAAI,CAACxG,UAAU,EAAE;cACjC,IAAI,CAACsG,mBAAmB,IAAIa,eAAe,EAAE;gBAC3C;gBACA,IAAI,CAACnH,UAAU,CAACQ,uBAAuB,GAAG,CAAC,IAAI,CAACR,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,CAAC;cAC9F,CAAC,MAAM,IAAI8F,mBAAmB,IAAI,CAACa,eAAe,EAAE;gBAClD;gBACA,IAAI,IAAI,CAACnH,UAAU,CAACQ,uBAAuB,IAAI,IAAI,CAACR,UAAU,CAACQ,uBAAuB,GAAG,CAAC,EAAE;kBAC1F,IAAI,CAACR,UAAU,CAACQ,uBAAuB,EAAE;gBAC3C;cACF;YACF;UACF,CAAC,CAAC;QACJ;QAEA,IAAI,CAACf,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC2H,cAAc,CAAC,8CAA8C,CAAC;MACrE;KACD,CAAC;EACJ;EAEMA,cAAcA,CAACC,OAAe;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClC,MAAMC,KAAK,SAASF,KAAI,CAACpG,eAAe,CAACuG,MAAM,CAAC;QAC9CJ,OAAO,EAAEA,OAAO;QAChBK,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;OACR,CAAC;MACF,MAAMJ,KAAK,CAACK,OAAO,EAAE;IAAC;EACxB;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC9H,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACS,sBAAsB,IAAI,IAAI,CAACT,UAAU,CAACS,sBAAsB,KAAK,CAAC,EAAE;MAC/G,OAAO,CAAC;IACV;IAEA,OAAO,CAAC,IAAI,CAACT,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,IAAI,CAACR,UAAU,CAACS,sBAAsB,GAAG,GAAG;EACtG;EAEAsH,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC/H,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACS,sBAAsB,EAAE;MAC/D,OAAO,YAAY;IACrB;IAEA,OAAO,GAAG,IAAI,CAACT,UAAU,CAACQ,uBAAuB,IAAI,CAAC,IAAI,IAAI,CAACR,UAAU,CAACS,sBAAsB,YAAY;EAC9G;;2BA9hBWM,uBAAuB;;mCAAvBA,wBAAuB,EAAAlC,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAArJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvJ,EAAA,CAAAmJ,iBAAA,CAAAK,EAAA,CAAAC,eAAA;AAAA;;QAAvBvH,wBAAuB;EAAAwH,SAAA;EAAAC,MAAA;IAAApH,OAAA;IAAAC,MAAA;IAAAC,UAAA;IAAAC,OAAA;IAAAC,gBAAA;IAAAC,YAAA;EAAA;EAAAgH,QAAA,GAAA5J,EAAA,CAAA6J,oBAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCjBpCnK,EAAA,CAAA8B,UAAA,IAAAuI,0CAAA,qBAA8E;;;MAA3CrK,EAAA,CAAAiC,UAAA,SAAAmI,GAAA,CAAAzH,gBAAA,IAAAyH,GAAA,CAAAtH,eAAA,CAAyC;;;iBDehElD,YAAY,EAAA0K,EAAA,CAAAC,IAAA,EAAE1K,WAAW;EAAA2K,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}