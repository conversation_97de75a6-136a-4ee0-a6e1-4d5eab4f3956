:host {
    --bg: #0c0c0f;
    --card: #121217;
    --pill: #1c1c1e;
    --text: #fff;
    --text-muted: #8e8e93;
    --accent: #4d7bff;
    --radius: 16px;
    --background-color: #0C0C0F;
    --text-color: #FFFFFF;
    --secondary-text: #8E8E93;
    --accent-color: #4169E1;
    --quest-bg: #1C1C1E;
    --quest-border: #2C2C2E;
}

header {
    display: flex
    ;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
}
h1, .page-title {
    font-size: 20px;
    font-weight: 600;
}conti
body.dark-theme {
    background-color: var(--background-color);
    color: var(--text-color);
    min-height: 100vh;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
    width: 480px;
    padding: 20px;
    overflow-y: auto;
    scrollbar-width: none;
}
.container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 22px;
}

.logo span {
    font-size: 18px;
    font-weight: bold;
}

.goal-card {
    background: var(--card);
    padding: 20px;
    border-radius: var(--radius);
    margin-bottom: 20px;
    box-shadow: 0 0 0 1px #1e1e1e;
    margin-top: 10px;
}

.description-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.goal-desc {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 10px;
    flex: 1;
}

.edit-bio-btn {
    background: none;
    border: none;
    color: var(--accent);
    font-size: 12px;
    cursor: pointer;
    padding: 2px 5px;
    margin-left: 10px;
}

.description-form {
    margin-bottom: 15px;
}

.description-form textarea {
    width: 100%;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    margin-bottom: 10px;
    min-height: 80px;
    resize: vertical;
}

.save-btn, .cancel-btn {
    padding: 8px 12px;
    border-radius: var(--radius);
    font-size: 14px;
    cursor: pointer;
    margin-right: 10px;
}

.save-btn {
    background: var(--accent);
    color: white;
    border: none;
}

.cancel-btn {
    background: #2c2c2e;
    color: white;
    border: none;
}

.delete-goal-container {
    margin-top: 30px;
    text-align: center;
    margin-bottom: 100px;
}

.delete-goal-btn {
    background: rgba(255, 59, 48, 0.2);
    color: #FF3B30;
    border: 1px solid #FF3B30;
    padding: 10px 16px;
    border-radius: var(--radius);
    font-size: 14px;
    cursor: pointer;
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    height: 10px;
    background: #2c2c2e;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 6px;
}

.progress-fill {
    height: 100%;
    background: var(--accent);
    transition: width 0.3s ease-in-out;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.progress-update input {
    width: 100%;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    margin-top: 10px;
}

button {
    background: var(--accent);
    color: white;
    font-weight: 600;
    border: none;
    padding: 10px 16px;
    border-radius: var(--radius);
    cursor: pointer;
    margin-top: 10px;
}

.microgoals-section {
    margin-bottom: 24px;
}

.microgoals-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 12px;
    margin-top: 12px;
}

.microgoal-form {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 6px;
}

.microgoal-add input {
    width: 100%;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    margin-bottom: 8px;
}

.microgoal-add button {
    width: 100%;
}

.journal-section {
    background: var(--pill);
    padding: 16px;
    border-radius: var(--radius);
}

.journal-entry {
    border-top: 1px solid #2c2c2e;
    padding: 10px;
    margin-top: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 8px;
    display: flex;
    align-items: center;
    position: relative;
}

.journal-entry:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.journal-entry:first-child {
    border-top: none;
    padding-top: 10px;
    margin-top: 0;
}

.journal-entry-content {
    flex: 1;
    min-width: 0;
}

.journal-entry-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
}

.milestone {
    font-weight: 600;
    color: var(--accent);
    margin-right: 8px;
}

.journal-arrow {
    color: var(--text-muted);
    font-size: 18px;
    font-weight: bold;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

.journal-preview {
    color: var(--text-muted);
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 50%;
}

.journal-modal {
    margin-top: 20px;
    background: var(--card);
    padding: 14px;
    border-radius: var(--radius);
    text-align: center;

    .toolbar {
        display: flex;
        gap: 12px;
        padding: 8px;
        background: var(--background);
        border-radius: 6px;
        margin-bottom: 12px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .toolbar-group {
        display: flex;
        gap: 4px;
        border-right: 1px solid #3a3a3c;
        padding-right: 12px;

        &:last-child {
            border-right: none;
            padding-right: 0;
        }
    }

    .toolbar-btn {
        background: transparent;
        border: 1px solid #3a3a3c;
        border-radius: 4px;
        padding: 6px 10px;
        color: var(--text);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        font-size: 12px;

        &:hover {
            background: var(--card);
        }

        &:active {
            background: #3a3a3c;
        }
    }
}

.rich-editor-small {
    min-height: 120px;
    padding: 12px;
    border: 1px solid #3a3a3c;
    border-radius: 6px;
    background: var(--background);
    color: var(--text);
    font-size: 14px;
    line-height: 1.5;
    outline: none;
    margin-bottom: 12px;
    text-align: left;

    &:focus {
        border-color: var(--accent);
    }

    &[contenteditable]:empty::before {
        content: attr(placeholder);
        color: var(--text-muted);
        font-style: italic;
    }

    h3 {
        font-size: 16px;
        margin: 8px 0 4px 0;
        font-weight: 600;
    }

    p {
        margin: 6px 0;
    }

    ul, ol {
        margin: 6px 0;
        padding-left: 20px;
    }

    li {
        margin: 3px 0;
    }

    .notion-quote {
        border-left: 4px solid var(--accent);
        padding: 8px 12px;
        margin: 12px 0;
        background: rgba(0, 122, 255, 0.1);
        border-radius: 0 6px 6px 0;
        font-style: italic;
        color: var(--text-muted);

        &:focus {
            outline: none;
            background: rgba(0, 122, 255, 0.15);
        }
    }
}

.journal-link {
    display: inline-block;
    margin-top: 10px;
    color: var(--accent);
    text-decoration: underline;
}


.container {
    max-width: 480px;
    padding: 20px;
    margin-left: auto;
    margin-right: auto;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    height: 22px;
}

.logo span {
    font-size: 18px;
    font-weight: bold;
}

.top-date {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-muted);
}

.page-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 20px;
}

.goal-card {
    background: var(--card);
    padding: 20px;
    border-radius: var(--radius);
    margin-bottom: 20px;
    box-shadow: 0 0 0 1px #1e1e1e;
}

.goal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.goal-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.goal-percent {
    font-size: 14px;
    font-weight: 500;
    color: var(--accent);
}

.progress-bar {
    height: 10px;
    background: #2c2c2e;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: var(--accent);
    transition: width 0.3s ease-in-out;
}

.goal-value {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 12px;
}

.goal-link {
    font-weight: 500;
    color: var(--accent);
    text-decoration: none;
    display: inline-block;
}

.goal-link:hover {
    text-decoration: underline;
}

.no-goals {
    text-align: center;
    color: var(--text-muted);
    margin-top: 40px;
}
.delete-btn {
    background: none;
    border: none;
    color: #ff4d4d;
    cursor: pointer;
    margin-left: 8px;
    font-size: 16px;
    vertical-align: middle;
}

.delete-btn:hover {
    color: red;
}
.microgoal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--pill);
    padding: 10px 14px;
    margin-bottom: 8px;
    border-radius: 12px;
}

.microgoal-form {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.micro-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: var(--accent);
}

.checkmark {
    font-size: 20px;
    color: var(--text-muted);
}

.checkmark.checked {
    color: #4cd964;
}

.microgoal-title {
    font-size: 15px;
    font-weight: 500;
    color: var(--text);
}

.delete-form {
    margin-left: auto;
}

.delete-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #ff4d6d;
    cursor: pointer;
}

.journal-modal.fancy {
    background-color: var(--pill);
    border-radius: 16px;
    padding: 20px;
    margin-top: 24px;
    text-align: center;
    box-shadow: 0 0 0 1px #1e1e1e;
}

.journal-modal.fancy p {
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 12px;
}

.journal-link {
    font-weight: 600;
    font-size: 14px;
    text-decoration: underline;
    color: var(--accent);
}

.milestone-highlight {
    color: var(--accent);
    font-weight: 600;
}

.micro-btn, .delete-btn {
    margin: 0;
}
.inline-journal-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 12px;
}

.inline-journal-form textarea {
    background-color: var(--pill);
    border: 1px solid var(--quest-border);
    border-radius: 10px;
    padding: 10px;
    color: var(--text);
    resize: vertical;
    font-size: 14px;
}

.inline-journal-form button {
    align-self: flex-start;
    background-color: var(--accent);
    border: none;
    padding: 10px 16px;
    color: white;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.inline-journal-form button:hover {
    opacity: 0.9;
}
.save-bar {
    display: flex;
}
.save-bar button {
    border-radius: 0 16px 16px 0;
}
.save-bar input {
    border-radius: 16px 0 0 16px;
}
.goal-card-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.goal-card-link:hover .goal-card {
    box-shadow: 0 0 0 1px var(--accent-color);
    transition: box-shadow 0.2s;
}
.goal-value{
    display: flex;
    justify-content: space-between;
}

.logo-wrap {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
}

.back-link {
    font-size: 14px;
    color: var(--text-muted);
    text-decoration: none;
    margin-left: 2px;
    transition: color 0.2s ease;
    margin-bottom: 10px;

}

.back-link:hover {
    color: var(--accent);
}

.goal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.goal-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.goal-emoji {
    font-size: 20px;
    line-height: 1;
}

.goal-name {
    font-weight: 600;
    font-size: 16px;
}

/* Add Goal Button */
.add-quest-link {
    display: flex;
    align-items: center;
    color: var(--accent);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 6px;
    background-color: rgba(77, 123, 255, 0.1);
    transition: background-color 0.2s;
}

.add-quest-link:hover {
    background-color: rgba(77, 123, 255, 0.2);
}

.add-quest-icon {
    margin-right: 4px;
    font-size: 16px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    background-color: transparent;
    margin: 5% auto;
    padding: 20px;
    width: 90%;
    max-width: 500px;
    position: relative;
    color: white;
    max-height: 80vh;
    overflow-y: auto;
}

.close-modal {
    color: #666;
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: white;
}

.modal h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: white;
}

.form-group {
    margin-bottom: 18px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #CCC;
    font-size: 14px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    background-color: #1C1C1E;
    border: 1px solid #3C3C3E;
    border-radius: 8px;
    color: white;
    font-size: 14px;
}

.form-group textarea {
    height: 80px;
    width: 95%;
    resize: vertical;
}

#emoji {
    width: 60px;
    text-align: center;
    background-color: #1C1C1E;
    border-radius: 8px;
    padding: 10px;
}

.goal-inputs {
    display: flex;
    gap: 8px;
}

.goal-inputs input {
    width: 80px;
}

.goal-inputs select {
    flex-grow: 1;
}

.submit-btn {
    background: var(--accent);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    margin-top: 20px;
    transition: background 0.2s;
}

.submit-btn:hover {
    background-color: #3A57C2;
}

/* Goal Settings */
.goal-settings {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--quest-border);
}

.public-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.public-toggle label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text);
    cursor: pointer;
}

.public-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--accent);
    cursor: pointer;
}

/* Goal Info and Editing */
.goal-info-container {
    margin-bottom: 15px;
}

.goal-info-container h2 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.description-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.edit-form {
    margin-bottom: 15px;
}

.title-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.emoji-input {
    width: 60px;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    text-align: center;
    font-size: 16px;
}

.name-input {
    flex: 1;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    font-size: 14px;
}

.description-textarea {
    width: 100%;
    padding: 10px;
    background: var(--pill);
    color: white;
    border: 1px solid #2c2c2e;
    border-radius: var(--radius);
    margin-bottom: 10px;
    min-height: 80px;
    resize: vertical;
    font-size: 14px;
}

.form-buttons {
    display: flex;
    gap: 10px;
}
