import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { GroupSideQuestService } from '../../services/group-sidequest.service';
import { GroupDailyQuest, GroupSideQuestMemberStatus } from '../../models/group-sidequest.model';
import { take } from 'rxjs/operators';
import { from } from 'rxjs';
import { ToastController } from '@ionic/angular';
import { SupabaseService } from '../../services/supabase.service';

@Component({
  selector: 'app-group-sidequest',
  templateUrl: './group-sidequest.component.html',
  styleUrls: ['./group-sidequest.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class GroupSideQuestComponent implements OnInit, OnChanges {
  @Input() groupId: string = '';
  @Input() userId: string = '';
  @Input() joinedDate: string = '';
  @Input() isAdmin: boolean = false;
  @Input() enableSidequests: boolean = true;
  @Input() selectedDate: string = '';

  dailyQuest: GroupDailyQuest | null = null;
  memberStatus: GroupSideQuestMemberStatus | null = null;
  memberStatuses: GroupSideQuestMemberStatus[] = [];
  isBeforeJoinDate: boolean = false;
  isLoading: boolean = true;
  togglingSideQuest: boolean = false;
  isTodaySelected: boolean = false;

  constructor(
    private groupSideQuestService: GroupSideQuestService,
    private toastController: ToastController,
    private supabaseService: SupabaseService
  ) { }

  ngOnInit() {
    if (this.enableSidequests) {
      // Check if we need to reset member statuses for a new day
      this.checkAndResetMemberStatuses();
    } else {
      this.isLoading = false;
    }
  }

  /**
   * Check if member statuses need to be reset for a new day
   * This ensures that completion status is properly tracked each day
   */
  checkAndResetMemberStatuses() {
    // First load the side quest to get its ID
    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (sideQuest) => {
        if (!sideQuest) {
          this.isLoading = false;
          return;
        }

        // Get the current date
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD

        // Check if the side quest was assigned today
        if (sideQuest.date_assigned === todayStr) {
          this.loadSideQuest();
          return;
        }


        // Get all member statuses
        this.groupSideQuestService.getAllMemberStatuses(sideQuest.id).pipe(
          take(1)
        ).subscribe({
          next: (statuses) => {
            // Check if any member status has last_updated different from today
            const needsReset = statuses.some(s => s.last_updated !== todayStr);

            if (needsReset) {

              // Reset all member statuses for the new day
              from(
                this.supabaseService.getClient()
                  .rpc('reset_group_sidequest_member_statuses', { sidequest_id: sideQuest.id })
              ).subscribe({
                next: () => {
                  this.loadSideQuest();
                },
                error: (error) => {
                  console.error('GroupSideQuestComponent: Error resetting member statuses:', error);
                  this.loadSideQuest();
                }
              });
            } else {
              this.loadSideQuest();
            }
          },
          error: (error) => {
            console.error('GroupSideQuestComponent: Error getting member statuses for reset check:', error);
            this.loadSideQuest();
          }
        });
      },
      error: (error) => {
        console.error('GroupSideQuestComponent: Error getting side quest for reset check:', error);
        this.isLoading = false;
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['groupId'] || changes['userId'] || changes['enableSidequests'] || changes['selectedDate']) {
      // Check if selected date is today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const selectedDate = this.selectedDate ? new Date(this.selectedDate) : new Date();
      selectedDate.setHours(0, 0, 0, 0);
      this.isTodaySelected = selectedDate.getTime() === today.getTime();


      // If side quests are disabled, reset the component state
      if (!this.enableSidequests) {
        this.dailyQuest = null;
        this.memberStatus = null;
        this.isLoading = false;
        return;
      }

      this.loadSideQuest();
    }
  }

  loadSideQuest() {

    if (!this.groupId || !this.userId) {
      this.isLoading = false;
      return;
    }

    if (!this.enableSidequests) {
      this.dailyQuest = null;
      this.memberStatus = null;
      this.isLoading = false;
      return;
    }

    this.isLoading = true;

    // Check if user joined date is before today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const joinedDate = new Date(this.joinedDate);
    joinedDate.setHours(0, 0, 0, 0);
    const eligibleDate = new Date(today);
    eligibleDate.setDate(today.getDate() - 1);
    this.isBeforeJoinDate = joinedDate > eligibleDate;


    // Always ensure a side quest is loaded from the pool
    this.groupSideQuestService.ensureGroupHasDailySideQuest(this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (sideQuest) => {

        // Convert GroupSideQuest to GroupDailyQuest
        if (sideQuest) {
          this.dailyQuest = {
            id: sideQuest.id,
            group_id: sideQuest.group_id,
            streak: sideQuest.streak,
            completed: sideQuest.completed,
            value_achieved: sideQuest.value_achieved,
            date_assigned: sideQuest.date_assigned,
            last_completed_date: sideQuest.last_completed_date,
            category: sideQuest.category,
            current_quest: {
              id: sideQuest.current_quest?.id || '',
              name: sideQuest.current_quest?.name || '',
              description: sideQuest.current_quest?.description,
              goal_value: sideQuest.current_quest?.goal_value || 0,
              goal_unit: sideQuest.current_quest?.goal_unit || 'count',
              emoji: sideQuest.current_quest?.emoji || '🎯'
            },
            eligible_members_count: 0,
            completed_members_count: 0
          };
        } else {
          this.dailyQuest = null;
        }

        // We'll calculate the eligible and completed members counts when loading member statuses

        if (sideQuest) {
          // Get all member statuses
          this.loadMemberStatuses(sideQuest.id);

          // Get the current user's status
          this.loadMemberStatus(sideQuest.id, this.userId);
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('GroupSideQuestComponent: Error loading group side quest:', error);
        this.isLoading = false;
      }
    });
  }

  loadMemberStatuses(sideQuestId: string) {
    this.groupSideQuestService.getAllMemberStatuses(sideQuestId).pipe(
      take(1)
    ).subscribe({
      next: (statuses) => {
        this.memberStatuses = statuses;

        // Update the counts in the daily quest
        if (this.dailyQuest) {
          // Count eligible members (joined before today)
          const today = new Date();
          today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

          // Get all members with their joined dates
          from(
            this.supabaseService.getClient()
              .from('group_members')
              .select('user_id, joined_date, nickname')
              .eq('group_id', this.groupId)
          ).subscribe(response => {
            if (response.error) {
              console.error('Error getting group members:', response.error);
              return;
            }

            // Filter eligible members (joined before today)
            const eligibleMembers = response.data.filter(member => {
              const joinedDate = new Date(member.joined_date);
              return joinedDate < today; // Before today, not today
            });

            // Get eligible member IDs
            const eligibleMemberIds = eligibleMembers.map(m => m.user_id);

            // Update the eligible members count - this is the total number of members who joined at least a day ago
            this.dailyQuest!.eligible_members_count = eligibleMembers.length;

            // Get today's date in YYYY-MM-DD format
            const todayStr = new Date().toISOString().split('T')[0];

            // Count completed members among eligible members only
            // Only count members who have completed the quest TODAY
            this.dailyQuest!.completed_members_count = statuses.filter(s =>
              s.completed && // Status is completed
              eligibleMemberIds.includes(s.member_id) && // Member is eligible (joined at least a day ago)
              s.last_updated === todayStr // Status was updated today
            ).length;



            // Log detailed status information for debugging
            // statuses.forEach(s => {
            //   const member = response.data.find(m => m.user_id === s.member_id);
            //   const isEligible = eligibleMemberIds.includes(s.member_id);
            //   const todayStr = new Date().toISOString().split('T')[0];
            //   const isUpdatedToday = s.last_updated === todayStr;
            // });
          });
        }
      },
      error: (error) => {
        console.error('Error loading member statuses:', error);
      }
    });
  }

  loadMemberStatus(sideQuestId: string, userId: string) {
    this.groupSideQuestService.getMemberStatus(sideQuestId, userId).pipe(
      take(1)
    ).subscribe({
      next: (status) => {
        if (status) {
          // Check if the status is from today
          const today = new Date().toISOString().split('T')[0];

          if (status.last_updated === today) {
            // Status is from today, use it
            this.memberStatus = status;
          } else {
            // Status is from a previous day, reset it to today's default
            this.resetMemberStatusToToday(status, sideQuestId);
          }
        } else {
          // Create a new status for this member
          this.createMemberStatus(sideQuestId, userId);
        }
      },
      error: (error) => {
        console.error('Error loading member status:', error);
      }
    });
  }

  resetMemberStatusToToday(existingStatus: GroupSideQuestMemberStatus, sideQuestId: string) {
    const today = new Date().toISOString().split('T')[0];

    // Update the existing status to reset it for today
    from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .update({
          completed: false,
          value_achieved: 0,
          last_updated: today
        })
        .eq('id', existingStatus.id)
        .select()
        .single()
    ).pipe(
      take(1)
    ).subscribe({
      next: (response) => {
        if (response.error) {
          console.error('Error resetting member status:', response.error);
          return;
        }

        this.memberStatus = response.data as GroupSideQuestMemberStatus;

        // Reload all member statuses to update counts
        this.loadMemberStatuses(sideQuestId);
      },
      error: (error) => {
        console.error('Error resetting member status:', error);
      }
    });
  }

  createMemberStatus(sideQuestId: string, userId: string) {
    const today = new Date();
    const dateString = today.toISOString().split('T')[0];

    const newStatus = {
      group_quest_id: sideQuestId,
      member_id: userId,
      completed: false,
      value_achieved: 0,
      last_updated: dateString
    };

    from(
      this.supabaseService.getClient()
        .from('group_sidequest_member_status')
        .insert(newStatus)
        .select()
        .single()
    ).pipe(
      take(1)
    ).subscribe({
      next: (response) => {
        if (response.error) {
          console.error('Error creating member status:', response.error);
          return;
        }

        this.memberStatus = response.data as GroupSideQuestMemberStatus;

        // Reload all member statuses
        this.loadMemberStatuses(sideQuestId);
      },
      error: (error) => {
        console.error('Error creating member status:', error);
      }
    });
  }

  toggleSideQuest() {
    if (!this.groupId || !this.userId || !this.dailyQuest || !this.memberStatus || this.isBeforeJoinDate || this.togglingSideQuest) {
      return;
    }

    this.togglingSideQuest = true;

    // Toggle the completion status locally first for immediate feedback
    const newCompletionStatus = !this.memberStatus.completed;
    const newValueAchieved = newCompletionStatus ? this.dailyQuest.current_quest.goal_value : 0;
    const today = new Date().toISOString().split('T')[0]; // Format as YYYY-MM-DD



    // Update the local member status immediately
    if (this.memberStatus) {
      this.memberStatus.completed = newCompletionStatus;
      this.memberStatus.value_achieved = newValueAchieved;
      this.memberStatus.last_updated = today; // Update last_updated to today
    }

    // Also update the completed members count for immediate feedback
    if (this.dailyQuest) {
      // Only update the count if the user is eligible (joined before today)
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for comparison

      // Check if the current user is eligible
      from(
        this.supabaseService.getClient()
          .from('group_members')
          .select('joined_date')
          .eq('group_id', this.groupId)
          .eq('user_id', this.userId)
          .single()
      ).subscribe(response => {
        if (response.error) {
          console.error('Error checking eligibility:', response.error);
          return;
        }

        // Check if the current user is eligible (joined before today)
        const joinedDate = new Date(response.data.joined_date);
        const isEligible = joinedDate < today; // Before today, not today

        // Update the completed members count based on eligibility and completion status
        if (isEligible) {
          // For immediate feedback, update the count directly
          if (newCompletionStatus) {
            // If completing, increment the count
            this.dailyQuest!.completed_members_count = (this.dailyQuest!.completed_members_count || 0) + 1;
          } else {
            // If uncompleting, decrement the count
            if (this.dailyQuest!.completed_members_count && this.dailyQuest!.completed_members_count > 0) {
              this.dailyQuest!.completed_members_count--;
            }
          }
        } else {
        }

        // We don't need to reload member statuses here anymore
        // We've already updated the counts locally for immediate feedback
      });
    }

    // Send the update to the server in the background
    this.groupSideQuestService.toggleMemberCompletion(this.memberStatus.id, this.groupId).pipe(
      take(1)
    ).subscribe({
      next: (updatedStatus) => {

        // Log the difference between local and server status


        // Update with the server response
        this.memberStatus = updatedStatus;

        // Update the streak directly in the UI based on completion status
        if (this.dailyQuest) {
          // Check if all eligible members have completed the quest
          const completedCount = this.dailyQuest.completed_members_count || 0;
          const eligibleCount = this.dailyQuest.eligible_members_count || 0;
          const allCompleted = completedCount === eligibleCount && eligibleCount > 0;

          // Check if the quest was previously completed
          const wasCompleted = this.dailyQuest.completed;


          // Update the streak and completed status based on the same logic as in the backend
          if (allCompleted && !wasCompleted) {
            // All members have completed the quest, increase streak
            this.dailyQuest.streak += 1;
            this.dailyQuest.completed = true;
          } else if (!allCompleted && wasCompleted) {
            // Quest was previously completed but now it's not, decrease streak
            this.dailyQuest.streak = Math.max(0, this.dailyQuest.streak - 1);
            this.dailyQuest.completed = false;
          }
        }

        this.togglingSideQuest = false;
      },
      error: (error) => {
        console.error('Error toggling side quest:', error);

        // Store the previous last_updated value to revert to
        const previousLastUpdated = this.memberStatus?.last_updated || '';

        // Revert the local changes on error
        if (this.memberStatus) {
          this.memberStatus.completed = !newCompletionStatus;
          this.memberStatus.value_achieved = !newCompletionStatus ? this.dailyQuest!.current_quest.goal_value : 0;
          this.memberStatus.last_updated = previousLastUpdated; // Revert last_updated
        }

        // Revert the completed members count locally
        if (this.dailyQuest) {
          // Only update the count if the user is eligible
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          // Check if the current user is eligible
          from(
            this.supabaseService.getClient()
              .from('group_members')
              .select('joined_date')
              .eq('group_id', this.groupId)
              .eq('user_id', this.userId)
              .single()
          ).subscribe(response => {
            if (response.error) return;

            const joinedDate = new Date(response.data.joined_date);
            const isEligible = joinedDate < today;

            // Get today's date in YYYY-MM-DD format
            const todayStr = new Date().toISOString().split('T')[0];
            const wasUpdatedToday = previousLastUpdated === todayStr;

            // Revert the count based on eligibility
            if (isEligible && this.dailyQuest) {
              if (!newCompletionStatus && wasUpdatedToday) {
                // If we were uncompleting, revert by adding 1 back
                this.dailyQuest.completed_members_count = (this.dailyQuest.completed_members_count || 0) + 1;
              } else if (newCompletionStatus && !wasUpdatedToday) {
                // If we were completing, revert by subtracting 1
                if (this.dailyQuest.completed_members_count && this.dailyQuest.completed_members_count > 0) {
                  this.dailyQuest.completed_members_count--;
                }
              }
            }
          });
        }

        this.togglingSideQuest = false;
        this.showErrorToast('Error updating side quest. Please try again.');
      }
    });
  }

  async showErrorToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 3000,
      position: 'bottom',
      color: 'danger'
    });
    await toast.present();
  }

  getProgressPercentage(): number {
    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count || this.dailyQuest.eligible_members_count === 0) {
      return 0;
    }

    return (this.dailyQuest.completed_members_count || 0) / this.dailyQuest.eligible_members_count * 100;
  }

  getMemberStatusText(): string {
    if (!this.dailyQuest || !this.dailyQuest.eligible_members_count) {
      return 'No members';
    }

    return `${this.dailyQuest.completed_members_count || 0}/${this.dailyQuest.eligible_members_count} completed`;
  }
}
