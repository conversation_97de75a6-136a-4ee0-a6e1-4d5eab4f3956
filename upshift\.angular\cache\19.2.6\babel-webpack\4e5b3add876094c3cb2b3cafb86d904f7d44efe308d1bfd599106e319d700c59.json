{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _JournalEntryDetailPage;\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/goal.service\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"richEditor\"];\nfunction JournalEntryDetailPage_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startEditing());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JournalEntryDetailPage_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteEntry());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83C\\uDFAF \", ctx_r1.entry.milestone_percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 2, ctx_r1.entry.created_at, \"medium\"));\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.entry.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"p\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(2, \"Normal text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h1\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(4, \"Heading 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h2\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(6, \"Heading 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h3\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(8, \"Heading 3\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65)(2, \"span\", 66);\n    i0.ɵɵtext(3, \"Text Color\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 67)(5, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"default\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ff453a\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ff9500\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ffcc02\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#30d158\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#007aff\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#af52de\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 65)(13, \"span\", 66);\n    i0.ɵɵtext(14, \"Background\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 67)(16, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"default\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ff453a20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ff950020\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ffcc0220\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#30d15820\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#007aff20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#af52de20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"bold\"));\n    });\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"italic\"));\n    });\n    i0.ɵɵelementStart(7, \"em\");\n    i0.ɵɵtext(8, \"I\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"underline\"));\n    });\n    i0.ɵɵelementStart(10, \"u\");\n    i0.ɵɵtext(11, \"U\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"strikeThrough\"));\n    });\n    i0.ɵɵelementStart(13, \"s\");\n    i0.ɵɵtext(14, \"S\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 24)(16, \"div\", 29)(17, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(\"headings\"));\n    });\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"H\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"ion-icon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, JournalEntryDetailPage_div_10_div_3_div_21_Template, 9, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 24)(23, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertList(false));\n    });\n    i0.ɵɵelement(24, \"ion-icon\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertList(true));\n    });\n    i0.ɵɵelement(26, \"ion-icon\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertCheckList());\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 24)(30, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyLeft\"));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyCenter\"));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyRight\"));\n    });\n    i0.ɵɵelement(35, \"ion-icon\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 24)(37, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertQuote());\n    });\n    i0.ɵɵelement(38, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertCode());\n    });\n    i0.ɵɵelement(40, \"ion-icon\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertDivider());\n    });\n    i0.ɵɵelement(42, \"ion-icon\", 50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 24)(44, \"div\", 29)(45, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(\"colors\"));\n    });\n    i0.ɵɵelement(46, \"ion-icon\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, JournalEntryDetailPage_div_10_div_3_div_47_Template, 23, 0, \"div\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 24)(49, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"undo\"));\n    });\n    i0.ɵɵelement(50, \"ion-icon\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_51_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"redo\"));\n    });\n    i0.ɵɵelement(52, \"ion-icon\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 58, 0);\n    i0.ɵɵlistener(\"input\", function JournalEntryDetailPage_div_10_div_3_Template_div_input_53_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentInput($event));\n    })(\"blur\", function JournalEntryDetailPage_div_10_div_3_Template_div_blur_53_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 59)(56, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_56_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cancelEditing());\n    });\n    i0.ɵɵtext(57, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveEntry());\n    });\n    i0.ɵɵtext(59, \"Save\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeDropdown === \"headings\");\n    i0.ɵɵadvance(26);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeDropdown === \"colors\");\n  }\n}\nfunction JournalEntryDetailPage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, JournalEntryDetailPage_div_10_div_1_Template, 6, 5, \"div\", 15)(2, JournalEntryDetailPage_div_10_div_2_Template, 1, 1, \"div\", 16)(3, JournalEntryDetailPage_div_10_div_3_Template, 60, 2, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing && ctx_r1.entry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEditing);\n  }\n}\nfunction JournalEntryDetailPage_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵelement(1, \"ion-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading journal entry...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class JournalEntryDetailPage {\n  constructor(route, router, goalService, toastController) {\n    this.route = route;\n    this.router = router;\n    this.goalService = goalService;\n    this.toastController = toastController;\n    this.goalId = '';\n    this.entryId = '';\n    this.entry = null;\n    this.isEditing = false;\n    this.editContent = '';\n    this.isLoading = true;\n    this.activeDropdown = null;\n  }\n  ngOnInit() {\n    this.goalId = this.route.snapshot.paramMap.get('goalId') || '';\n    this.entryId = this.route.snapshot.paramMap.get('entryId') || '';\n    if (this.goalId && this.entryId) {\n      this.loadJournalEntry();\n    } else {\n      this.router.navigate(['/goals']);\n    }\n  }\n  ngAfterViewInit() {\n    // Initialize rich editor content after view is ready\n    if (this.isEditing && this.richEditor) {\n      this.richEditor.nativeElement.innerHTML = this.editContent;\n    }\n    // Add keyboard shortcuts\n    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    // Add click outside handler for dropdowns\n    document.addEventListener('click', this.handleClickOutside.bind(this));\n  }\n  ngOnDestroy() {\n    // Clean up event listeners\n    document.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    document.removeEventListener('click', this.handleClickOutside.bind(this));\n  }\n  loadJournalEntry() {\n    this.isLoading = true;\n    // Get all journal entries for the goal and find the specific one\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.entry = entries.find(e => e.id === this.entryId) || null;\n      this.isLoading = false;\n      if (!this.entry) {\n        this.showToast('Journal entry not found');\n        this.router.navigate(['/goals', this.goalId]);\n      }\n    });\n  }\n  startEditing() {\n    if (this.entry) {\n      this.editContent = this.entry.content;\n      this.isEditing = true;\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.richEditor) {\n          this.richEditor.nativeElement.innerHTML = this.editContent;\n        }\n      }, 0);\n    }\n  }\n  cancelEditing() {\n    this.isEditing = false;\n    this.editContent = '';\n  }\n  saveEntry() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.entry || !_this.editContent.trim()) return;\n      try {\n        yield _this.goalService.updateJournalEntry(_this.entry.id, {\n          content: _this.editContent.trim()\n        });\n        _this.entry.content = _this.editContent.trim();\n        _this.isEditing = false;\n        _this.editContent = '';\n        _this.showToast('Journal entry updated successfully');\n      } catch (error) {\n        console.error('Error updating journal entry:', error);\n        _this.showToast('Error updating journal entry');\n      }\n    })();\n  }\n  deleteEntry() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.entry) return;\n      const confirmed = confirm('Are you sure you want to delete this journal entry? This action cannot be undone.');\n      if (!confirmed) return;\n      try {\n        yield _this2.goalService.deleteJournalEntry(_this2.entry.id);\n        _this2.showToast('Journal entry deleted successfully');\n        _this2.router.navigate(['/goals', _this2.goalId]);\n      } catch (error) {\n        console.error('Error deleting journal entry:', error);\n        _this2.showToast('Error deleting journal entry');\n      }\n    })();\n  }\n  goBack() {\n    this.router.navigate(['/goals', this.goalId]);\n  }\n  showToast(message) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this3.toastController.create({\n        message,\n        duration: 2000,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  // Rich text formatting methods\n  formatText(command, value) {\n    document.execCommand(command, false, value);\n  }\n  insertList(ordered = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n  onContentInput(event) {\n    this.editContent = event.target.innerHTML;\n  }\n  onContentBlur(event) {\n    this.editContent = event.target.innerHTML;\n  }\n  // Dropdown management\n  toggleDropdown(dropdown) {\n    this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;\n  }\n  closeDropdown() {\n    this.activeDropdown = null;\n  }\n  // Advanced formatting methods\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n      range.deleteContents();\n      range.insertNode(checklistItem);\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n    }\n  }\n  insertQuote() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const quote = document.createElement('blockquote');\n      quote.className = 'notion-quote';\n      quote.innerHTML = 'Quote text here...';\n      range.deleteContents();\n      range.insertNode(quote);\n      // Select the quote content\n      const newRange = document.createRange();\n      newRange.selectNodeContents(quote);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n    }\n  }\n  insertCode() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      if (selectedText) {\n        // Inline code\n        const code = document.createElement('code');\n        code.className = 'notion-code';\n        code.textContent = selectedText;\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(code);\n      } else {\n        // Code block\n        const range = selection.getRangeAt(0);\n        const codeBlock = document.createElement('pre');\n        codeBlock.className = 'notion-code-block';\n        codeBlock.innerHTML = '<code contenteditable=\"true\">// Your code here</code>';\n        range.deleteContents();\n        range.insertNode(codeBlock);\n        // Select the code content\n        const codeElement = codeBlock.querySelector('code');\n        if (codeElement) {\n          const newRange = document.createRange();\n          newRange.selectNodeContents(codeElement);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n        }\n      }\n    }\n  }\n  insertDivider() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const divider = document.createElement('hr');\n      divider.className = 'notion-divider';\n      range.deleteContents();\n      range.insertNode(divider);\n      // Move cursor after divider\n      range.setStartAfter(divider);\n      range.collapse(true);\n      selection.removeAllRanges();\n      selection.addRange(range);\n    }\n  }\n  setTextColor(color) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n      this.formatText('foreColor', '#ffffff');\n    } else {\n      this.formatText('foreColor', color);\n    }\n  }\n  setBackgroundColor(color) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n    } else {\n      this.formatText('backColor', color);\n    }\n  }\n}\n_JournalEntryDetailPage = JournalEntryDetailPage;\n_JournalEntryDetailPage.ɵfac = function JournalEntryDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _JournalEntryDetailPage)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.GoalService), i0.ɵɵdirectiveInject(i3.ToastController));\n};\n_JournalEntryDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _JournalEntryDetailPage,\n  selectors: [[\"app-journal-entry-detail\"]],\n  viewQuery: function JournalEntryDetailPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.richEditor = _t.first);\n    }\n  },\n  decls: 13,\n  vars: 4,\n  consts: [[\"richEditor\", \"\"], [1, \"container\"], [1, \"header-content\"], [1, \"back-btn\", 3, \"click\"], [\"name\", \"arrow-back\"], [1, \"header-actions\"], [\"class\", \"edit-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"delete-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"content\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"edit-btn\", 3, \"click\"], [\"name\", \"create-outline\"], [1, \"delete-btn\", 3, \"click\"], [\"name\", \"trash-outline\"], [1, \"content\"], [\"class\", \"milestone-info\", 4, \"ngIf\"], [\"class\", \"journal-content\", 3, \"innerHTML\", 4, \"ngIf\"], [\"class\", \"edit-container\", 4, \"ngIf\"], [1, \"milestone-info\"], [1, \"milestone-badge\"], [1, \"entry-date\"], [1, \"journal-content\", 3, \"innerHTML\"], [1, \"edit-container\"], [1, \"toolbar\"], [1, \"toolbar-group\"], [\"type\", \"button\", \"title\", \"Bold (Ctrl+B)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Italic (Ctrl+I)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Underline (Ctrl+U)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Strikethrough\", 1, \"toolbar-btn\", 3, \"click\"], [1, \"dropdown-container\"], [\"type\", \"button\", \"title\", \"Headings\", 1, \"toolbar-btn\", \"dropdown-btn\", 3, \"click\"], [\"name\", \"chevron-down-outline\"], [\"class\", \"dropdown-menu\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Bullet List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"list-outline\"], [\"type\", \"button\", \"title\", \"Numbered List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"list-circle-outline\"], [\"type\", \"button\", \"title\", \"To-do List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"checkbox-outline\"], [\"type\", \"button\", \"title\", \"Align Left\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"text-left-outline\"], [\"type\", \"button\", \"title\", \"Align Center\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"text-center-outline\"], [\"type\", \"button\", \"title\", \"Align Right\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"text-right-outline\"], [\"type\", \"button\", \"title\", \"Quote\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"chatbox-outline\"], [\"type\", \"button\", \"title\", \"Code\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"code-outline\"], [\"type\", \"button\", \"title\", \"Divider\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"remove-outline\"], [\"type\", \"button\", \"title\", \"Text Color\", 1, \"toolbar-btn\", \"dropdown-btn\", 3, \"click\"], [\"name\", \"color-palette-outline\"], [\"class\", \"dropdown-menu color-menu\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Undo (Ctrl+Z)\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"arrow-undo-outline\"], [\"type\", \"button\", \"title\", \"Redo (Ctrl+Y)\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [\"contenteditable\", \"true\", \"placeholder\", \"Write your journal entry here...\", 1, \"rich-editor\", 3, \"input\", \"blur\"], [1, \"edit-actions\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"save-btn\", 3, \"click\"], [1, \"dropdown-menu\"], [\"type\", \"button\", 3, \"click\"], [1, \"dropdown-menu\", \"color-menu\"], [1, \"color-section\"], [1, \"color-label\"], [1, \"color-grid\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffffff\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff453a\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff9500\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffcc02\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#30d158\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#007aff\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#af52de\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"transparent\", \"border\", \"1px solid #666\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff453a20\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff950020\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffcc0220\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#30d15820\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#007aff20\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#af52de20\", 3, \"click\"], [1, \"loading-container\"]],\n  template: function JournalEntryDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\")(2, \"div\", 2)(3, \"button\", 3);\n      i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_Template_button_click_3_listener() {\n        return ctx.goBack();\n      });\n      i0.ɵɵelement(4, \"ion-icon\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"h1\");\n      i0.ɵɵtext(6, \"Journal Entry\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵtemplate(8, JournalEntryDetailPage_button_8_Template, 2, 0, \"button\", 6)(9, JournalEntryDetailPage_button_9_Template, 2, 0, \"button\", 7);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(10, JournalEntryDetailPage_div_10_Template, 4, 3, \"div\", 8)(11, JournalEntryDetailPage_div_11_Template, 4, 0, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(12, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isEditing && ctx.entry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isEditing && ctx.entry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n    }\n  },\n  dependencies: [IonicModule, i3.IonIcon, i3.IonSpinner, CommonModule, i4.NgIf, i4.DatePipe, FormsModule, NavigationComponent],\n  styles: [\"[_nghost-%COMP%] {\\n  --background: #1c1c1e;\\n  --text: #ffffff;\\n  --text-muted: #8e8e93;\\n  --accent: #007aff;\\n  --card: #2c2c2e;\\n  --border: #3a3a3c;\\n  --success: #30d158;\\n  --danger: #ff453a;\\n  --radius: 12px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  min-height: 100vh;\\n  background: var(--background);\\n  color: var(--text);\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 16px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: none;\\n  border-radius: 8px;\\n  padding: 8px;\\n  color: var(--accent);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n  flex: 1;\\n  text-align: center;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: none;\\n  border-radius: 8px;\\n  padding: 8px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  color: var(--danger);\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover, .delete-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border-radius: var(--radius);\\n  padding: 24px;\\n  margin-bottom: 100px;\\n}\\n\\n.milestone-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid var(--border);\\n}\\n\\n.milestone-badge[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.entry-date[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 14px;\\n}\\n\\n.journal-content[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  font-size: 16px;\\n}\\n.journal-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 24px 0 12px 0;\\n  font-weight: 600;\\n}\\n.journal-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n  padding-left: 24px;\\n}\\n.journal-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 6px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.journal-content[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.journal-content[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.journal-content[_ngcontent-%COMP%]   s[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  opacity: 0.7;\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin: 8px 0;\\n  padding: 4px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   .checklist-checkbox[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n  accent-color: var(--accent);\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent);\\n  padding: 12px 16px;\\n  margin: 16px 0;\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 0 8px 8px 0;\\n  font-style: italic;\\n  color: var(--text-muted);\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 4px;\\n  padding: 2px 6px;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 13px;\\n  color: #ff6b6b;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n  overflow-x: auto;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 14px;\\n  color: #a8e6cf;\\n  white-space: pre;\\n  display: block;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-divider[_ngcontent-%COMP%] {\\n  border: none;\\n  height: 1px;\\n  background: var(--border);\\n  margin: 24px 0;\\n  opacity: 0.6;\\n}\\n\\n.edit-container[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--background);\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n  border: 1px solid var(--border);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  border-right: 1px solid var(--border);\\n  padding-right: 12px;\\n  align-items: center;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n  padding-right: 0;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 8px 12px;\\n  color: var(--text);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 36px;\\n  height: 36px;\\n  font-size: 14px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--card);\\n  border-color: var(--accent);\\n  transform: translateY(-1px);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:active {\\n  background: var(--border);\\n  transform: translateY(0);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn.dropdown-btn[_ngcontent-%COMP%] {\\n  gap: 4px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn.dropdown-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 8px;\\n  min-width: 160px;\\n  z-index: 1000;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n  margin-top: 4px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: transparent;\\n  border: none;\\n  padding: 8px 12px;\\n  color: var(--text);\\n  text-align: left;\\n  cursor: pointer;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: background-color 0.2s ease;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: var(--background);\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-menu[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  padding: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: var(--text-muted);\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 6px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 4px;\\n  border: 1px solid var(--border);\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.rich-editor[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  padding: 16px;\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  background: var(--background);\\n  color: var(--text);\\n  font-size: 16px;\\n  line-height: 1.6;\\n  outline: none;\\n  margin-bottom: 16px;\\n}\\n.rich-editor[_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent);\\n}\\n.rich-editor[contenteditable][_ngcontent-%COMP%]:empty::before {\\n  content: attr(placeholder);\\n  color: var(--text-muted);\\n  font-style: italic;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 16px 0 8px 0;\\n  font-weight: 600;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  padding-left: 24px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin: 8px 0;\\n  padding: 4px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   .checklist-checkbox[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n  accent-color: var(--accent);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  outline: none;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:focus {\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 4px;\\n  padding: 2px 4px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent);\\n  padding: 12px 16px;\\n  margin: 16px 0;\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 0 8px 8px 0;\\n  font-style: italic;\\n  color: var(--text-muted);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background: rgba(0, 122, 255, 0.15);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 4px;\\n  padding: 2px 6px;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 13px;\\n  color: #ff6b6b;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n  overflow-x: auto;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 14px;\\n  color: #a8e6cf;\\n  outline: none;\\n  white-space: pre;\\n  display: block;\\n  min-height: 20px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-divider[_ngcontent-%COMP%] {\\n  border: none;\\n  height: 1px;\\n  background: var(--border);\\n  margin: 24px 0;\\n  opacity: 0.6;\\n}\\n\\n.edit-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  color: var(--text);\\n}\\n.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n}\\n.save-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  color: var(--text-muted);\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n}\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .toolbar[_ngcontent-%COMP%] {\\n    gap: 8px !important;\\n  }\\n  .toolbar[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%] {\\n    gap: 2px !important;\\n    padding-right: 8px !important;\\n  }\\n  .toolbar[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%] {\\n    min-width: 32px !important;\\n    height: 32px !important;\\n    padding: 6px 8px !important;\\n  }\\n  .edit-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .edit-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .edit-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["IonicModule", "CommonModule", "FormsModule", "NavigationComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "JournalEntryDetailPage_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "startEditing", "ɵɵelement", "ɵɵelementEnd", "JournalEntryDetailPage_button_9_Template_button_click_0_listener", "_r3", "deleteEntry", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "entry", "milestone_percentage", "ɵɵtextInterpolate", "ɵɵpipeBind2", "created_at", "ɵɵproperty", "content", "ɵɵsanitizeHtml", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_1_listener", "_r5", "formatText", "closeDropdown", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_3_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_5_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_7_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_5_listener", "_r6", "setTextColor", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_6_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_7_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_8_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_9_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_10_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_11_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_16_listener", "setBackgroundColor", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_17_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_18_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_19_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_20_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_21_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template_button_click_22_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_3_listener", "_r4", "JournalEntryDetailPage_div_10_div_3_Template_button_click_6_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_9_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_12_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_17_listener", "toggleDropdown", "ɵɵtemplate", "JournalEntryDetailPage_div_10_div_3_div_21_Template", "JournalEntryDetailPage_div_10_div_3_Template_button_click_23_listener", "insertList", "JournalEntryDetailPage_div_10_div_3_Template_button_click_25_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_27_listener", "insertCheckList", "JournalEntryDetailPage_div_10_div_3_Template_button_click_30_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_32_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_34_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_37_listener", "insertQuote", "JournalEntryDetailPage_div_10_div_3_Template_button_click_39_listener", "insertCode", "JournalEntryDetailPage_div_10_div_3_Template_button_click_41_listener", "insertDivider", "JournalEntryDetailPage_div_10_div_3_Template_button_click_45_listener", "JournalEntryDetailPage_div_10_div_3_div_47_Template", "JournalEntryDetailPage_div_10_div_3_Template_button_click_49_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_51_listener", "JournalEntryDetailPage_div_10_div_3_Template_div_input_53_listener", "$event", "onContentInput", "JournalEntryDetailPage_div_10_div_3_Template_div_blur_53_listener", "onContentBlur", "JournalEntryDetailPage_div_10_div_3_Template_button_click_56_listener", "cancelEditing", "JournalEntryDetailPage_div_10_div_3_Template_button_click_58_listener", "saveEntry", "activeDropdown", "JournalEntryDetailPage_div_10_div_1_Template", "JournalEntryDetailPage_div_10_div_2_Template", "JournalEntryDetailPage_div_10_div_3_Template", "isEditing", "JournalEntryDetailPage", "constructor", "route", "router", "goalService", "toastController", "goalId", "entryId", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadJournalEntry", "navigate", "ngAfterViewInit", "rich<PERSON><PERSON><PERSON>", "nativeElement", "innerHTML", "document", "addEventListener", "handleKeyboardShortcuts", "bind", "handleClickOutside", "ngOnDestroy", "removeEventListener", "getJournalEntries", "subscribe", "entries", "find", "e", "id", "showToast", "setTimeout", "_this", "_asyncToGenerator", "trim", "updateJournalEntry", "error", "console", "_this2", "confirmed", "confirm", "deleteJournalEntry", "goBack", "message", "_this3", "toast", "create", "duration", "position", "present", "command", "value", "execCommand", "ordered", "event", "target", "dropdown", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "checklistItem", "createElement", "className", "deleteContents", "insertNode", "textSpan", "querySelector", "newRange", "createRange", "selectNodeContents", "removeAllRanges", "addRange", "quote", "selectedText", "toString", "code", "textContent", "codeBlock", "codeElement", "divider", "setStartAfter", "collapse", "color", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "GoalService", "i3", "ToastController", "selectors", "viewQuery", "JournalEntryDetailPage_Query", "rf", "ctx", "JournalEntryDetailPage_Template_button_click_3_listener", "JournalEntryDetailPage_button_8_Template", "JournalEntryDetailPage_button_9_Template", "JournalEntryDetailPage_div_10_Template", "JournalEntryDetailPage_div_11_Template", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "i4", "NgIf", "DatePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\journal-entry-detail\\journal-entry-detail.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\journal-entry-detail\\journal-entry-detail.page.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { IonicModule, ToastController } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { GoalJournalEntry } from '../../../models/goal.model';\nimport { GoalService } from '../../../services/goal.service';\n\n@Component({\n  selector: 'app-journal-entry-detail',\n  templateUrl: './journal-entry-detail.page.html',\n  styleUrls: ['./journal-entry-detail.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent]\n})\nexport class JournalEntryDetailPage implements OnInit, AfterViewInit {\n  @ViewChild('richEditor', { static: false }) richEditor!: ElementRef<HTMLDivElement>;\n\n  goalId: string = '';\n  entryId: string = '';\n  entry: GoalJournalEntry | null = null;\n  isEditing: boolean = false;\n  editContent: string = '';\n  isLoading: boolean = true;\n  activeDropdown: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private goalService: GoalService,\n    private toastController: ToastController\n  ) {}\n\n  ngOnInit() {\n    this.goalId = this.route.snapshot.paramMap.get('goalId') || '';\n    this.entryId = this.route.snapshot.paramMap.get('entryId') || '';\n\n    if (this.goalId && this.entryId) {\n      this.loadJournalEntry();\n    } else {\n      this.router.navigate(['/goals']);\n    }\n  }\n\n  ngAfterViewInit() {\n    // Initialize rich editor content after view is ready\n    if (this.isEditing && this.richEditor) {\n      this.richEditor.nativeElement.innerHTML = this.editContent;\n    }\n\n    // Add keyboard shortcuts\n    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n\n    // Add click outside handler for dropdowns\n    document.addEventListener('click', this.handleClickOutside.bind(this));\n  }\n\n  ngOnDestroy() {\n    // Clean up event listeners\n    document.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    document.removeEventListener('click', this.handleClickOutside.bind(this));\n  }\n\n  loadJournalEntry() {\n    this.isLoading = true;\n\n    // Get all journal entries for the goal and find the specific one\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.entry = entries.find(e => e.id === this.entryId) || null;\n      this.isLoading = false;\n\n      if (!this.entry) {\n        this.showToast('Journal entry not found');\n        this.router.navigate(['/goals', this.goalId]);\n      }\n    });\n  }\n\n  startEditing() {\n    if (this.entry) {\n      this.editContent = this.entry.content;\n      this.isEditing = true;\n\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.richEditor) {\n          this.richEditor.nativeElement.innerHTML = this.editContent;\n        }\n      }, 0);\n    }\n  }\n\n  cancelEditing() {\n    this.isEditing = false;\n    this.editContent = '';\n  }\n\n  async saveEntry() {\n    if (!this.entry || !this.editContent.trim()) return;\n\n    try {\n      await this.goalService.updateJournalEntry(this.entry.id!, { content: this.editContent.trim() });\n      this.entry.content = this.editContent.trim();\n      this.isEditing = false;\n      this.editContent = '';\n      this.showToast('Journal entry updated successfully');\n    } catch (error) {\n      console.error('Error updating journal entry:', error);\n      this.showToast('Error updating journal entry');\n    }\n  }\n\n  async deleteEntry() {\n    if (!this.entry) return;\n\n    const confirmed = confirm('Are you sure you want to delete this journal entry? This action cannot be undone.');\n    if (!confirmed) return;\n\n    try {\n      await this.goalService.deleteJournalEntry(this.entry.id!);\n      this.showToast('Journal entry deleted successfully');\n      this.router.navigate(['/goals', this.goalId]);\n    } catch (error) {\n      console.error('Error deleting journal entry:', error);\n      this.showToast('Error deleting journal entry');\n    }\n  }\n\n  goBack() {\n    this.router.navigate(['/goals', this.goalId]);\n  }\n\n  private async showToast(message: string) {\n    const toast = await this.toastController.create({\n      message,\n      duration: 2000,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n\n  // Rich text formatting methods\n  formatText(command: string, value?: string) {\n    document.execCommand(command, false, value);\n  }\n\n  insertList(ordered: boolean = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n\n  onContentInput(event: any) {\n    this.editContent = event.target.innerHTML;\n  }\n\n  onContentBlur(event: any) {\n    this.editContent = event.target.innerHTML;\n  }\n\n  // Dropdown management\n  toggleDropdown(dropdown: string) {\n    this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;\n  }\n\n  closeDropdown() {\n    this.activeDropdown = null;\n  }\n\n  // Advanced formatting methods\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n\n      range.deleteContents();\n      range.insertNode(checklistItem);\n\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n    }\n  }\n\n  insertQuote() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const quote = document.createElement('blockquote');\n      quote.className = 'notion-quote';\n      quote.innerHTML = 'Quote text here...';\n\n      range.deleteContents();\n      range.insertNode(quote);\n\n      // Select the quote content\n      const newRange = document.createRange();\n      newRange.selectNodeContents(quote);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n    }\n  }\n\n  insertCode() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      if (selectedText) {\n        // Inline code\n        const code = document.createElement('code');\n        code.className = 'notion-code';\n        code.textContent = selectedText;\n\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(code);\n      } else {\n        // Code block\n        const range = selection.getRangeAt(0);\n        const codeBlock = document.createElement('pre');\n        codeBlock.className = 'notion-code-block';\n        codeBlock.innerHTML = '<code contenteditable=\"true\">// Your code here</code>';\n\n        range.deleteContents();\n        range.insertNode(codeBlock);\n\n        // Select the code content\n        const codeElement = codeBlock.querySelector('code');\n        if (codeElement) {\n          const newRange = document.createRange();\n          newRange.selectNodeContents(codeElement);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n        }\n      }\n    }\n  }\n\n  insertDivider() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const divider = document.createElement('hr');\n      divider.className = 'notion-divider';\n\n      range.deleteContents();\n      range.insertNode(divider);\n\n      // Move cursor after divider\n      range.setStartAfter(divider);\n      range.collapse(true);\n      selection.removeAllRanges();\n      selection.addRange(range);\n    }\n  }\n\n  setTextColor(color: string) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n      this.formatText('foreColor', '#ffffff');\n    } else {\n      this.formatText('foreColor', color);\n    }\n  }\n\n  setBackgroundColor(color: string) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n    } else {\n      this.formatText('backColor', color);\n    }\n  }\n}\n", "<div class=\"container\">\n    <header>\n        <div class=\"header-content\">\n            <button class=\"back-btn\" (click)=\"goBack()\">\n                <ion-icon name=\"arrow-back\"></ion-icon>\n            </button>\n            <h1>Journal Entry</h1>\n            <div class=\"header-actions\">\n                <button class=\"edit-btn\" *ngIf=\"!isEditing && entry\" (click)=\"startEditing()\">\n                    <ion-icon name=\"create-outline\"></ion-icon>\n                </button>\n                <button class=\"delete-btn\" *ngIf=\"!isEditing && entry\" (click)=\"deleteEntry()\">\n                    <ion-icon name=\"trash-outline\"></ion-icon>\n                </button>\n            </div>\n        </div>\n    </header>\n\n    <div class=\"content\" *ngIf=\"!isLoading\">\n        <div class=\"milestone-info\" *ngIf=\"entry\">\n            <div class=\"milestone-badge\">🎯 {{ entry.milestone_percentage }}%</div>\n            <div class=\"entry-date\">{{ entry.created_at | date:'medium' }}</div>\n        </div>\n\n        <!-- View Mode -->\n        <div class=\"journal-content\" *ngIf=\"!isEditing && entry\" [innerHTML]=\"entry.content\"></div>\n\n        <!-- Edit Mode -->\n        <div class=\"edit-container\" *ngIf=\"isEditing\">\n            <!-- Rich Text Toolbar -->\n            <div class=\"toolbar\">\n                <!-- Text Formatting -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('bold')\" title=\"Bold (Ctrl+B)\">\n                        <strong>B</strong>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('italic')\" title=\"Italic (Ctrl+I)\">\n                        <em>I</em>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('underline')\" title=\"Underline (Ctrl+U)\">\n                        <u>U</u>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('strikeThrough')\" title=\"Strikethrough\">\n                        <s>S</s>\n                    </button>\n                </div>\n\n                <!-- Headings -->\n                <div class=\"toolbar-group\">\n                    <div class=\"dropdown-container\">\n                        <button type=\"button\" class=\"toolbar-btn dropdown-btn\" (click)=\"toggleDropdown('headings')\" title=\"Headings\">\n                            <span>H</span>\n                            <ion-icon name=\"chevron-down-outline\"></ion-icon>\n                        </button>\n                        <div class=\"dropdown-menu\" *ngIf=\"activeDropdown === 'headings'\">\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'p'); closeDropdown()\">Normal text</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h1'); closeDropdown()\">Heading 1</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h2'); closeDropdown()\">Heading 2</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h3'); closeDropdown()\">Heading 3</button>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Lists -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(false)\" title=\"Bullet List\">\n                        <ion-icon name=\"list-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(true)\" title=\"Numbered List\">\n                        <ion-icon name=\"list-circle-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertCheckList()\" title=\"To-do List\">\n                        <ion-icon name=\"checkbox-outline\"></ion-icon>\n                    </button>\n                </div>\n\n                <!-- Alignment -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyLeft')\" title=\"Align Left\">\n                        <ion-icon name=\"text-left-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyCenter')\" title=\"Align Center\">\n                        <ion-icon name=\"text-center-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyRight')\" title=\"Align Right\">\n                        <ion-icon name=\"text-right-outline\"></ion-icon>\n                    </button>\n                </div>\n\n                <!-- Special Elements -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertQuote()\" title=\"Quote\">\n                        <ion-icon name=\"chatbox-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertCode()\" title=\"Code\">\n                        <ion-icon name=\"code-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertDivider()\" title=\"Divider\">\n                        <ion-icon name=\"remove-outline\"></ion-icon>\n                    </button>\n                </div>\n\n                <!-- Colors -->\n                <div class=\"toolbar-group\">\n                    <div class=\"dropdown-container\">\n                        <button type=\"button\" class=\"toolbar-btn dropdown-btn\" (click)=\"toggleDropdown('colors')\" title=\"Text Color\">\n                            <ion-icon name=\"color-palette-outline\"></ion-icon>\n                        </button>\n                        <div class=\"dropdown-menu color-menu\" *ngIf=\"activeDropdown === 'colors'\">\n                            <div class=\"color-section\">\n                                <span class=\"color-label\">Text Color</span>\n                                <div class=\"color-grid\">\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('default'); closeDropdown()\" style=\"background: #ffffff\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ff453a'); closeDropdown()\" style=\"background: #ff453a\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ff9500'); closeDropdown()\" style=\"background: #ff9500\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ffcc02'); closeDropdown()\" style=\"background: #ffcc02\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#30d158'); closeDropdown()\" style=\"background: #30d158\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#007aff'); closeDropdown()\" style=\"background: #007aff\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#af52de'); closeDropdown()\" style=\"background: #af52de\"></button>\n                                </div>\n                            </div>\n                            <div class=\"color-section\">\n                                <span class=\"color-label\">Background</span>\n                                <div class=\"color-grid\">\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('default'); closeDropdown()\" style=\"background: transparent; border: 1px solid #666\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ff453a20'); closeDropdown()\" style=\"background: #ff453a20\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ff950020'); closeDropdown()\" style=\"background: #ff950020\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ffcc0220'); closeDropdown()\" style=\"background: #ffcc0220\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#30d15820'); closeDropdown()\" style=\"background: #30d15820\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#007aff20'); closeDropdown()\" style=\"background: #007aff20\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#af52de20'); closeDropdown()\" style=\"background: #af52de20\"></button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- More Options -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('undo')\" title=\"Undo (Ctrl+Z)\">\n                        <ion-icon name=\"arrow-undo-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('redo')\" title=\"Redo (Ctrl+Y)\">\n                        <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n                    </button>\n                </div>\n            </div>\n\n            <!-- Rich Text Editor -->\n            <div\n                class=\"rich-editor\"\n                contenteditable=\"true\"\n                (input)=\"onContentInput($event)\"\n                (blur)=\"onContentBlur($event)\"\n                #richEditor\n                placeholder=\"Write your journal entry here...\">\n            </div>\n\n            <!-- Action Buttons -->\n            <div class=\"edit-actions\">\n                <button class=\"cancel-btn\" (click)=\"cancelEditing()\">Cancel</button>\n                <button class=\"save-btn\" (click)=\"saveEntry()\">Save</button>\n            </div>\n        </div>\n    </div>\n\n    <!-- Loading State -->\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\n        <ion-spinner></ion-spinner>\n        <p>Loading journal entry...</p>\n    </div>\n</div>\n\n<!-- Navigation -->\n<app-navigation></app-navigation>\n"], "mappings": ";;AAEA,SAASA,WAAW,QAAyB,gBAAgB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,qDAAqD;;;;;;;;;;ICGzEC,EAAA,CAAAC,cAAA,iBAA8E;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACzET,EAAA,CAAAU,SAAA,mBAA2C;IAC/CV,EAAA,CAAAW,YAAA,EAAS;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAA+E;IAAxBD,EAAA,CAAAE,UAAA,mBAAAU,iEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,WAAA,EAAa;IAAA,EAAC;IAC1Ed,EAAA,CAAAU,SAAA,mBAA0C;IAC9CV,EAAA,CAAAW,YAAA,EAAS;;;;;IAObX,EADJ,CAAAC,cAAA,cAA0C,cACT;IAAAD,EAAA,CAAAe,MAAA,GAAoC;IAAAf,EAAA,CAAAW,YAAA,EAAM;IACvEX,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAe,MAAA,GAAsC;;IAClEf,EADkE,CAAAW,YAAA,EAAM,EAClE;;;;IAF2BX,EAAA,CAAAgB,SAAA,GAAoC;IAApChB,EAAA,CAAAiB,kBAAA,kBAAAX,MAAA,CAAAY,KAAA,CAAAC,oBAAA,MAAoC;IACzCnB,EAAA,CAAAgB,SAAA,GAAsC;IAAtChB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAf,MAAA,CAAAY,KAAA,CAAAI,UAAA,YAAsC;;;;;IAIlEtB,EAAA,CAAAU,SAAA,cAA2F;;;;IAAlCV,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAY,KAAA,CAAAM,OAAA,EAAAxB,EAAA,CAAAyB,cAAA,CAA2B;;;;;;IA8BhEzB,EADJ,CAAAC,cAAA,cAAiE,iBACmB;IAA1DD,EAAA,CAAAE,UAAA,mBAAAwB,4EAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,GAAG,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,kBAAW;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACpGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA4B,4EAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA6B,4EAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA8B,4EAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAC9Ff,EAD8F,CAAAW,YAAA,EAAS,EACjG;;;;;;IAmDEX,EAFR,CAAAC,cAAA,cAA0E,cAC3C,eACG;IAAAD,EAAA,CAAAe,MAAA,iBAAU;IAAAf,EAAA,CAAAW,YAAA,EAAO;IAEvCX,EADJ,CAAAC,cAAA,cAAwB,iBACmG;IAA/ED,EAAA,CAAAE,UAAA,mBAAA+B,4EAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAkC,4EAAA;MAAApC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAmC,4EAAA;MAAArC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAoC,4EAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAqC,4EAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,kBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAsC,6EAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,kBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAuC,6EAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA6B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAAnC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAElG7B,EAF+H,CAAAW,YAAA,EAAS,EAC9H,EACJ;IAEFX,EADJ,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAe,MAAA,kBAAU;IAAAf,EAAA,CAAAW,YAAA,EAAO;IAEvCX,EADJ,CAAAC,cAAA,eAAwB,kBACqI;IAAjHD,EAAA,CAAAE,UAAA,mBAAAwC,6EAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,SAAS,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAyD7B,EAAA,CAAAW,YAAA,EAAS;IAClKX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA0C,6EAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA2C,6EAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA4C,6EAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA6C,6EAAA;MAAA/C,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA8C,6EAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA+C,6EAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAA8B,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAqC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA3C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAG9G7B,EAH6I,CAAAW,YAAA,EAAS,EACxI,EACJ,EACJ;;;;;;IApGVX,EALZ,CAAAC,cAAA,cAA8C,cAErB,cAEU,iBACsE;IAAnDD,EAAA,CAAAE,UAAA,mBAAAgD,qEAAA;MAAAlD,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAe,MAAA,QAAC;IACbf,EADa,CAAAW,YAAA,EAAS,EACb;IACTX,EAAA,CAAAC,cAAA,iBAAiG;IAAvDD,EAAA,CAAAE,UAAA,mBAAAkD,qEAAA;MAAApD,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,QAAQ,CAAC;IAAA,EAAC;IACpE5B,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,QAAC;IACTf,EADS,CAAAW,YAAA,EAAK,EACL;IACTX,EAAA,CAAAC,cAAA,iBAAuG;IAA7DD,EAAA,CAAAE,UAAA,mBAAAmD,qEAAA;MAAArD,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IACvE5B,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAe,MAAA,SAAC;IACRf,EADQ,CAAAW,YAAA,EAAI,EACH;IACTX,EAAA,CAAAC,cAAA,kBAAsG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAoD,sEAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAC3E5B,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAe,MAAA,SAAC;IAEZf,EAFY,CAAAW,YAAA,EAAI,EACH,EACP;IAKEX,EAFR,CAAAC,cAAA,eAA2B,eACS,kBACiF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAqD,sEAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACvFxD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAe,MAAA,SAAC;IAAAf,EAAA,CAAAW,YAAA,EAAO;IACdX,EAAA,CAAAU,SAAA,oBAAiD;IACrDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAyD,UAAA,KAAAC,mDAAA,kBAAiE;IAOzE1D,EADI,CAAAW,YAAA,EAAM,EACJ;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACmE;IAAhDD,EAAA,CAAAE,UAAA,mBAAAyD,sEAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsD,UAAA,CAAW,KAAK,CAAC;IAAA,EAAC;IACjE5D,EAAA,CAAAU,SAAA,oBAAyC;IAC7CV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA2F;IAAjDD,EAAA,CAAAE,UAAA,mBAAA2D,sEAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsD,UAAA,CAAW,IAAI,CAAC;IAAA,EAAC;IAChE5D,EAAA,CAAAU,SAAA,oBAAgD;IACpDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAyF;IAA/CD,EAAA,CAAAE,UAAA,mBAAA4D,sEAAA;MAAA9D,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyD,eAAA,EAAiB;IAAA,EAAC;IACjE/D,EAAA,CAAAU,SAAA,oBAA6C;IAErDV,EADI,CAAAW,YAAA,EAAS,EACP;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBAC0E;IAAvDD,EAAA,CAAAE,UAAA,mBAAA8D,sEAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,aAAa,CAAC;IAAA,EAAC;IACzE5B,EAAA,CAAAU,SAAA,oBAA8C;IAClDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAqG;IAA3DD,EAAA,CAAAE,UAAA,mBAAA+D,sEAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAC3E5B,EAAA,CAAAU,SAAA,oBAAgD;IACpDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAmG;IAAzDD,EAAA,CAAAE,UAAA,mBAAAgE,sEAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,cAAc,CAAC;IAAA,EAAC;IAC1E5B,EAAA,CAAAU,SAAA,oBAA+C;IAEvDV,EADI,CAAAW,YAAA,EAAS,EACP;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACyD;IAAtCD,EAAA,CAAAE,UAAA,mBAAAiE,sEAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8D,WAAA,EAAa;IAAA,EAAC;IAC7DpE,EAAA,CAAAU,SAAA,oBAA4C;IAChDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA8E;IAApCD,EAAA,CAAAE,UAAA,mBAAAmE,sEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgE,UAAA,EAAY;IAAA,EAAC;IAC5DtE,EAAA,CAAAU,SAAA,oBAAyC;IAC7CV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAoF;IAA1CD,EAAA,CAAAE,UAAA,mBAAAqE,sEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,aAAA,EAAe;IAAA,EAAC;IAC/DxE,EAAA,CAAAU,SAAA,oBAA2C;IAEnDV,EADI,CAAAW,YAAA,EAAS,EACP;IAKEX,EAFR,CAAAC,cAAA,eAA2B,eACS,kBACiF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAuE,sEAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkD,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACrFxD,EAAA,CAAAU,SAAA,oBAAkD;IACtDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAyD,UAAA,KAAAiB,mDAAA,mBAA0E;IA2BlF1E,EADI,CAAAW,YAAA,EAAM,EACJ;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACsE;IAAnDD,EAAA,CAAAE,UAAA,mBAAAyE,sEAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAU,SAAA,oBAA+C;IACnDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAE,UAAA,mBAAA0E,sEAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAU,SAAA,oBAA+C;IAG3DV,EAFQ,CAAAW,YAAA,EAAS,EACP,EACJ;IAGNX,EAAA,CAAAC,cAAA,kBAMmD;IAF/CD,EADA,CAAAE,UAAA,mBAAA2E,mEAAAC,MAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAyE,cAAA,CAAAD,MAAA,CAAsB;IAAA,EAAC,kBAAAE,kEAAAF,MAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACxBF,MAAA,CAAA2E,aAAA,CAAAH,MAAA,CAAqB;IAAA,EAAC;IAGlC9E,EAAA,CAAAW,YAAA,EAAM;IAIFX,EADJ,CAAAC,cAAA,eAA0B,kBAC+B;IAA1BD,EAAA,CAAAE,UAAA,mBAAAgF,sEAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6E,aAAA,EAAe;IAAA,EAAC;IAACnF,EAAA,CAAAe,MAAA,cAAM;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACpEX,EAAA,CAAAC,cAAA,kBAA+C;IAAtBD,EAAA,CAAAE,UAAA,mBAAAkF,sEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,SAAA,EAAW;IAAA,EAAC;IAACrF,EAAA,CAAAe,MAAA,YAAI;IAE3Df,EAF2D,CAAAW,YAAA,EAAS,EAC1D,EACJ;;;;IA7GsCX,EAAA,CAAAgB,SAAA,IAAmC;IAAnChB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgF,cAAA,gBAAmC;IAsDxBtF,EAAA,CAAAgB,SAAA,IAAiC;IAAjChB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAgF,cAAA,cAAiC;;;;;IA1F5FtF,EAAA,CAAAC,cAAA,cAAwC;IAUpCD,EATA,CAAAyD,UAAA,IAAA8B,4CAAA,kBAA0C,IAAAC,4CAAA,kBAM2C,IAAAC,4CAAA,mBAGvC;IAwIlDzF,EAAA,CAAAW,YAAA,EAAM;;;;IAjJ2BX,EAAA,CAAAgB,SAAA,EAAW;IAAXhB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAY,KAAA,CAAW;IAMVlB,EAAA,CAAAgB,SAAA,EAAyB;IAAzBhB,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAoF,SAAA,IAAApF,MAAA,CAAAY,KAAA,CAAyB;IAG1BlB,EAAA,CAAAgB,SAAA,EAAe;IAAfhB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAoF,SAAA,CAAe;;;;;IA2IhD1F,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAU,SAAA,kBAA2B;IAC3BV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,+BAAwB;IAC/Bf,EAD+B,CAAAW,YAAA,EAAI,EAC7B;;;AD1JV,OAAM,MAAOgF,sBAAsB;EAWjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,eAAgC;IAHhC,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAZzB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAhF,KAAK,GAA4B,IAAI;IACrC,KAAAwE,SAAS,GAAY,KAAK;IAC1B,KAAAS,WAAW,GAAW,EAAE;IACxB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAd,cAAc,GAAkB,IAAI;EAOjC;EAEHe,QAAQA,CAAA;IACN,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACJ,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACN,OAAO,GAAG,IAAI,CAACL,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE,IAAI,IAAI,CAACP,MAAM,IAAI,IAAI,CAACC,OAAO,EAAE;MAC/B,IAAI,CAACO,gBAAgB,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC;EACF;EAEAC,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACkB,UAAU,EAAE;MACrC,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACX,WAAW;IAC5D;IAEA;IACAY,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE7E;IACAH,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;EAEAE,WAAWA,CAAA;IACT;IACAL,QAAQ,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACJ,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChFH,QAAQ,CAACM,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3E;EAEAT,gBAAgBA,CAAA;IACd,IAAI,CAACL,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACL,WAAW,CAACuB,iBAAiB,CAAC,IAAI,CAACrB,MAAM,CAAC,CAACsB,SAAS,CAACC,OAAO,IAAG;MAClE,IAAI,CAACtG,KAAK,GAAGsG,OAAO,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK,IAAI,CAACzB,OAAO,CAAC,IAAI,IAAI;MAC7D,IAAI,CAACE,SAAS,GAAG,KAAK;MAEtB,IAAI,CAAC,IAAI,CAAClF,KAAK,EAAE;QACf,IAAI,CAAC0G,SAAS,CAAC,yBAAyB,CAAC;QACzC,IAAI,CAAC9B,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACT,MAAM,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEAxF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACS,KAAK,EAAE;MACd,IAAI,CAACiF,WAAW,GAAG,IAAI,CAACjF,KAAK,CAACM,OAAO;MACrC,IAAI,CAACkE,SAAS,GAAG,IAAI;MAErB;MACAmC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACjB,UAAU,EAAE;UACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACX,WAAW;QAC5D;MACF,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAhB,aAAaA,CAAA;IACX,IAAI,CAACO,SAAS,GAAG,KAAK;IACtB,IAAI,CAACS,WAAW,GAAG,EAAE;EACvB;EAEMd,SAASA,CAAA;IAAA,IAAAyC,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAI,CAACD,KAAI,CAAC5G,KAAK,IAAI,CAAC4G,KAAI,CAAC3B,WAAW,CAAC6B,IAAI,EAAE,EAAE;MAE7C,IAAI;QACF,MAAMF,KAAI,CAAC/B,WAAW,CAACkC,kBAAkB,CAACH,KAAI,CAAC5G,KAAK,CAACyG,EAAG,EAAE;UAAEnG,OAAO,EAAEsG,KAAI,CAAC3B,WAAW,CAAC6B,IAAI;QAAE,CAAE,CAAC;QAC/FF,KAAI,CAAC5G,KAAK,CAACM,OAAO,GAAGsG,KAAI,CAAC3B,WAAW,CAAC6B,IAAI,EAAE;QAC5CF,KAAI,CAACpC,SAAS,GAAG,KAAK;QACtBoC,KAAI,CAAC3B,WAAW,GAAG,EAAE;QACrB2B,KAAI,CAACF,SAAS,CAAC,oCAAoC,CAAC;MACtD,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDJ,KAAI,CAACF,SAAS,CAAC,8BAA8B,CAAC;MAChD;IAAC;EACH;EAEM9G,WAAWA,CAAA;IAAA,IAAAsH,MAAA;IAAA,OAAAL,iBAAA;MACf,IAAI,CAACK,MAAI,CAAClH,KAAK,EAAE;MAEjB,MAAMmH,SAAS,GAAGC,OAAO,CAAC,mFAAmF,CAAC;MAC9G,IAAI,CAACD,SAAS,EAAE;MAEhB,IAAI;QACF,MAAMD,MAAI,CAACrC,WAAW,CAACwC,kBAAkB,CAACH,MAAI,CAAClH,KAAK,CAACyG,EAAG,CAAC;QACzDS,MAAI,CAACR,SAAS,CAAC,oCAAoC,CAAC;QACpDQ,MAAI,CAACtC,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,EAAE0B,MAAI,CAACnC,MAAM,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDE,MAAI,CAACR,SAAS,CAAC,8BAA8B,CAAC;MAChD;IAAC;EACH;EAEAY,MAAMA,CAAA;IACJ,IAAI,CAAC1C,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACT,MAAM,CAAC,CAAC;EAC/C;EAEc2B,SAASA,CAACa,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MACrC,MAAMY,KAAK,SAASD,MAAI,CAAC1C,eAAe,CAAC4C,MAAM,CAAC;QAC9CH,OAAO;QACPI,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;EAEA;EACAnH,UAAUA,CAACoH,OAAe,EAAEC,KAAc;IACxClC,QAAQ,CAACmC,WAAW,CAACF,OAAO,EAAE,KAAK,EAAEC,KAAK,CAAC;EAC7C;EAEArF,UAAUA,CAACuF,OAAA,GAAmB,KAAK;IACjC,MAAMH,OAAO,GAAGG,OAAO,GAAG,mBAAmB,GAAG,qBAAqB;IACrEpC,QAAQ,CAACmC,WAAW,CAACF,OAAO,EAAE,KAAK,CAAC;EACtC;EAEAjE,cAAcA,CAACqE,KAAU;IACvB,IAAI,CAACjD,WAAW,GAAGiD,KAAK,CAACC,MAAM,CAACvC,SAAS;EAC3C;EAEA7B,aAAaA,CAACmE,KAAU;IACtB,IAAI,CAACjD,WAAW,GAAGiD,KAAK,CAACC,MAAM,CAACvC,SAAS;EAC3C;EAEA;EACAtD,cAAcA,CAAC8F,QAAgB;IAC7B,IAAI,CAAChE,cAAc,GAAG,IAAI,CAACA,cAAc,KAAKgE,QAAQ,GAAG,IAAI,GAAGA,QAAQ;EAC1E;EAEAzH,aAAaA,CAAA;IACX,IAAI,CAACyD,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAvB,eAAeA,CAAA;IACb,MAAMwF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAG9C,QAAQ,CAAC+C,aAAa,CAAC,KAAK,CAAC;MACnDD,aAAa,CAACE,SAAS,GAAG,gBAAgB;MAC1CF,aAAa,CAAC/C,SAAS,GAAG,iGAAiG;MAE3H6C,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACJ,aAAa,CAAC;MAE/B;MACA,MAAMK,QAAQ,GAAGL,aAAa,CAACM,aAAa,CAAC,MAAM,CAAC;MACpD,IAAID,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGrD,QAAQ,CAACsD,WAAW,EAAE;QACvCD,QAAQ,CAACE,kBAAkB,CAACJ,QAAQ,CAAC;QACrCX,SAAS,CAACgB,eAAe,EAAE;QAC3BhB,SAAS,CAACiB,QAAQ,CAACJ,QAAQ,CAAC;MAC9B;IACF;EACF;EAEAhG,WAAWA,CAAA;IACT,MAAMmF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMa,KAAK,GAAG1D,QAAQ,CAAC+C,aAAa,CAAC,YAAY,CAAC;MAClDW,KAAK,CAACV,SAAS,GAAG,cAAc;MAChCU,KAAK,CAAC3D,SAAS,GAAG,oBAAoB;MAEtC6C,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACQ,KAAK,CAAC;MAEvB;MACA,MAAML,QAAQ,GAAGrD,QAAQ,CAACsD,WAAW,EAAE;MACvCD,QAAQ,CAACE,kBAAkB,CAACG,KAAK,CAAC;MAClClB,SAAS,CAACgB,eAAe,EAAE;MAC3BhB,SAAS,CAACiB,QAAQ,CAACJ,QAAQ,CAAC;IAC9B;EACF;EAEA9F,UAAUA,CAAA;IACR,MAAMiF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMgB,YAAY,GAAGnB,SAAS,CAACoB,QAAQ,EAAE;MACzC,IAAID,YAAY,EAAE;QAChB;QACA,MAAME,IAAI,GAAG7D,QAAQ,CAAC+C,aAAa,CAAC,MAAM,CAAC;QAC3Cc,IAAI,CAACb,SAAS,GAAG,aAAa;QAC9Ba,IAAI,CAACC,WAAW,GAAGH,YAAY;QAE/B,MAAMf,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;QACrCD,KAAK,CAACK,cAAc,EAAE;QACtBL,KAAK,CAACM,UAAU,CAACW,IAAI,CAAC;MACxB,CAAC,MAAM;QACL;QACA,MAAMjB,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;QACrC,MAAMkB,SAAS,GAAG/D,QAAQ,CAAC+C,aAAa,CAAC,KAAK,CAAC;QAC/CgB,SAAS,CAACf,SAAS,GAAG,mBAAmB;QACzCe,SAAS,CAAChE,SAAS,GAAG,uDAAuD;QAE7E6C,KAAK,CAACK,cAAc,EAAE;QACtBL,KAAK,CAACM,UAAU,CAACa,SAAS,CAAC;QAE3B;QACA,MAAMC,WAAW,GAAGD,SAAS,CAACX,aAAa,CAAC,MAAM,CAAC;QACnD,IAAIY,WAAW,EAAE;UACf,MAAMX,QAAQ,GAAGrD,QAAQ,CAACsD,WAAW,EAAE;UACvCD,QAAQ,CAACE,kBAAkB,CAACS,WAAW,CAAC;UACxCxB,SAAS,CAACgB,eAAe,EAAE;UAC3BhB,SAAS,CAACiB,QAAQ,CAACJ,QAAQ,CAAC;QAC9B;MACF;IACF;EACF;EAEA5F,aAAaA,CAAA;IACX,MAAM+E,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMoB,OAAO,GAAGjE,QAAQ,CAAC+C,aAAa,CAAC,IAAI,CAAC;MAC5CkB,OAAO,CAACjB,SAAS,GAAG,gBAAgB;MAEpCJ,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACe,OAAO,CAAC;MAEzB;MACArB,KAAK,CAACsB,aAAa,CAACD,OAAO,CAAC;MAC5BrB,KAAK,CAACuB,QAAQ,CAAC,IAAI,CAAC;MACpB3B,SAAS,CAACgB,eAAe,EAAE;MAC3BhB,SAAS,CAACiB,QAAQ,CAACb,KAAK,CAAC;IAC3B;EACF;EAEAxH,YAAYA,CAACgJ,KAAa;IACxB,IAAIA,KAAK,KAAK,SAAS,EAAE;MACvB,IAAI,CAACvJ,UAAU,CAAC,cAAc,CAAC;MAC/B,IAAI,CAACA,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC;IACzC,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,CAAC,WAAW,EAAEuJ,KAAK,CAAC;IACrC;EACF;EAEAxI,kBAAkBA,CAACwI,KAAa;IAC9B,IAAIA,KAAK,KAAK,SAAS,EAAE;MACvB,IAAI,CAACvJ,UAAU,CAAC,cAAc,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,CAAC,WAAW,EAAEuJ,KAAK,CAAC;IACrC;EACF;;0BAvQWxF,sBAAsB;;mCAAtBA,uBAAsB,EAAA3F,EAAA,CAAAoL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtL,EAAA,CAAAoL,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvL,EAAA,CAAAoL,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAzL,EAAA,CAAAoL,iBAAA,CAAAM,EAAA,CAAAC,eAAA;AAAA;;QAAtBhG,uBAAsB;EAAAiG,SAAA;EAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MCbvB/L,EAHZ,CAAAC,cAAA,aAAuB,aACX,aACwB,gBACoB;MAAnBD,EAAA,CAAAE,UAAA,mBAAA+L,wDAAA;QAAA,OAASD,GAAA,CAAAxD,MAAA,EAAQ;MAAA,EAAC;MACvCxI,EAAA,CAAAU,SAAA,kBAAuC;MAC3CV,EAAA,CAAAW,YAAA,EAAS;MACTX,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAe,MAAA,oBAAa;MAAAf,EAAA,CAAAW,YAAA,EAAK;MACtBX,EAAA,CAAAC,cAAA,aAA4B;MAIxBD,EAHA,CAAAyD,UAAA,IAAAyI,wCAAA,oBAA8E,IAAAC,wCAAA,oBAGC;MAK3FnM,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACD;MAuJTX,EArJA,CAAAyD,UAAA,KAAA2I,sCAAA,iBAAwC,KAAAC,sCAAA,iBAqJS;MAIrDrM,EAAA,CAAAW,YAAA,EAAM;MAGNX,EAAA,CAAAU,SAAA,sBAAiC;;;MAtKSV,EAAA,CAAAgB,SAAA,GAAyB;MAAzBhB,EAAA,CAAAuB,UAAA,UAAAyK,GAAA,CAAAtG,SAAA,IAAAsG,GAAA,CAAA9K,KAAA,CAAyB;MAGvBlB,EAAA,CAAAgB,SAAA,EAAyB;MAAzBhB,EAAA,CAAAuB,UAAA,UAAAyK,GAAA,CAAAtG,SAAA,IAAAsG,GAAA,CAAA9K,KAAA,CAAyB;MAO3ClB,EAAA,CAAAgB,SAAA,EAAgB;MAAhBhB,EAAA,CAAAuB,UAAA,UAAAyK,GAAA,CAAA5F,SAAA,CAAgB;MAqJNpG,EAAA,CAAAgB,SAAA,EAAe;MAAfhB,EAAA,CAAAuB,UAAA,SAAAyK,GAAA,CAAA5F,SAAA,CAAe;;;iBDzJvCxG,WAAW,EAAA8L,EAAA,CAAAY,OAAA,EAAAZ,EAAA,CAAAa,UAAA,EAAE1M,YAAY,EAAA2M,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,QAAA,EAAE5M,WAAW,EAAEC,mBAAmB;EAAA4M,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}