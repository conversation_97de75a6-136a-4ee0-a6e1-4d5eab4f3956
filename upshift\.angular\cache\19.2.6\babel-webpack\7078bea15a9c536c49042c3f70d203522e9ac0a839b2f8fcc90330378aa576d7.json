{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\models\\goal.model.ts"], "sourcesContent": ["export type GoalUnit = 'count' | 'steps' | 'm' | 'km' | 'sec' | 'min' | 'hr' | 'Cal' | 'g' | 'mg' | 'drink' | 'pages' | 'books' | '%' | '€' | '$' | '£' | 'days' | 'weeks' | 'months' | 'years';\n\nexport interface Goal {\n  id?: string; // Firestore ID\n  user_id: string;\n  name: string;\n  description: string;\n  emoji: string;\n  start_date: Date;\n  end_date?: Date;\n\n  goal_value: number;\n  current_value: number;\n  goal_unit: GoalUnit;\n\n  before_photo?: string;\n  after_photo?: string;\n  public: boolean;\n}\n\nexport interface MicroGoal {\n  id?: string; // Firestore ID\n  goal_id: string;\n  title: string;\n  completed: boolean;\n  completed_at?: Date;\n}\n\nexport interface GoalJournalEntry {\n  id?: string; // Firestore ID\n  goal_id: string;\n  milestone_percentage: number;\n  content: string;\n  created_at: Date;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}