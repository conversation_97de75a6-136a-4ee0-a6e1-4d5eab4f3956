<section class="daily-side-quest" *ngIf="enableSidequests && isTodaySelected">
  <h2>Daily Group Side Quest</h2>

  <div class="no-sidequest" *ngIf="!dailyQuest">
    <p>No side quest available for this group.</p>
    <!-- Side quests are automatically loaded from the pool, no need for manual creation -->
  </div>

  <div class="quest-list" *ngIf="dailyQuest">
    <div class="quest-item"
         [class.completed]="memberStatus?.completed"
         [class.disabled-quest]="isBeforeJoinDate"
         (click)="!isBeforeJoinDate && !togglingSideQuest && toggleSideQuest()">
      <div class="quest-icon">
        {{ dailyQuest.current_quest.emoji }}
      </div>
      <div class="quest-info">
        <h3>{{ dailyQuest.current_quest.name }}</h3>
        <p>{{ dailyQuest.current_quest.description }}</p>
        <div class="progress-container">
          <div class="progress-text values">
            <span>
              {{ memberStatus?.completed ? dailyQuest.current_quest.goal_value : 0 }}/{{ dailyQuest.current_quest.goal_value }}
            </span>
            <span class="members-count">
              Members: {{ dailyQuest.completed_members_count }}/{{ dailyQuest.eligible_members_count }}
            </span>
          </div>
        </div>
      </div>
      <div class="quest-streak">
        🔥{{ dailyQuest.streak }}d
      </div>
    </div>
  </div>
</section>
