{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _ProfilePage;\nimport { inject } from '@angular/core';\nimport { IonRouterOutlet } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { UserService } from '../../services/user.service';\nimport { GoalService } from '../../services/goal.service';\nimport { of, switchMap, combineLatest, map } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { XpService, EntityType } from '../../services/xp.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/badges\", a0];\nconst _c1 = () => [\"/profile-settings\"];\nfunction ProfilePage_div_8_img_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 35);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.user.profile_picture, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.user.username);\n  }\n}\nfunction ProfilePage_div_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProfilePage_div_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.user.bio);\n  }\n}\nfunction ProfilePage_div_8_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(1, \"Add Bio\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePage_div_8_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 42);\n    i0.ɵɵelement(7, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 44)(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r5.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", category_r5.progress, \"%\")(\"background-color\", category_r5.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", category_r5.current_xp, \" XP\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r5.required_xp, \" XP needed\");\n  }\n}\nfunction ProfilePage_div_8_div_35_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_div_35_button_4_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.openAllGoalsModal());\n    });\n    i0.ɵɵtext(1, \"VIEW ALL\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfilePage_div_8_div_35_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52)(2, \"span\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"span\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const goal_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(goal_r7.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r7.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", goal_r7.current_value, \"/\", goal_r7.goal_value, \" \", goal_r7.goal_unit, \"\");\n  }\n}\nfunction ProfilePage_div_8_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"h3\");\n    i0.ɵɵtext(3, \"\\uD83C\\uDFAF Goals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProfilePage_div_8_div_35_button_4_Template, 2, 0, \"button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵtemplate(6, ProfilePage_div_8_div_35_div_6_Template, 9, 5, \"div\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.publicGoals.length > 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.displayedGoals);\n  }\n}\nfunction ProfilePage_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7);\n    i0.ɵɵtemplate(3, ProfilePage_div_8_img_3_Template, 1, 2, \"img\", 8)(4, ProfilePage_div_8_ng_container_4_Template, 2, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 10)(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 13)(11, \"div\", 14);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 16);\n    i0.ɵɵtemplate(16, ProfilePage_div_8_div_16_Template, 2, 1, \"div\", 17)(17, ProfilePage_div_8_button_17_Template, 2, 0, \"button\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 19)(19, \"form\", 20);\n    i0.ɵɵlistener(\"ngSubmit\", function ProfilePage_div_8_Template_form_ngSubmit_19_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateBio());\n    });\n    i0.ɵɵelementStart(20, \"input\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ProfilePage_div_8_Template_input_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedBio, $event) || (ctx_r1.editedBio = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 22)(22, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_8_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleBioForm());\n    });\n    i0.ɵɵtext(23, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 24);\n    i0.ɵɵtext(25, \"Save\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(26, \"div\", 25)(27, \"h2\");\n    i0.ɵɵtext(28, \"XP Progress\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, ProfilePage_div_8_div_29_Template, 13, 8, \"div\", 26);\n    i0.ɵɵelementStart(30, \"div\", 27)(31, \"div\", 28);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 29);\n    i0.ɵɵtext(34, \" Reach required XP in all categories to level up \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, ProfilePage_div_8_div_35_Template, 7, 2, \"div\", 30);\n    i0.ɵɵelementStart(36, \"div\", 31)(37, \"a\", 32)(38, \"span\", 33);\n    i0.ɵɵtext(39, \"\\uD83C\\uDFC6\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" View Badges \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"a\", 34);\n    i0.ɵɵtext(42, \"\\u2699\\uFE0F Settings\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.profile_picture);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.user.profile_picture);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.user.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", ctx_r1.user.username, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Level \", ctx_r1.user.level, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.user.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.bio && ctx_r1.user.bio.trim() !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.user.bio || ctx_r1.user.bio.trim() === \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showBioForm ? \"block\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedBio);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Next Level: \", ctx_r1.nextLevel, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.publicGoals.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(16, _c0, ctx_r1.user.id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(18, _c1));\n  }\n}\nfunction ProfilePage_div_9_div_8_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"span\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 70);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const microgoal_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(microgoal_r9.completed ? \"\\u2705\" : \"\\u2B1C\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(microgoal_r9.title);\n  }\n}\nfunction ProfilePage_div_9_div_8_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"h4\");\n    i0.ɵɵtext(2, \"Microgoals:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ProfilePage_div_9_div_8_div_9_div_3_Template, 5, 2, \"div\", 67);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const goal_r10 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", goal_r10.microgoals);\n  }\n}\nfunction ProfilePage_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64)(2, \"span\", 53);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"span\", 56);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, ProfilePage_div_9_div_8_div_9_Template, 4, 1, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const goal_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(goal_r10.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", goal_r10.current_value, \"/\", goal_r10.goal_value, \" \", goal_r10.goal_unit, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", goal_r10.microgoals && goal_r10.microgoals.length > 0);\n  }\n}\nfunction ProfilePage_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAllGoalsModal());\n    });\n    i0.ɵɵelementStart(1, \"div\", 58);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_9_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementStart(2, \"div\", 59)(3, \"h3\");\n    i0.ɵɵtext(4, \"\\uD83C\\uDFAF All Goals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ProfilePage_div_9_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAllGoalsModal());\n    });\n    i0.ɵɵtext(6, \"\\u2715\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 61);\n    i0.ɵɵtemplate(8, ProfilePage_div_9_div_8_Template, 10, 6, \"div\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.publicGoals);\n  }\n}\nexport class ProfilePage {\n  constructor() {\n    // User data\n    this.user = null;\n    this.userSubscription = null;\n    // XP categories\n    this.categories = [];\n    this.nextLevel = 0;\n    // Bio form\n    this.showBioForm = false;\n    this.editedBio = '';\n    // Goals\n    this.publicGoals = [];\n    this.showAllGoalsModal = false;\n    this.supabaseService = inject(SupabaseService);\n    this.userService = inject(UserService);\n    this.goalService = inject(GoalService);\n    this.xpService = inject(XpService);\n    this.routerOutlet = inject(IonRouterOutlet);\n  }\n  ngOnInit() {\n    // Subscribe to the current user profile from UserService\n    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {\n      if (userProfile) {\n        var _user$level, _user$strength_xp, _user$money_xp, _user$health_xp, _user$knowledge_xp;\n        // Convert UserProfile to User\n        const user = userProfile;\n        // Store the original user data for reference\n        // Ensure all required fields have default values\n        this.user = {\n          ...user,\n          level: (_user$level = user.level) !== null && _user$level !== void 0 ? _user$level : 1,\n          // Use nullish coalescing to handle 0 values correctly\n          strength_xp: (_user$strength_xp = user.strength_xp) !== null && _user$strength_xp !== void 0 ? _user$strength_xp : 0,\n          money_xp: (_user$money_xp = user.money_xp) !== null && _user$money_xp !== void 0 ? _user$money_xp : 0,\n          health_xp: (_user$health_xp = user.health_xp) !== null && _user$health_xp !== void 0 ? _user$health_xp : 0,\n          knowledge_xp: (_user$knowledge_xp = user.knowledge_xp) !== null && _user$knowledge_xp !== void 0 ? _user$knowledge_xp : 0,\n          bio: user.bio || '',\n          title: user.title || '🥚 Beginner'\n        };\n        this.editedBio = this.user.bio || '';\n        this.calculateXpProgress();\n        this.loadPublicGoals();\n        // Ensure edit button visibility is correct on page load\n        setTimeout(() => {\n          var _this$user;\n          const editBioBtn = document.getElementById('edit-bio-btn');\n          if (editBioBtn && (!((_this$user = this.user) !== null && _this$user !== void 0 && _this$user.bio) || this.user.bio.trim() === '')) {\n            editBioBtn.style.display = 'none';\n          }\n        }, 100);\n      } else {\n        console.error('Profile: No user profile data received');\n        // If no profile data, try to get the auth user and ensure it exists\n        this.supabaseService.currentUser$.pipe(switchMap(authUser => {\n          if (!authUser) {\n            return of(null);\n          }\n          // Use the ensureUserExists method to get or create the user\n          return this.userService.ensureUserExists(authUser);\n        })).subscribe();\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  ionViewWillEnter() {\n    // Check if we're coming from another page (not initial load)\n    if (this.routerOutlet.canGoBack()) {\n      // Refresh the user profile\n      this.userService.refreshCurrentUserProfile().then(() => {}).catch(error => {\n        console.error('Profile: Error refreshing profile:', error);\n      });\n    } else {}\n  }\n  calculateXpProgress() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.user) {\n        console.error('Cannot calculate XP progress: user is null');\n        return;\n      }\n      // Make sure user.level is defined and is a number\n      if (typeof _this.user.level !== 'number') {\n        console.error('User level is not a number:', _this.user.level);\n        _this.user.level = 1; // Default to level 1 if not set\n      }\n      // Use the XP service to calculate the progress\n      _this.xpService.calculateXpProgress(_this.user, EntityType.USER).subscribe(result => {\n        if (result) {\n          _this.categories = result.categories;\n          _this.nextLevel = result.next_level;\n        }\n      });\n    })();\n  }\n  toggleBioForm() {\n    this.showBioForm = !this.showBioForm;\n    // Reset the edited bio to the current bio when opening the form\n    if (this.showBioForm && this.user) {\n      this.editedBio = this.user.bio || '';\n    }\n    // When closing the form, ensure edit button visibility is correct\n    if (!this.showBioForm) {\n      setTimeout(() => {\n        var _this$user2, _this$user3;\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!((_this$user2 = this.user) !== null && _this$user2 !== void 0 && _this$user2.bio) || ((_this$user3 = this.user) === null || _this$user3 === void 0 ? void 0 : _this$user3.bio.trim()) === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    }\n  }\n  updateBio() {\n    if (!this.user || !this.user.id) return;\n    // Trim and validate bio\n    const bio = this.editedBio.trim();\n    if (bio.length > 100) {\n      // Show error message\n      return;\n    }\n    // Update user bio (will be set to empty string if only whitespace)\n    this.userService.updateUserBio(this.user.id, bio).then(() => {\n      if (this.user) {\n        // If bio is empty, set it to empty string\n        this.user.bio = bio === '' ? '' : bio;\n      }\n      this.showBioForm = false;\n      // Force DOM update to ensure edit button visibility is correct\n      setTimeout(() => {\n        var _this$user4;\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!((_this$user4 = this.user) !== null && _this$user4 !== void 0 && _this$user4.bio) || this.user.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    });\n  }\n  loadPublicGoals() {\n    var _this$user5;\n    if (!((_this$user5 = this.user) !== null && _this$user5 !== void 0 && _this$user5.id)) return;\n    this.goalService.getPublicGoals(this.user.id).pipe(switchMap(goals => {\n      if (goals.length === 0) {\n        return of([]);\n      }\n      // Get microgoals for each goal\n      const goalObservables = goals.map(goal => this.goalService.getMicroGoals(goal.id).pipe(map(microgoals => ({\n        ...goal,\n        microgoals\n      }))));\n      return combineLatest(goalObservables);\n    })).subscribe({\n      next: goalsWithMicrogoals => {\n        this.publicGoals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('Error loading public goals:', error);\n      }\n    });\n  }\n  get displayedGoals() {\n    return this.publicGoals.slice(0, 3);\n  }\n  openAllGoalsModal() {\n    this.showAllGoalsModal = true;\n  }\n  closeAllGoalsModal() {\n    this.showAllGoalsModal = false;\n  }\n}\n_ProfilePage = ProfilePage;\n_ProfilePage.ɵfac = function ProfilePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ProfilePage)();\n};\n_ProfilePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _ProfilePage,\n  selectors: [[\"app-profile\"]],\n  decls: 11,\n  vars: 2,\n  consts: [[1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [\"class\", \"goals-modal\", 3, \"click\", 4, \"ngIf\"], [1, \"profile-container\"], [1, \"profile-header\"], [1, \"profile-picture\"], [3, \"src\", \"alt\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"profile-info\"], [1, \"profile-name\"], [1, \"profile-username\"], [1, \"profile-level\"], [1, \"level-badge\"], [1, \"profile-title\"], [1, \"profile-bio-container\"], [\"class\", \"profile-bio\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"add-bio-btn\", \"class\", \"edit-bio-btn\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"bio-form\", 1, \"bio-form\"], [3, \"ngSubmit\"], [\"type\", \"text\", \"name\", \"bio\", \"maxlength\", \"100\", \"placeholder\", \"Add a short bio (max 100 characters)\", 3, \"ngModelChange\", \"ngModel\"], [1, \"bio-form-buttons\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [\"type\", \"submit\", 1, \"save-btn\"], [1, \"xp-section\"], [\"class\", \"category-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"next-level-info\"], [1, \"next-level-text\"], [1, \"next-level-requirements\"], [\"class\", \"goals-section\", 4, \"ngIf\"], [1, \"button-container\"], [1, \"badges-button\", 3, \"routerLink\"], [2, \"margin-right\", \"5px\"], [1, \"settings-button\", 3, \"routerLink\"], [3, \"src\", \"alt\"], [1, \"profile-bio\", 3, \"click\"], [\"id\", \"add-bio-btn\", 1, \"edit-bio-btn\", 3, \"click\"], [1, \"category-card\"], [1, \"category-header\"], [1, \"category-icon\"], [1, \"category-name\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"xp-text\"], [1, \"goals-section\"], [1, \"goals-header\"], [\"class\", \"view-all-btn\", 3, \"click\", 4, \"ngIf\"], [1, \"goals-list\"], [\"class\", \"goal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"view-all-btn\", 3, \"click\"], [1, \"goal-item\"], [1, \"goal-info\"], [1, \"goal-emoji\"], [1, \"goal-name\"], [1, \"goal-progress\"], [1, \"progress-text\"], [1, \"goals-modal\", 3, \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"modal-header\"], [1, \"close-btn\", 3, \"click\"], [1, \"modal-body\"], [\"class\", \"goal-detail\", 4, \"ngFor\", \"ngForOf\"], [1, \"goal-detail\"], [1, \"goal-header\"], [\"class\", \"microgoals\", 4, \"ngIf\"], [1, \"microgoals\"], [\"class\", \"microgoal\", 4, \"ngFor\", \"ngForOf\"], [1, \"microgoal\"], [1, \"microgoal-status\"], [1, \"microgoal-title\"]],\n  template: function ProfilePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1);\n      i0.ɵɵelement(3, \"img\", 2);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Profile\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(8, ProfilePage_div_8_Template, 43, 19, \"div\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(9, ProfilePage_div_9_Template, 9, 1, \"div\", 4);\n      i0.ɵɵelement(10, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", ctx.user);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showAllGoalsModal);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent],\n  styles: [\".quest-message[_ngcontent-%COMP%] {\\n  color: #FF9500;\\n  font-weight: 500;\\n  font-size: small;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent-color);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  \\n\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent-color);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n\\n\\n\\n.schedule-container[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.days-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.day-checkbox[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n\\n.day-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  cursor: pointer;\\n  margin-bottom: 0;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 8px;\\n  margin-top: 8px;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%] {\\n  justify-content: center;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 2px;\\n}\\n\\n.month-days-selector[_ngcontent-%COMP%]   .day-checkbox[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.quests[_ngcontent-%COMP%], .side-quests[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.quest-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.quest-item[_ngcontent-%COMP%] {\\n  background-color: var(--quest-bg);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.quest-item[_ngcontent-%COMP%]:active {\\n  transform: scale(0.98);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%] {\\n  border-color: var(--accent-color);\\n}\\n\\n.quest-item.completed[_ngcontent-%COMP%]   .quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.quest-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  min-width: 24px;\\n  text-align: center;\\n}\\n\\n.quest-info[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.quest-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.quest-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n  margin-bottom: 4px;\\n}\\n\\n.progress[_ngcontent-%COMP%], .progress-time[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 12px;\\n}\\n\\n.quest-streak[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.side-quests[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-top: 32px;\\n}\\n\\n.quests[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 4px 0;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  -webkit-appearance: none;\\n  -moz-appearance: none;\\n       appearance: none;\\n  width: 100%;\\n  height: 4px;\\n  background: var(--inactive-date);\\n  outline: none;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  -webkit-appearance: none;\\n  appearance: none;\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: var(--accent-color);\\n  cursor: pointer;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-thumb {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  background: var(--accent-color);\\n  cursor: pointer;\\n  border: none;\\n  position: relative;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-webkit-slider-runnable-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n.progress-slider[_ngcontent-%COMP%]::-moz-range-track {\\n  height: 4px;\\n  border-radius: 2px;\\n}\\n\\n\\n\\n.progress-slider[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-color) 50%, var(--bg-tertiary) 50%, var(--bg-tertiary) 100%);\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text);\\n  margin-top: 2px;\\n}\\n\\n[_nghost-%COMP%] {\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n  --active-date: #4169E1;\\n  --inactive-date: #2C2C2E;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  scrollbar-width: none;\\n  overflow-y: auto;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.scroll-y[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.week-calendar[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.days[_ngcontent-%COMP%], .dates[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  text-align: center;\\n  gap: 8px;\\n}\\n\\n.days[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.date[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  margin: 0 auto;\\n  font-size: 14px;\\n  background-color: var(--inactive-date);\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n  position: relative;\\n}\\n\\n.date-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  fill: transparent;\\n  stroke-width: 2.5;\\n  stroke-linecap: round;\\n  transform-origin: center;\\n  transform: rotate(-90deg);\\n  transition: stroke-dasharray 0.3s ease;\\n}\\n\\n\\n\\n.date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: var(--accent-color);\\n  stroke-opacity: 0.9;\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   .progress-circle.low[_ngcontent-%COMP%] {\\n  stroke: var(--accent-color);\\n}\\n\\n.date-progress[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%] {\\n  stroke-width: 3;\\n}\\n\\n\\n\\n.date.selected[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #78a8f3;\\n  stroke-opacity: 0.7;\\n}\\n\\n.date.active[_ngcontent-%COMP%]   .date-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  stroke: #78a8f3;\\n  \\n\\n  \\n\\n  stroke-opacity: 0.7;\\n}\\n\\n.date-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.date.active[_ngcontent-%COMP%] {\\n  background-color: var(--active-date);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n}\\n\\n.date.selected[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -4px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 4px;\\n  height: 4px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n}\\n\\n.date[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.date.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 16px;\\n}\\n\\n.side-quests[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 90%;\\n  height: 1px;\\n  background: linear-gradient(to right, transparent, #4B0082, transparent);\\n}\\n\\n.calendar[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  padding: 10px;\\n  background: var(--bg-secondary);\\n  border-radius: 8px;\\n}\\n\\n.calendar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 10px;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 5px;\\n  text-align: center;\\n}\\n\\n.day-name[_ngcontent-%COMP%] {\\n  color: var(--secondary-text);\\n  font-size: 14px;\\n}\\n\\n.day-number[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 30px;\\n  height: 30px;\\n  border-radius: 50%;\\n  cursor: pointer;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  margin: 0 auto;\\n}\\n\\n.day-number[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.day-number.selected[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.day-number.today[_ngcontent-%COMP%] {\\n  border: 2px solid var(--primary-color);\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border: none;\\n  background: var(--bg-tertiary);\\n  color: var(--text-primary);\\n  border-radius: 4px;\\n  cursor: pointer;\\n  text-decoration: none;\\n}\\n\\n.nav-arrow[_ngcontent-%COMP%]:hover {\\n  background: var(--bg-hover);\\n}\\n\\n.time-display[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  margin-right: 16px;\\n}\\n\\n\\n\\ninput[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, \\ninput[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button {\\n  opacity: 0.3;\\n}\\n\\n.profile-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  margin-bottom: 80px;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.profile-picture[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background-color: var(--card-bg);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 32px;\\n  margin-right: 20px;\\n  overflow: hidden;\\n}\\n\\n.profile-picture[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.profile-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n\\n.profile-username[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--secondary-text);\\n  margin-bottom: 5px;\\n}\\n\\n.profile-bio[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-secondary, #a0a3b1);\\n  margin-bottom: 10px;\\n  font-style: italic;\\n  max-width: 300px;\\n  cursor: pointer;\\n}\\n\\n.profile-bio-container[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  padding-left: 100px;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: var(--accent-color, #4169e1);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  transition: all 0.2s ease;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(65, 105, 225, 0.1);\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .profile-bio:empty + #edit-bio-btn {\\n  display: none;\\n}\\n\\n.bio-form[_ngcontent-%COMP%] {\\n  margin: 0 auto 20px;\\n  max-width: 400px;\\n}\\n\\n.bio-form[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border-radius: 8px;\\n  border: 1px solid var(--border, #26272e);\\n  background-color: var(--surface, #16171c);\\n  color: var(--text, #ffffff);\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n}\\n\\n.bio-form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color, #4169e1);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: var(--text, #ffffff);\\n  border: 1px solid var(--border, #26272e);\\n  padding: 8px 16px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n\\n.profile-level[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 5px;\\n}\\n\\n.level-badge[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n  border-radius: 12px;\\n  padding: 2px 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-right: 10px;\\n}\\n\\n.profile-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--accent-color);\\n}\\n\\n.xp-section[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.xp-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.category-card[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  border-radius: 12px;\\n  padding: 15px;\\n  margin-bottom: 15px;\\n}\\n\\n.category-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.category-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 10px;\\n}\\n\\n.category-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background-color: var(--bg-color);\\n  border-radius: 5px;\\n  margin-bottom: 8px;\\n  overflow: hidden;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  border-radius: 5px;\\n}\\n\\n.xp-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.next-level-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n  padding: 15px;\\n  background-color: var(--card-bg);\\n  border-radius: 12px;\\n}\\n\\n.next-level-text[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin-bottom: 10px;\\n}\\n\\n.next-level-requirements[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  margin-top: 15px;\\n  display: inline-block;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.back-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n  gap: 10px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-top: 10px;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  color: var(--accent-color);\\n  border: 1px solid var(--accent-color);\\n  border-radius: 20px;\\n  padding: 5px 12px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.action-button.danger[_ngcontent-%COMP%] {\\n  color: var(--danger-color);\\n  border-color: var(--danger-color);\\n}\\n\\n.settings-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  background-color: var(--accent-color);\\n  color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 10px 20px;\\n  font-size: 16px;\\n  text-decoration: none;\\n  text-align: center;\\n}\\n\\n.badges-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-right: 10px;\\n  padding: 8px 16px;\\n  background-color: #1c1c1e;\\n  color: white;\\n  border: 1px solid #4d7bff;\\n  border-radius: 20px;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n\\n.button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 30px;\\n}\\n\\n\\n\\n.goals-section[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  margin-bottom: 20px;\\n}\\n\\n.goals-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.goals-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin: 0;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: var(--accent-color);\\n  border: none;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  transition: background-color 0.2s;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(65, 105, 225, 0.1);\\n}\\n\\n.goals-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n\\n.goal-item[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  border-radius: 8px;\\n  padding: 12px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.goal-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n\\n.goal-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.goal-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--secondary-text);\\n}\\n\\n\\n\\n.goals-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 1000;\\n}\\n\\n.goals-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  border-radius: 12px;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n  position: relative;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  border-bottom: 1px solid var(--quest-border);\\n  padding-bottom: 15px;\\n}\\n\\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin: 0;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--secondary-text);\\n  font-size: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 4px;\\n  transition: color 0.2s;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--text-color);\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%] {\\n  background-color: var(--card-bg);\\n  border-radius: 8px;\\n  padding: 15px;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%]   .goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%]   .goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%]   .goal-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%]   .goal-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.goal-detail[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--secondary-text);\\n}\\n\\n.microgoals[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.microgoals[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-bottom: 10px;\\n  color: var(--secondary-text);\\n}\\n\\n.microgoal[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  padding: 5px 0;\\n}\\n\\n.microgoal-status[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.microgoal-title[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-color);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "IonRouterOutlet", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "UserService", "GoalService", "of", "switchMap", "combineLatest", "map", "NavigationComponent", "SupabaseService", "XpService", "EntityType", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "user", "profile_picture", "ɵɵsanitizeUrl", "username", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementStart", "ɵɵlistener", "ProfilePage_div_8_div_16_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "toggleBioForm", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "bio", "ProfilePage_div_8_button_17_Template_button_click_0_listener", "_r4", "category_r5", "icon", "name", "ɵɵstyleProp", "progress", "color", "ɵɵtextInterpolate1", "current_xp", "required_xp", "ProfilePage_div_8_div_35_button_4_Template_button_click_0_listener", "_r6", "openAllGoalsModal", "goal_r7", "emoji", "ɵɵtextInterpolate3", "current_value", "goal_value", "goal_unit", "ɵɵtemplate", "ProfilePage_div_8_div_35_button_4_Template", "ProfilePage_div_8_div_35_div_6_Template", "publicGoals", "length", "displayedGoals", "ProfilePage_div_8_img_3_Template", "ProfilePage_div_8_ng_container_4_Template", "ProfilePage_div_8_div_16_Template", "ProfilePage_div_8_button_17_Template", "ProfilePage_div_8_Template_form_ngSubmit_19_listener", "_r1", "updateBio", "ɵɵtwoWayListener", "ProfilePage_div_8_Template_input_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "editedBio", "ProfilePage_div_8_Template_button_click_22_listener", "ProfilePage_div_8_div_29_Template", "ProfilePage_div_8_div_35_Template", "level", "title", "trim", "showBioForm", "ɵɵtwoWayProperty", "categories", "nextLevel", "ɵɵpureFunction1", "_c0", "id", "ɵɵpureFunction0", "_c1", "microgoal_r9", "completed", "ProfilePage_div_9_div_8_div_9_div_3_Template", "goal_r10", "microgoals", "ProfilePage_div_9_div_8_div_9_Template", "ProfilePage_div_9_Template_div_click_0_listener", "_r8", "closeAllGoalsModal", "ProfilePage_div_9_Template_div_click_1_listener", "stopPropagation", "ProfilePage_div_9_Template_button_click_5_listener", "ProfilePage_div_9_div_8_Template", "ProfilePage", "constructor", "userSubscription", "showAllGoalsModal", "supabaseService", "userService", "goalService", "xpService", "routerOutlet", "ngOnInit", "currentUserProfile$", "subscribe", "userProfile", "_user$level", "_user$strength_xp", "_user$money_xp", "_user$health_xp", "_user$knowledge_xp", "strength_xp", "money_xp", "health_xp", "knowledge_xp", "calculateXpProgress", "loadPublicGoals", "setTimeout", "_this$user", "editBioBtn", "document", "getElementById", "style", "display", "console", "error", "currentUser$", "pipe", "authUser", "ensureUserExists", "ngOnDestroy", "unsubscribe", "ionViewWillEnter", "canGoBack", "refreshCurrentUserProfile", "then", "catch", "_this", "_asyncToGenerator", "USER", "result", "next_level", "_this$user2", "_this$user3", "updateUserBio", "_this$user4", "_this$user5", "getPublicGoals", "goals", "goalObservables", "goal", "getMicroGoals", "next", "goalsWithMicrogoals", "slice", "selectors", "decls", "vars", "consts", "template", "ProfilePage_Template", "rf", "ctx", "ProfilePage_div_8_Template", "ProfilePage_div_9_Template", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\profile\\profile.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\profile\\profile.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\nimport { IonRouterOutlet } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { UserService } from '../../services/user.service';\nimport { GoalService } from '../../services/goal.service';\nimport { User } from '../../models/user.model';\nimport { Goal, MicroGoal } from '../../models/supabase.models';\nimport { Subscription, of, switchMap, combineLatest, map } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { XpService, EntityType } from '../../services/xp.service';\n\ninterface CategoryDisplay {\n  name: string;\n  icon: string;\n  color: string;\n  current_xp: number;\n  required_xp: number;\n  progress: number;\n}\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.page.html',\n  styleUrls: ['./profile.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]\n})\nexport class ProfilePage implements OnInit {\n  // User data\n  user: User | null = null;\n  userSubscription: Subscription | null = null;\n\n  // XP categories\n  categories: CategoryDisplay[] = [];\n  nextLevel = 0;\n\n  // Bio form\n  showBioForm = false;\n  editedBio = '';\n\n  // Goals\n  publicGoals: any[] = [];\n  showAllGoalsModal = false;\n\n  private supabaseService = inject(SupabaseService);\n  private userService = inject(UserService);\n  private goalService = inject(GoalService);\n  private xpService = inject(XpService);\n  private routerOutlet = inject(IonRouterOutlet);\n\n  constructor() {}\n\n  ngOnInit() {\n    // Subscribe to the current user profile from UserService\n    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {\n\n      if (userProfile) {\n        // Convert UserProfile to User\n        const user = userProfile as unknown as User;\n\n        // Store the original user data for reference\n\n        // Ensure all required fields have default values\n        this.user = {\n          ...user,\n          level: user.level ?? 1,  // Use nullish coalescing to handle 0 values correctly\n          strength_xp: user.strength_xp ?? 0,\n          money_xp: user.money_xp ?? 0,\n          health_xp: user.health_xp ?? 0,\n          knowledge_xp: user.knowledge_xp ?? 0,\n          bio: user.bio || '',\n          title: user.title || '🥚 Beginner'\n        };\n\n        this.editedBio = this.user.bio || '';\n\n\n\n        this.calculateXpProgress();\n        this.loadPublicGoals();\n\n        // Ensure edit button visibility is correct on page load\n        setTimeout(() => {\n          const editBioBtn = document.getElementById('edit-bio-btn');\n          if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {\n            editBioBtn.style.display = 'none';\n          }\n        }, 100);\n      } else {\n        console.error('Profile: No user profile data received');\n\n        // If no profile data, try to get the auth user and ensure it exists\n        this.supabaseService.currentUser$.pipe(\n          switchMap(authUser => {\n            if (!authUser) {\n              return of(null);\n            }\n\n\n            // Use the ensureUserExists method to get or create the user\n            return this.userService.ensureUserExists(authUser);\n          })\n        ).subscribe();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n\n  ionViewWillEnter() {\n\n    // Check if we're coming from another page (not initial load)\n    if (this.routerOutlet.canGoBack()) {\n\n      // Refresh the user profile\n      this.userService.refreshCurrentUserProfile().then(() => {\n      }).catch(error => {\n        console.error('Profile: Error refreshing profile:', error);\n      });\n    } else {\n    }\n  }\n\n  async calculateXpProgress() {\n    if (!this.user) {\n      console.error('Cannot calculate XP progress: user is null');\n      return;\n    }\n\n\n    // Make sure user.level is defined and is a number\n    if (typeof this.user.level !== 'number') {\n      console.error('User level is not a number:', this.user.level);\n      this.user.level = 1; // Default to level 1 if not set\n    }\n\n    // Use the XP service to calculate the progress\n    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {\n      if (result) {\n        this.categories = result.categories;\n        this.nextLevel = result.next_level;\n      }\n    });\n  }\n\n  toggleBioForm() {\n    this.showBioForm = !this.showBioForm;\n\n    // Reset the edited bio to the current bio when opening the form\n    if (this.showBioForm && this.user) {\n      this.editedBio = this.user.bio || '';\n    }\n\n    // When closing the form, ensure edit button visibility is correct\n    if (!this.showBioForm) {\n      setTimeout(() => {\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!this.user?.bio || this.user?.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    }\n  }\n\n  updateBio() {\n    if (!this.user || !this.user.id) return;\n\n    // Trim and validate bio\n    const bio = this.editedBio.trim();\n    if (bio.length > 100) {\n      // Show error message\n      return;\n    }\n\n    // Update user bio (will be set to empty string if only whitespace)\n    this.userService.updateUserBio(this.user.id, bio).then(() => {\n      if (this.user) {\n        // If bio is empty, set it to empty string\n        this.user.bio = bio === '' ? '' : bio;\n      }\n      this.showBioForm = false;\n\n      // Force DOM update to ensure edit button visibility is correct\n      setTimeout(() => {\n        const editBioBtn = document.getElementById('edit-bio-btn');\n        if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {\n          editBioBtn.style.display = 'none';\n        }\n      }, 0);\n    });\n  }\n\n  loadPublicGoals() {\n    if (!this.user?.id) return;\n\n    this.goalService.getPublicGoals(this.user.id).pipe(\n      switchMap(goals => {\n        if (goals.length === 0) {\n          return of([]);\n        }\n\n        // Get microgoals for each goal\n        const goalObservables = goals.map(goal =>\n          this.goalService.getMicroGoals(goal.id!).pipe(\n            map(microgoals => ({\n              ...goal,\n              microgoals\n            }))\n          )\n        );\n\n        return combineLatest(goalObservables);\n      })\n    ).subscribe({\n      next: goalsWithMicrogoals => {\n        this.publicGoals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('Error loading public goals:', error);\n      }\n    });\n  }\n\n  get displayedGoals() {\n    return this.publicGoals.slice(0, 3);\n  }\n\n  openAllGoalsModal() {\n    this.showAllGoalsModal = true;\n  }\n\n  closeAllGoalsModal() {\n    this.showAllGoalsModal = false;\n  }\n}\n", "\r\n    <div class=\"container\">\r\n        <header>\r\n            <div class=\"logo\">\r\n                <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n                <span>Upshift</span>\r\n            </div>\r\n            <h1>Profile</h1>\r\n        </header>\r\n\r\n        <div class=\"profile-container\" *ngIf=\"user\">\r\n            <div class=\"profile-header\">\r\n                <div class=\"profile-picture\">\r\n                    <img *ngIf=\"user.profile_picture\" [src]=\"user.profile_picture\" [alt]=\"user.username\">\r\n                    <ng-container *ngIf=\"!user.profile_picture\">👤</ng-container>\r\n                </div>\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-name\">{{ user.name }}</div>\r\n                    <div class=\"profile-username\">&#64;{{ user.username }}</div>\r\n\r\n                    <div class=\"profile-level\">\r\n                        <div class=\"level-badge\">Level {{ user.level }}</div>\r\n                        <div class=\"profile-title\">{{ user.title }}</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"profile-bio-container\">\r\n                <div class=\"profile-bio\" *ngIf=\"user.bio && user.bio.trim() !== ''\" (click)=\"toggleBioForm()\">{{ user.bio }}</div>\r\n                <button *ngIf=\"!user.bio || user.bio.trim() === ''\" id=\"add-bio-btn\" class=\"edit-bio-btn\" (click)=\"toggleBioForm()\">Add Bio</button>\r\n\r\n            </div>\r\n\r\n            <div id=\"bio-form\" class=\"bio-form\" [style.display]=\"showBioForm ? 'block' : 'none'\">\r\n                <form (ngSubmit)=\"updateBio()\">\r\n                    <input type=\"text\" name=\"bio\" maxlength=\"100\" placeholder=\"Add a short bio (max 100 characters)\" [(ngModel)]=\"editedBio\">\r\n                    <div class=\"bio-form-buttons\">\r\n                        <button type=\"button\" class=\"cancel-btn\" (click)=\"toggleBioForm()\">Cancel</button>\r\n                        <button type=\"submit\" class=\"save-btn\">Save</button>\r\n                    </div>\r\n                </form>\r\n            </div>\r\n\r\n            <div class=\"xp-section\">\r\n                <h2>XP Progress</h2>\r\n\r\n                <div class=\"category-card\" *ngFor=\"let category of categories\">\r\n                    <div class=\"category-header\">\r\n                        <div class=\"category-icon\">{{ category.icon }}</div>\r\n                        <div class=\"category-name\">{{ category.name }}</div>\r\n                    </div>\r\n                    <div class=\"progress-container\">\r\n                        <div class=\"progress-bar\" [style.width.%]=\"category.progress\" [style.background-color]=\"category.color\"></div>\r\n                    </div>\r\n                    <div class=\"xp-text\">\r\n                        <span>{{ category.current_xp }} XP</span>\r\n                        <span>{{ category.required_xp }} XP needed</span>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"next-level-info\">\r\n                    <div class=\"next-level-text\">Next Level: {{ nextLevel }}</div>\r\n                    <div class=\"next-level-requirements\">\r\n                        Reach required XP in all categories to level up\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Public Goals Section -->\r\n                <div class=\"goals-section\" *ngIf=\"publicGoals.length > 0\">\r\n                    <div class=\"goals-header\">\r\n                        <h3>🎯 Goals</h3>\r\n                        <button *ngIf=\"publicGoals.length > 3\" class=\"view-all-btn\" (click)=\"openAllGoalsModal()\">VIEW ALL</button>\r\n                    </div>\r\n\r\n                    <div class=\"goals-list\">\r\n                        <div class=\"goal-item\" *ngFor=\"let goal of displayedGoals\">\r\n                            <div class=\"goal-info\">\r\n                                <span class=\"goal-emoji\">{{ goal.emoji }}</span>\r\n                                <span class=\"goal-name\">{{ goal.name }}</span>\r\n                            </div>\r\n                            <div class=\"goal-progress\">\r\n                                <span class=\"progress-text\">{{ goal.current_value }}/{{ goal.goal_value }} {{ goal.goal_unit }}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"button-container\">\r\n                    <a [routerLink]=\"['/badges', user.id]\" class=\"badges-button\">\r\n                        <span style=\"margin-right: 5px;\">🏆</span> View Badges\r\n                    </a>\r\n                    <a [routerLink]=\"['/profile-settings']\" class=\"settings-button\">⚙️ Settings</a>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- All Goals Modal -->\r\n    <div class=\"goals-modal\" *ngIf=\"showAllGoalsModal\" (click)=\"closeAllGoalsModal()\">\r\n        <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n            <div class=\"modal-header\">\r\n                <h3>🎯 All Goals</h3>\r\n                <button class=\"close-btn\" (click)=\"closeAllGoalsModal()\">✕</button>\r\n            </div>\r\n            <div class=\"modal-body\">\r\n                <div class=\"goal-detail\" *ngFor=\"let goal of publicGoals\">\r\n                    <div class=\"goal-header\">\r\n                        <span class=\"goal-emoji\">{{ goal.emoji }}</span>\r\n                        <span class=\"goal-name\">{{ goal.name }}</span>\r\n                    </div>\r\n                    <div class=\"goal-progress\">\r\n                        <span class=\"progress-text\">{{ goal.current_value }}/{{ goal.goal_value }} {{ goal.goal_unit }}</span>\r\n                    </div>\r\n                    <div class=\"microgoals\" *ngIf=\"goal.microgoals && goal.microgoals.length > 0\">\r\n                        <h4>Microgoals:</h4>\r\n                        <div class=\"microgoal\" *ngFor=\"let microgoal of goal.microgoals\">\r\n                            <span class=\"microgoal-status\">{{ microgoal.completed ? '✅' : '⬜' }}</span>\r\n                            <span class=\"microgoal-title\">{{ microgoal.title }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";;AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,6BAA6B;AAGzD,SAAuBC,EAAE,EAAEC,SAAS,EAAEC,aAAa,EAAEC,GAAG,QAAQ,MAAM;AACtE,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;;;;;;;;;;ICA7CC,EAAA,CAAAC,SAAA,cAAqF;;;;IAAtBD,EAA7B,CAAAE,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,EAAAL,EAAA,CAAAM,aAAA,CAA4B,QAAAH,MAAA,CAAAC,IAAA,CAAAG,QAAA,CAAsB;;;;;IACpFP,EAAA,CAAAQ,uBAAA,GAA4C;IAAAR,EAAA,CAAAS,MAAA,mBAAE;;;;;;;IAalDT,EAAA,CAAAU,cAAA,cAA8F;IAA1BV,EAAA,CAAAW,UAAA,mBAAAC,uDAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,GAAc;IAAAT,EAAA,CAAAkB,YAAA,EAAM;;;;IAApBlB,EAAA,CAAAmB,SAAA,EAAc;IAAdnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAc;;;;;;IAC5GrB,EAAA,CAAAU,cAAA,iBAAoH;IAA1BV,EAAA,CAAAW,UAAA,mBAAAW,6DAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAApB,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,cAAO;IAAAT,EAAA,CAAAkB,YAAA,EAAS;;;;;IAmB5HlB,EAFR,CAAAU,cAAA,cAA+D,cAC9B,cACE;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IACpDlB,EAAA,CAAAU,cAAA,cAA2B;IAAAV,EAAA,CAAAS,MAAA,GAAmB;IAClDT,EADkD,CAAAkB,YAAA,EAAM,EAClD;IACNlB,EAAA,CAAAU,cAAA,cAAgC;IAC5BV,EAAA,CAAAC,SAAA,cAA8G;IAClHD,EAAA,CAAAkB,YAAA,EAAM;IAEFlB,EADJ,CAAAU,cAAA,cAAqB,WACX;IAAAV,EAAA,CAAAS,MAAA,IAA4B;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IACzClB,EAAA,CAAAU,cAAA,YAAM;IAAAV,EAAA,CAAAS,MAAA,IAAoC;IAElDT,EAFkD,CAAAkB,YAAA,EAAO,EAC/C,EACJ;;;;IAV6BlB,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,iBAAA,CAAAI,WAAA,CAAAC,IAAA,CAAmB;IACnBzB,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAoB,iBAAA,CAAAI,WAAA,CAAAE,IAAA,CAAmB;IAGpB1B,EAAA,CAAAmB,SAAA,GAAmC;IAACnB,EAApC,CAAA2B,WAAA,UAAAH,WAAA,CAAAI,QAAA,MAAmC,qBAAAJ,WAAA,CAAAK,KAAA,CAA0C;IAGjG7B,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAA8B,kBAAA,KAAAN,WAAA,CAAAO,UAAA,QAA4B;IAC5B/B,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAA8B,kBAAA,KAAAN,WAAA,CAAAQ,WAAA,eAAoC;;;;;;IAe1ChC,EAAA,CAAAU,cAAA,iBAA0F;IAA9BV,EAAA,CAAAW,UAAA,mBAAAsB,mEAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAqB,GAAA;MAAA,MAAA/B,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAgC,iBAAA,EAAmB;IAAA,EAAC;IAACnC,EAAA,CAAAS,MAAA,eAAQ;IAAAT,EAAA,CAAAkB,YAAA,EAAS;;;;;IAMnGlB,EAFR,CAAAU,cAAA,cAA2D,cAChC,eACM;IAAAV,EAAA,CAAAS,MAAA,GAAgB;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IAChDlB,EAAA,CAAAU,cAAA,eAAwB;IAAAV,EAAA,CAAAS,MAAA,GAAe;IAC3CT,EAD2C,CAAAkB,YAAA,EAAO,EAC5C;IAEFlB,EADJ,CAAAU,cAAA,cAA2B,eACK;IAAAV,EAAA,CAAAS,MAAA,GAAmE;IAEvGT,EAFuG,CAAAkB,YAAA,EAAO,EACpG,EACJ;;;;IAN2BlB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAoB,iBAAA,CAAAgB,OAAA,CAAAC,KAAA,CAAgB;IACjBrC,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAoB,iBAAA,CAAAgB,OAAA,CAAAV,IAAA,CAAe;IAGX1B,EAAA,CAAAmB,SAAA,GAAmE;IAAnEnB,EAAA,CAAAsC,kBAAA,KAAAF,OAAA,CAAAG,aAAA,OAAAH,OAAA,CAAAI,UAAA,OAAAJ,OAAA,CAAAK,SAAA,KAAmE;;;;;IAXvGzC,EAFR,CAAAU,cAAA,cAA0D,cAC5B,SAClB;IAAAV,EAAA,CAAAS,MAAA,yBAAQ;IAAAT,EAAA,CAAAkB,YAAA,EAAK;IACjBlB,EAAA,CAAA0C,UAAA,IAAAC,0CAAA,qBAA0F;IAC9F3C,EAAA,CAAAkB,YAAA,EAAM;IAENlB,EAAA,CAAAU,cAAA,cAAwB;IACpBV,EAAA,CAAA0C,UAAA,IAAAE,uCAAA,kBAA2D;IAUnE5C,EADI,CAAAkB,YAAA,EAAM,EACJ;;;;IAdWlB,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAA0C,WAAA,CAAAC,MAAA,KAA4B;IAIG9C,EAAA,CAAAmB,SAAA,GAAiB;IAAjBnB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA4C,cAAA,CAAiB;;;;;;IA9DjE/C,EAFR,CAAAU,cAAA,aAA4C,aACZ,aACK;IAEzBV,EADA,CAAA0C,UAAA,IAAAM,gCAAA,iBAAqF,IAAAC,yCAAA,0BACzC;IAChDjD,EAAA,CAAAkB,YAAA,EAAM;IAEFlB,EADJ,CAAAU,cAAA,cAA0B,cACI;IAAAV,EAAA,CAAAS,MAAA,GAAe;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAC/ClB,EAAA,CAAAU,cAAA,cAA8B;IAAAV,EAAA,CAAAS,MAAA,GAAwB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAGxDlB,EADJ,CAAAU,cAAA,eAA2B,eACE;IAAAV,EAAA,CAAAS,MAAA,IAAsB;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IACrDlB,EAAA,CAAAU,cAAA,eAA2B;IAAAV,EAAA,CAAAS,MAAA,IAAgB;IAGvDT,EAHuD,CAAAkB,YAAA,EAAM,EAC/C,EACJ,EACJ;IACNlB,EAAA,CAAAU,cAAA,eAAmC;IAE/BV,EADA,CAAA0C,UAAA,KAAAQ,iCAAA,kBAA8F,KAAAC,oCAAA,qBACsB;IAExHnD,EAAA,CAAAkB,YAAA,EAAM;IAGFlB,EADJ,CAAAU,cAAA,eAAqF,gBAClD;IAAzBV,EAAA,CAAAW,UAAA,sBAAAyC,qDAAA;MAAApD,EAAA,CAAAa,aAAA,CAAAwC,GAAA;MAAA,MAAAlD,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAYb,MAAA,CAAAmD,SAAA,EAAW;IAAA,EAAC;IAC1BtD,EAAA,CAAAU,cAAA,iBAAyH;IAAxBV,EAAA,CAAAuD,gBAAA,2BAAAC,2DAAAC,MAAA;MAAAzD,EAAA,CAAAa,aAAA,CAAAwC,GAAA;MAAA,MAAAlD,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAAf,EAAA,CAAA0D,kBAAA,CAAAvD,MAAA,CAAAwD,SAAA,EAAAF,MAAA,MAAAtD,MAAA,CAAAwD,SAAA,GAAAF,MAAA;MAAA,OAAAzD,EAAA,CAAAgB,WAAA,CAAAyC,MAAA;IAAA,EAAuB;IAAxHzD,EAAA,CAAAkB,YAAA,EAAyH;IAErHlB,EADJ,CAAAU,cAAA,eAA8B,kBACyC;IAA1BV,EAAA,CAAAW,UAAA,mBAAAiD,oDAAA;MAAA5D,EAAA,CAAAa,aAAA,CAAAwC,GAAA;MAAA,MAAAlD,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAc,aAAA,EAAe;IAAA,EAAC;IAACjB,EAAA,CAAAS,MAAA,cAAM;IAAAT,EAAA,CAAAkB,YAAA,EAAS;IAClFlB,EAAA,CAAAU,cAAA,kBAAuC;IAAAV,EAAA,CAAAS,MAAA,YAAI;IAGvDT,EAHuD,CAAAkB,YAAA,EAAS,EAClD,EACH,EACL;IAGFlB,EADJ,CAAAU,cAAA,eAAwB,UAChB;IAAAV,EAAA,CAAAS,MAAA,mBAAW;IAAAT,EAAA,CAAAkB,YAAA,EAAK;IAEpBlB,EAAA,CAAA0C,UAAA,KAAAmB,iCAAA,mBAA+D;IAe3D7D,EADJ,CAAAU,cAAA,eAA6B,eACI;IAAAV,EAAA,CAAAS,MAAA,IAA2B;IAAAT,EAAA,CAAAkB,YAAA,EAAM;IAC9DlB,EAAA,CAAAU,cAAA,eAAqC;IACjCV,EAAA,CAAAS,MAAA,yDACJ;IACJT,EADI,CAAAkB,YAAA,EAAM,EACJ;IAGNlB,EAAA,CAAA0C,UAAA,KAAAoB,iCAAA,kBAA0D;IAqBlD9D,EAFR,CAAAU,cAAA,eAA8B,aACmC,gBACxB;IAAAV,EAAA,CAAAS,MAAA,oBAAE;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IAAClB,EAAA,CAAAS,MAAA,qBAC/C;IAAAT,EAAA,CAAAkB,YAAA,EAAI;IACJlB,EAAA,CAAAU,cAAA,aAAgE;IAAAV,EAAA,CAAAS,MAAA,6BAAW;IAGvFT,EAHuF,CAAAkB,YAAA,EAAI,EAC7E,EACJ,EACJ;;;;IAhFYlB,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,CAA0B;IACjBL,EAAA,CAAAmB,SAAA,EAA2B;IAA3BnB,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAC,eAAA,CAA2B;IAGhBL,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAAsB,IAAA,CAAe;IACX1B,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAA8B,kBAAA,MAAA3B,MAAA,CAAAC,IAAA,CAAAG,QAAA,KAAwB;IAGzBP,EAAA,CAAAmB,SAAA,GAAsB;IAAtBnB,EAAA,CAAA8B,kBAAA,WAAA3B,MAAA,CAAAC,IAAA,CAAA2D,KAAA,KAAsB;IACpB/D,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAoB,iBAAA,CAAAjB,MAAA,CAAAC,IAAA,CAAA4D,KAAA,CAAgB;IAKzBhE,EAAA,CAAAmB,SAAA,GAAwC;IAAxCnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAAiB,GAAA,IAAAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAA4C,IAAA,UAAwC;IACzDjE,EAAA,CAAAmB,SAAA,EAAyC;IAAzCnB,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,IAAA,CAAAiB,GAAA,IAAAlB,MAAA,CAAAC,IAAA,CAAAiB,GAAA,CAAA4C,IAAA,UAAyC;IAIlBjE,EAAA,CAAAmB,SAAA,EAAgD;IAAhDnB,EAAA,CAAA2B,WAAA,YAAAxB,MAAA,CAAA+D,WAAA,oBAAgD;IAEqBlE,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAmE,gBAAA,YAAAhE,MAAA,CAAAwD,SAAA,CAAuB;IAW5E3D,EAAA,CAAAmB,SAAA,GAAa;IAAbnB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAiE,UAAA,CAAa;IAe5BpE,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA8B,kBAAA,iBAAA3B,MAAA,CAAAkE,SAAA,KAA2B;IAOhCrE,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAA0C,WAAA,CAAAC,MAAA,KAA4B;IAoBjD9C,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAsE,eAAA,KAAAC,GAAA,EAAApE,MAAA,CAAAC,IAAA,CAAAoE,EAAA,EAAmC;IAGnCxE,EAAA,CAAAmB,SAAA,GAAoC;IAApCnB,EAAA,CAAAE,UAAA,eAAAF,EAAA,CAAAyE,eAAA,KAAAC,GAAA,EAAoC;;;;;IAyB/B1E,EADJ,CAAAU,cAAA,cAAiE,eAC9B;IAAAV,EAAA,CAAAS,MAAA,GAAqC;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IAC3ElB,EAAA,CAAAU,cAAA,eAA8B;IAAAV,EAAA,CAAAS,MAAA,GAAqB;IACvDT,EADuD,CAAAkB,YAAA,EAAO,EACxD;;;;IAF6BlB,EAAA,CAAAmB,SAAA,GAAqC;IAArCnB,EAAA,CAAAoB,iBAAA,CAAAuD,YAAA,CAAAC,SAAA,uBAAqC;IACtC5E,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAoB,iBAAA,CAAAuD,YAAA,CAAAX,KAAA,CAAqB;;;;;IAHvDhE,EADJ,CAAAU,cAAA,cAA8E,SACtE;IAAAV,EAAA,CAAAS,MAAA,kBAAW;IAAAT,EAAA,CAAAkB,YAAA,EAAK;IACpBlB,EAAA,CAAA0C,UAAA,IAAAmC,4CAAA,kBAAiE;IAIrE7E,EAAA,CAAAkB,YAAA,EAAM;;;;IAJ2ClB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAE,UAAA,YAAA4E,QAAA,CAAAC,UAAA,CAAkB;;;;;IAR/D/E,EAFR,CAAAU,cAAA,cAA0D,cAC7B,eACI;IAAAV,EAAA,CAAAS,MAAA,GAAgB;IAAAT,EAAA,CAAAkB,YAAA,EAAO;IAChDlB,EAAA,CAAAU,cAAA,eAAwB;IAAAV,EAAA,CAAAS,MAAA,GAAe;IAC3CT,EAD2C,CAAAkB,YAAA,EAAO,EAC5C;IAEFlB,EADJ,CAAAU,cAAA,cAA2B,eACK;IAAAV,EAAA,CAAAS,MAAA,GAAmE;IACnGT,EADmG,CAAAkB,YAAA,EAAO,EACpG;IACNlB,EAAA,CAAA0C,UAAA,IAAAsC,sCAAA,kBAA8E;IAOlFhF,EAAA,CAAAkB,YAAA,EAAM;;;;IAb2BlB,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAoB,iBAAA,CAAA0D,QAAA,CAAAzC,KAAA,CAAgB;IACjBrC,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAAoB,iBAAA,CAAA0D,QAAA,CAAApD,IAAA,CAAe;IAGX1B,EAAA,CAAAmB,SAAA,GAAmE;IAAnEnB,EAAA,CAAAsC,kBAAA,KAAAwC,QAAA,CAAAvC,aAAA,OAAAuC,QAAA,CAAAtC,UAAA,OAAAsC,QAAA,CAAArC,SAAA,KAAmE;IAE1EzC,EAAA,CAAAmB,SAAA,EAAmD;IAAnDnB,EAAA,CAAAE,UAAA,SAAA4E,QAAA,CAAAC,UAAA,IAAAD,QAAA,CAAAC,UAAA,CAAAjC,MAAA,KAAmD;;;;;;IAf5F9C,EAAA,CAAAU,cAAA,cAAkF;IAA/BV,EAAA,CAAAW,UAAA,mBAAAsE,gDAAA;MAAAjF,EAAA,CAAAa,aAAA,CAAAqE,GAAA;MAAA,MAAA/E,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAgF,kBAAA,EAAoB;IAAA,EAAC;IAC7EnF,EAAA,CAAAU,cAAA,cAA8D;IAAnCV,EAAA,CAAAW,UAAA,mBAAAyE,gDAAA3B,MAAA;MAAAzD,EAAA,CAAAa,aAAA,CAAAqE,GAAA;MAAA,OAAAlF,EAAA,CAAAgB,WAAA,CAASyC,MAAA,CAAA4B,eAAA,EAAwB;IAAA,EAAC;IAErDrF,EADJ,CAAAU,cAAA,cAA0B,SAClB;IAAAV,EAAA,CAAAS,MAAA,6BAAY;IAAAT,EAAA,CAAAkB,YAAA,EAAK;IACrBlB,EAAA,CAAAU,cAAA,iBAAyD;IAA/BV,EAAA,CAAAW,UAAA,mBAAA2E,mDAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAqE,GAAA;MAAA,MAAA/E,MAAA,GAAAH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASb,MAAA,CAAAgF,kBAAA,EAAoB;IAAA,EAAC;IAACnF,EAAA,CAAAS,MAAA,aAAC;IAC9DT,EAD8D,CAAAkB,YAAA,EAAS,EACjE;IACNlB,EAAA,CAAAU,cAAA,cAAwB;IACpBV,EAAA,CAAA0C,UAAA,IAAA6C,gCAAA,mBAA0D;IAkBtEvF,EAFQ,CAAAkB,YAAA,EAAM,EACJ,EACJ;;;;IAlBgDlB,EAAA,CAAAmB,SAAA,GAAc;IAAdnB,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA0C,WAAA,CAAc;;;ADzExE,OAAM,MAAO2C,WAAW;EAuBtBC,YAAA;IAtBA;IACA,KAAArF,IAAI,GAAgB,IAAI;IACxB,KAAAsF,gBAAgB,GAAwB,IAAI;IAE5C;IACA,KAAAtB,UAAU,GAAsB,EAAE;IAClC,KAAAC,SAAS,GAAG,CAAC;IAEb;IACA,KAAAH,WAAW,GAAG,KAAK;IACnB,KAAAP,SAAS,GAAG,EAAE;IAEd;IACA,KAAAd,WAAW,GAAU,EAAE;IACvB,KAAA8C,iBAAiB,GAAG,KAAK;IAEjB,KAAAC,eAAe,GAAG5G,MAAM,CAACa,eAAe,CAAC;IACzC,KAAAgG,WAAW,GAAG7G,MAAM,CAACM,WAAW,CAAC;IACjC,KAAAwG,WAAW,GAAG9G,MAAM,CAACO,WAAW,CAAC;IACjC,KAAAwG,SAAS,GAAG/G,MAAM,CAACc,SAAS,CAAC;IAC7B,KAAAkG,YAAY,GAAGhH,MAAM,CAACC,eAAe,CAAC;EAE/B;EAEfgH,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,gBAAgB,GAAG,IAAI,CAACG,WAAW,CAACK,mBAAmB,CAACC,SAAS,CAACC,WAAW,IAAG;MAEnF,IAAIA,WAAW,EAAE;QAAA,IAAAC,WAAA,EAAAC,iBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,kBAAA;QACf;QACA,MAAMrG,IAAI,GAAGgG,WAA8B;QAE3C;QAEA;QACA,IAAI,CAAChG,IAAI,GAAG;UACV,GAAGA,IAAI;UACP2D,KAAK,GAAAsC,WAAA,GAAEjG,IAAI,CAAC2D,KAAK,cAAAsC,WAAA,cAAAA,WAAA,GAAI,CAAC;UAAG;UACzBK,WAAW,GAAAJ,iBAAA,GAAElG,IAAI,CAACsG,WAAW,cAAAJ,iBAAA,cAAAA,iBAAA,GAAI,CAAC;UAClCK,QAAQ,GAAAJ,cAAA,GAAEnG,IAAI,CAACuG,QAAQ,cAAAJ,cAAA,cAAAA,cAAA,GAAI,CAAC;UAC5BK,SAAS,GAAAJ,eAAA,GAAEpG,IAAI,CAACwG,SAAS,cAAAJ,eAAA,cAAAA,eAAA,GAAI,CAAC;UAC9BK,YAAY,GAAAJ,kBAAA,GAAErG,IAAI,CAACyG,YAAY,cAAAJ,kBAAA,cAAAA,kBAAA,GAAI,CAAC;UACpCpF,GAAG,EAAEjB,IAAI,CAACiB,GAAG,IAAI,EAAE;UACnB2C,KAAK,EAAE5D,IAAI,CAAC4D,KAAK,IAAI;SACtB;QAED,IAAI,CAACL,SAAS,GAAG,IAAI,CAACvD,IAAI,CAACiB,GAAG,IAAI,EAAE;QAIpC,IAAI,CAACyF,mBAAmB,EAAE;QAC1B,IAAI,CAACC,eAAe,EAAE;QAEtB;QACAC,UAAU,CAAC,MAAK;UAAA,IAAAC,UAAA;UACd,MAAMC,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;UAC1D,IAAIF,UAAU,KAAK,GAAAD,UAAA,GAAC,IAAI,CAAC7G,IAAI,cAAA6G,UAAA,eAATA,UAAA,CAAW5F,GAAG,KAAI,IAAI,CAACjB,IAAI,CAACiB,GAAG,CAAC4C,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;YAClEiD,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;UACnC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;QAEvD;QACA,IAAI,CAAC5B,eAAe,CAAC6B,YAAY,CAACC,IAAI,CACpCjI,SAAS,CAACkI,QAAQ,IAAG;UACnB,IAAI,CAACA,QAAQ,EAAE;YACb,OAAOnI,EAAE,CAAC,IAAI,CAAC;UACjB;UAGA;UACA,OAAO,IAAI,CAACqG,WAAW,CAAC+B,gBAAgB,CAACD,QAAQ,CAAC;QACpD,CAAC,CAAC,CACH,CAACxB,SAAS,EAAE;MACf;IACF,CAAC,CAAC;EACJ;EAEA0B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnC,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACoC,WAAW,EAAE;IACrC;EACF;EAEAC,gBAAgBA,CAAA;IAEd;IACA,IAAI,IAAI,CAAC/B,YAAY,CAACgC,SAAS,EAAE,EAAE;MAEjC;MACA,IAAI,CAACnC,WAAW,CAACoC,yBAAyB,EAAE,CAACC,IAAI,CAAC,MAAK,CACvD,CAAC,CAAC,CAACC,KAAK,CAACX,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC,MAAM,CACP;EACF;EAEMV,mBAAmBA,CAAA;IAAA,IAAAsB,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI,CAACD,KAAI,CAAChI,IAAI,EAAE;QACdmH,OAAO,CAACC,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;MAGA;MACA,IAAI,OAAOY,KAAI,CAAChI,IAAI,CAAC2D,KAAK,KAAK,QAAQ,EAAE;QACvCwD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEY,KAAI,CAAChI,IAAI,CAAC2D,KAAK,CAAC;QAC7DqE,KAAI,CAAChI,IAAI,CAAC2D,KAAK,GAAG,CAAC,CAAC,CAAC;MACvB;MAEA;MACAqE,KAAI,CAACrC,SAAS,CAACe,mBAAmB,CAACsB,KAAI,CAAChI,IAAI,EAAEL,UAAU,CAACuI,IAAI,CAAC,CAACnC,SAAS,CAACoC,MAAM,IAAG;QAChF,IAAIA,MAAM,EAAE;UACVH,KAAI,CAAChE,UAAU,GAAGmE,MAAM,CAACnE,UAAU;UACnCgE,KAAI,CAAC/D,SAAS,GAAGkE,MAAM,CAACC,UAAU;QACpC;MACF,CAAC,CAAC;IAAC;EACL;EAEAvH,aAAaA,CAAA;IACX,IAAI,CAACiD,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC;IACA,IAAI,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC9D,IAAI,EAAE;MACjC,IAAI,CAACuD,SAAS,GAAG,IAAI,CAACvD,IAAI,CAACiB,GAAG,IAAI,EAAE;IACtC;IAEA;IACA,IAAI,CAAC,IAAI,CAAC6C,WAAW,EAAE;MACrB8C,UAAU,CAAC,MAAK;QAAA,IAAAyB,WAAA,EAAAC,WAAA;QACd,MAAMxB,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC1D,IAAIF,UAAU,KAAK,GAAAuB,WAAA,GAAC,IAAI,CAACrI,IAAI,cAAAqI,WAAA,eAATA,WAAA,CAAWpH,GAAG,KAAI,EAAAqH,WAAA,OAAI,CAACtI,IAAI,cAAAsI,WAAA,uBAATA,WAAA,CAAWrH,GAAG,CAAC4C,IAAI,EAAE,MAAK,EAAE,CAAC,EAAE;UACnEiD,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;QACnC;MACF,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAhE,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAAClD,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACoE,EAAE,EAAE;IAEjC;IACA,MAAMnD,GAAG,GAAG,IAAI,CAACsC,SAAS,CAACM,IAAI,EAAE;IACjC,IAAI5C,GAAG,CAACyB,MAAM,GAAG,GAAG,EAAE;MACpB;MACA;IACF;IAEA;IACA,IAAI,CAAC+C,WAAW,CAAC8C,aAAa,CAAC,IAAI,CAACvI,IAAI,CAACoE,EAAE,EAAEnD,GAAG,CAAC,CAAC6G,IAAI,CAAC,MAAK;MAC1D,IAAI,IAAI,CAAC9H,IAAI,EAAE;QACb;QACA,IAAI,CAACA,IAAI,CAACiB,GAAG,GAAGA,GAAG,KAAK,EAAE,GAAG,EAAE,GAAGA,GAAG;MACvC;MACA,IAAI,CAAC6C,WAAW,GAAG,KAAK;MAExB;MACA8C,UAAU,CAAC,MAAK;QAAA,IAAA4B,WAAA;QACd,MAAM1B,UAAU,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC1D,IAAIF,UAAU,KAAK,GAAA0B,WAAA,GAAC,IAAI,CAACxI,IAAI,cAAAwI,WAAA,eAATA,WAAA,CAAWvH,GAAG,KAAI,IAAI,CAACjB,IAAI,CAACiB,GAAG,CAAC4C,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;UAClEiD,UAAU,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;QACnC;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC;EACJ;EAEAP,eAAeA,CAAA;IAAA,IAAA8B,WAAA;IACb,IAAI,GAAAA,WAAA,GAAC,IAAI,CAACzI,IAAI,cAAAyI,WAAA,eAATA,WAAA,CAAWrE,EAAE,GAAE;IAEpB,IAAI,CAACsB,WAAW,CAACgD,cAAc,CAAC,IAAI,CAAC1I,IAAI,CAACoE,EAAE,CAAC,CAACkD,IAAI,CAChDjI,SAAS,CAACsJ,KAAK,IAAG;MAChB,IAAIA,KAAK,CAACjG,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOtD,EAAE,CAAC,EAAE,CAAC;MACf;MAEA;MACA,MAAMwJ,eAAe,GAAGD,KAAK,CAACpJ,GAAG,CAACsJ,IAAI,IACpC,IAAI,CAACnD,WAAW,CAACoD,aAAa,CAACD,IAAI,CAACzE,EAAG,CAAC,CAACkD,IAAI,CAC3C/H,GAAG,CAACoF,UAAU,KAAK;QACjB,GAAGkE,IAAI;QACPlE;OACD,CAAC,CAAC,CACJ,CACF;MAED,OAAOrF,aAAa,CAACsJ,eAAe,CAAC;IACvC,CAAC,CAAC,CACH,CAAC7C,SAAS,CAAC;MACVgD,IAAI,EAAEC,mBAAmB,IAAG;QAC1B,IAAI,CAACvG,WAAW,GAAGuG,mBAAmB;MACxC,CAAC;MACD5B,KAAK,EAAEA,KAAK,IAAG;QACbD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEA,IAAIzE,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACF,WAAW,CAACwG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACrC;EAEAlH,iBAAiBA,CAAA;IACf,IAAI,CAACwD,iBAAiB,GAAG,IAAI;EAC/B;EAEAR,kBAAkBA,CAAA;IAChB,IAAI,CAACQ,iBAAiB,GAAG,KAAK;EAChC;;eAlNWH,WAAW;;mCAAXA,YAAW;AAAA;;QAAXA,YAAW;EAAA8D,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MC5BZ5J,EAFR,CAAAU,cAAA,aAAuB,aACX,aACc;MACdV,EAAA,CAAAC,SAAA,aAA6D;MAC7DD,EAAA,CAAAU,cAAA,WAAM;MAAAV,EAAA,CAAAS,MAAA,cAAO;MACjBT,EADiB,CAAAkB,YAAA,EAAO,EAClB;MACNlB,EAAA,CAAAU,cAAA,SAAI;MAAAV,EAAA,CAAAS,MAAA,cAAO;MACfT,EADe,CAAAkB,YAAA,EAAK,EACX;MAETlB,EAAA,CAAA0C,UAAA,IAAAoH,0BAAA,mBAA4C;MAoFhD9J,EAAA,CAAAkB,YAAA,EAAM;MAGNlB,EAAA,CAAA0C,UAAA,IAAAqH,0BAAA,iBAAkF;MA4BtF/J,EAAA,CAAAC,SAAA,sBAAiC;;;MAnHOD,EAAA,CAAAmB,SAAA,GAAU;MAAVnB,EAAA,CAAAE,UAAA,SAAA2J,GAAA,CAAAzJ,IAAA,CAAU;MAuFpBJ,EAAA,CAAAmB,SAAA,EAAuB;MAAvBnB,EAAA,CAAAE,UAAA,SAAA2J,GAAA,CAAAlE,iBAAA,CAAuB;;;iBDpEzCvG,WAAW,EAAA4K,EAAA,CAAAC,0BAAA,EAAE/K,YAAY,EAAAgL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjL,WAAW,EAAAkL,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,kBAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAN,EAAA,CAAAO,MAAA,EAAEvL,YAAY,EAAAwL,EAAA,CAAAC,UAAA,EAAElL,mBAAmB;EAAAmL,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}