:host {
    --background: #1c1c1e;
    --text: #ffffff;
    --text-muted: #8e8e93;
    --accent: #007aff;
    --card: #2c2c2e;
    --border: #3a3a3c;
    --success: #30d158;
    --danger: #ff453a;
    --radius: 12px;
}

.container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    background: var(--background);
    color: var(--text);
}

header {
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.back-btn {
    background: var(--card);
    border: none;
    border-radius: 8px;
    padding: 8px;
    color: var(--accent);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.back-btn:hover {
    background: var(--border);
}

h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    flex: 1;
    text-align: center;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.edit-btn, .delete-btn {
    background: var(--card);
    border: none;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.edit-btn {
    color: var(--accent);
}

.delete-btn {
    color: var(--danger);
}

.edit-btn:hover, .delete-btn:hover {
    background: var(--border);
}

.content {
    background: var(--card);
    border-radius: var(--radius);
    padding: 24px;
    margin-bottom: 100px;
}

.milestone-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border);
}

.milestone-badge {
    background: var(--accent);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
}

.entry-date {
    color: var(--text-muted);
    font-size: 14px;
}

.journal-content {
    line-height: 1.6;
    font-size: 16px;
    
    h1, h2, h3, h4, h5, h6 {
        margin: 24px 0 12px 0;
        font-weight: 600;
    }
    
    h3 {
        font-size: 20px;
        color: var(--text);
    }
    
    p {
        margin: 12px 0;
    }
    
    ul, ol {
        margin: 12px 0;
        padding-left: 24px;
    }
    
    li {
        margin: 6px 0;
    }
    
    strong {
        font-weight: 600;
    }
    
    em {
        font-style: italic;
    }
    
    u {
        text-decoration: underline;
    }
}

.edit-container {
    .toolbar {
        display: flex;
        gap: 16px;
        padding: 12px;
        background: var(--background);
        border-radius: 8px;
        margin-bottom: 16px;
        flex-wrap: wrap;
    }
    
    .toolbar-group {
        display: flex;
        gap: 4px;
        border-right: 1px solid var(--border);
        padding-right: 16px;
        
        &:last-child {
            border-right: none;
            padding-right: 0;
        }
    }
    
    .toolbar-btn {
        background: transparent;
        border: 1px solid var(--border);
        border-radius: 6px;
        padding: 8px 12px;
        color: var(--text);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        font-size: 14px;
        
        &:hover {
            background: var(--card);
        }
        
        &:active {
            background: var(--border);
        }
        
        ion-icon {
            font-size: 16px;
        }
    }
}

.rich-editor {
    min-height: 300px;
    padding: 16px;
    border: 1px solid var(--border);
    border-radius: 8px;
    background: var(--background);
    color: var(--text);
    font-size: 16px;
    line-height: 1.6;
    outline: none;
    margin-bottom: 16px;
    
    &:focus {
        border-color: var(--accent);
    }
    
    &[contenteditable]:empty::before {
        content: attr(placeholder);
        color: var(--text-muted);
        font-style: italic;
    }
    
    h1, h2, h3, h4, h5, h6 {
        margin: 16px 0 8px 0;
        font-weight: 600;
    }
    
    h3 {
        font-size: 20px;
    }
    
    p {
        margin: 8px 0;
    }
    
    ul, ol {
        margin: 8px 0;
        padding-left: 24px;
    }
    
    li {
        margin: 4px 0;
    }
}

.edit-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.cancel-btn, .save-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.cancel-btn {
    background: var(--card);
    color: var(--text);
    
    &:hover {
        background: var(--border);
    }
}

.save-btn {
    background: var(--accent);
    color: white;
    
    &:hover {
        background: #0056b3;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-muted);
    
    ion-spinner {
        margin-bottom: 16px;
    }
    
    p {
        margin: 0;
        font-size: 16px;
    }
}

// Mobile responsiveness
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .content {
        padding: 16px;
    }
    
    .toolbar {
        gap: 8px !important;
        
        .toolbar-group {
            gap: 2px !important;
            padding-right: 8px !important;
        }
        
        .toolbar-btn {
            min-width: 32px !important;
            height: 32px !important;
            padding: 6px 8px !important;
        }
    }
    
    .edit-actions {
        flex-direction: column;
        
        .cancel-btn, .save-btn {
            width: 100%;
        }
    }
}
