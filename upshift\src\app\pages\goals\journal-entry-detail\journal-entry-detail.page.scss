:host {
    --background: #1c1c1e;
    --text: #ffffff;
    --text-muted: #8e8e93;
    --accent: #007aff;
    --card: #2c2c2e;
    --border: #3a3a3c;
    --success: #30d158;
    --danger: #ff453a;
    --radius: 12px;
}

.container {
    width: 480px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    background: var(--background);
    color: var(--text);
}

header {
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.back-btn {
    background: var(--card);
    border: none;
    border-radius: 8px;
    padding: 8px;
    color: var(--accent);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.back-btn:hover {
    background: var(--border);
}

h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    flex: 1;
    text-align: center;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.edit-btn, .delete-btn {
    background: var(--card);
    border: none;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.edit-btn {
    color: var(--accent);
}

.delete-btn {
    color: var(--danger);
}

.edit-btn:hover, .delete-btn:hover {
    background: var(--border);
}

.content {
    background: var(--card);
    border-radius: var(--radius);
    padding: 24px;
    margin-bottom: 100px;
}

.milestone-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border);
}

.milestone-badge {
    background: var(--accent);
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 600;
}

.entry-date {
    color: var(--text-muted);
    font-size: 14px;
}

.journal-content {
    line-height: 1.6;
    font-size: 16px;

    h1, h2, h3, h4, h5, h6 {
        margin: 24px 0 12px 0;
        font-weight: 600;
    }

    h1 {
        font-size: 28px;
        color: var(--text);
    }

    h2 {
        font-size: 24px;
        color: var(--text);
    }

    h3 {
        font-size: 20px;
        color: var(--text);
    }

    p {
        margin: 12px 0;
    }

    ul, ol {
        margin: 12px 0;
        padding-left: 24px;
    }

    li {
        margin: 6px 0;
    }

    strong {
        font-weight: 600;
    }

    em {
        font-style: italic;
    }

    u {
        text-decoration: underline;
    }

    s {
        text-decoration: line-through;
        opacity: 0.7;
    }

    // Notion-like elements in view mode
    .checklist-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin: 8px 0;
        padding: 4px 0;

        .checklist-checkbox {
            margin-top: 2px;
            accent-color: var(--accent);
        }

        span {
            flex: 1;
        }
    }

    .notion-quote {
        border-left: 4px solid var(--accent);
        padding: 12px 16px;
        margin: 16px 0;
        background: rgba(0, 122, 255, 0.1);
        border-radius: 0 8px 8px 0;
        font-style: italic;
        color: var(--text-muted);
    }

    .notion-code {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 4px;
        padding: 2px 6px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: #ff6b6b;
    }

    .notion-code-block {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;

        code {
            background: transparent;
            border: none;
            padding: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #a8e6cf;
            white-space: pre;
            display: block;
        }
    }

    .notion-divider {
        border: none;
        height: 1px;
        background: var(--border);
        margin: 24px 0;
        opacity: 0.6;
    }
}

.edit-container {
    .toolbar {
        display: flex;
        gap: 12px;
        padding: 16px;
        background: var(--background);
        border-radius: 12px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        border: 1px solid var(--border);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .toolbar-group {
        display: flex;
        gap: 4px;
        border-right: 1px solid var(--border);
        padding-right: 12px;
        align-items: center;

        &:last-child {
            border-right: none;
            padding-right: 0;
        }
    }

    .toolbar-btn {
        background: transparent;
        border: 1px solid var(--border);
        border-radius: 8px;
        padding: 8px 12px;
        color: var(--text);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        font-size: 14px;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
            background: var(--card);
            border-color: var(--accent);
            transform: translateY(-1px);
        }

        &:active {
            background: var(--border);
            transform: translateY(0);
        }

        ion-icon {
            font-size: 16px;
        }

        &.dropdown-btn {
            gap: 4px;

            ion-icon {
                font-size: 12px;
            }
        }
    }

    // Dropdown styles
    .dropdown-container {
        position: relative;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 8px;
        padding: 8px;
        min-width: 160px;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        margin-top: 4px;

        button {
            width: 100%;
            background: transparent;
            border: none;
            padding: 8px 12px;
            color: var(--text);
            text-align: left;
            cursor: pointer;
            border-radius: 6px;
            font-size: 14px;
            transition: background-color 0.2s ease;

            &:hover {
                background: var(--background);
            }
        }
    }

    .color-menu {
        min-width: 200px;
        padding: 12px;
    }

    .color-section {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .color-label {
        display: block;
        font-size: 12px;
        color: var(--text-muted);
        margin-bottom: 8px;
        font-weight: 500;
    }

    .color-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
    }

    .color-btn {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid var(--border);
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
            transform: scale(1.1);
        }
    }
}

.rich-editor {
    min-height: 300px;
    padding: 16px;
    border: 1px solid var(--border);
    border-radius: 8px;
    background: var(--background);
    color: var(--text);
    font-size: 16px;
    line-height: 1.6;
    outline: none;
    margin-bottom: 16px;

    &:focus {
        border-color: var(--accent);
    }

    &[contenteditable]:empty::before {
        content: attr(placeholder);
        color: var(--text-muted);
        font-style: italic;
    }

    h1, h2, h3, h4, h5, h6 {
        margin: 16px 0 8px 0;
        font-weight: 600;
    }

    h3 {
        font-size: 20px;
    }

    p {
        margin: 8px 0;
    }

    ul, ol {
        margin: 8px 0;
        padding-left: 24px;
    }

    li {
        margin: 4px 0;
    }

    // Notion-like elements
    .checklist-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin: 8px 0;
        padding: 4px 0;

        .checklist-checkbox {
            margin-top: 2px;
            accent-color: var(--accent);
        }

        span {
            flex: 1;
            outline: none;

            &:focus {
                background: rgba(255, 255, 255, 0.05);
                border-radius: 4px;
                padding: 2px 4px;
            }
        }
    }

    .notion-quote {
        border-left: 4px solid var(--accent);
        padding: 12px 16px;
        margin: 16px 0;
        background: rgba(0, 122, 255, 0.1);
        border-radius: 0 8px 8px 0;
        font-style: italic;
        color: var(--text-muted);

        &:focus {
            outline: none;
            background: rgba(0, 122, 255, 0.15);
        }
    }

    .notion-code {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 4px;
        padding: 2px 6px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: #ff6b6b;
    }

    .notion-code-block {
        background: var(--card);
        border: 1px solid var(--border);
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;

        code {
            background: transparent;
            border: none;
            padding: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #a8e6cf;
            outline: none;
            white-space: pre;
            display: block;
            min-height: 20px;
        }
    }

    .notion-divider {
        border: none;
        height: 1px;
        background: var(--border);
        margin: 24px 0;
        opacity: 0.6;
    }
}

.edit-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.cancel-btn, .save-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.cancel-btn {
    background: var(--card);
    color: var(--text);

    &:hover {
        background: var(--border);
    }
}

.save-btn {
    background: var(--accent);
    color: white;

    &:hover {
        background: #0056b3;
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-muted);

    ion-spinner {
        margin-bottom: 16px;
    }

    p {
        margin: 0;
        font-size: 16px;
    }
}

// Mobile responsiveness
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .content {
        padding: 16px;
    }

    .toolbar {
        gap: 8px !important;

        .toolbar-group {
            gap: 2px !important;
            padding-right: 8px !important;
        }

        .toolbar-btn {
            min-width: 32px !important;
            height: 32px !important;
            padding: 6px 8px !important;
        }
    }

    .edit-actions {
        flex-direction: column;

        .cancel-btn, .save-btn {
            width: 100%;
        }
    }
}
