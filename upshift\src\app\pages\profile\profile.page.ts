import { Component, OnInit, inject } from '@angular/core';
import { IonRouterOutlet } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { UserService } from '../../services/user.service';
import { GoalService } from '../../services/goal.service';
import { User } from '../../models/user.model';
import { Goal, MicroGoal } from '../../models/supabase.models';
import { Subscription, of, switchMap, combineLatest, map } from 'rxjs';
import { NavigationComponent } from '../../components/navigation/navigation.component';
import { SupabaseService } from '../../services/supabase.service';
import { XpService, EntityType } from '../../services/xp.service';

interface CategoryDisplay {
  name: string;
  icon: string;
  color: string;
  current_xp: number;
  required_xp: number;
  progress: number;
}

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]
})
export class ProfilePage implements OnInit {
  // User data
  user: User | null = null;
  userSubscription: Subscription | null = null;

  // XP categories
  categories: CategoryDisplay[] = [];
  nextLevel = 0;

  // Bio form
  showBioForm = false;
  editedBio = '';

  // Goals
  publicGoals: any[] = [];
  showAllGoalsModal = false;

  private supabaseService = inject(SupabaseService);
  private userService = inject(UserService);
  private goalService = inject(GoalService);
  private xpService = inject(XpService);
  private routerOutlet = inject(IonRouterOutlet);

  constructor() {}

  ngOnInit() {
    // Subscribe to the current user profile from UserService
    this.userSubscription = this.userService.currentUserProfile$.subscribe(userProfile => {

      if (userProfile) {
        // Convert UserProfile to User
        const user = userProfile as unknown as User;

        // Store the original user data for reference

        // Ensure all required fields have default values
        this.user = {
          ...user,
          level: user.level ?? 1,  // Use nullish coalescing to handle 0 values correctly
          strength_xp: user.strength_xp ?? 0,
          money_xp: user.money_xp ?? 0,
          health_xp: user.health_xp ?? 0,
          knowledge_xp: user.knowledge_xp ?? 0,
          bio: user.bio || '',
          title: user.title || '🥚 Beginner'
        };

        this.editedBio = this.user.bio || '';



        this.calculateXpProgress();
        this.loadPublicGoals();

        // Ensure edit button visibility is correct on page load
        setTimeout(() => {
          const editBioBtn = document.getElementById('edit-bio-btn');
          if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
            editBioBtn.style.display = 'none';
          }
        }, 100);
      } else {
        console.error('Profile: No user profile data received');

        // If no profile data, try to get the auth user and ensure it exists
        this.supabaseService.currentUser$.pipe(
          switchMap(authUser => {
            if (!authUser) {
              return of(null);
            }


            // Use the ensureUserExists method to get or create the user
            return this.userService.ensureUserExists(authUser);
          })
        ).subscribe();
      }
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  ionViewWillEnter() {

    // Check if we're coming from another page (not initial load)
    if (this.routerOutlet.canGoBack()) {

      // Refresh the user profile
      this.userService.refreshCurrentUserProfile().then(() => {
      }).catch(error => {
        console.error('Profile: Error refreshing profile:', error);
      });
    } else {
    }
  }

  async calculateXpProgress() {
    if (!this.user) {
      console.error('Cannot calculate XP progress: user is null');
      return;
    }


    // Make sure user.level is defined and is a number
    if (typeof this.user.level !== 'number') {
      console.error('User level is not a number:', this.user.level);
      this.user.level = 1; // Default to level 1 if not set
    }

    // Use the XP service to calculate the progress
    this.xpService.calculateXpProgress(this.user, EntityType.USER).subscribe(result => {
      if (result) {
        this.categories = result.categories;
        this.nextLevel = result.next_level;
      }
    });
  }

  toggleBioForm() {
    this.showBioForm = !this.showBioForm;

    // Reset the edited bio to the current bio when opening the form
    if (this.showBioForm && this.user) {
      this.editedBio = this.user.bio || '';
    }

    // When closing the form, ensure edit button visibility is correct
    if (!this.showBioForm) {
      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user?.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    }
  }

  updateBio() {
    if (!this.user || !this.user.id) return;

    // Trim and validate bio
    const bio = this.editedBio.trim();
    if (bio.length > 100) {
      // Show error message
      return;
    }

    // Update user bio (will be set to empty string if only whitespace)
    this.userService.updateUserBio(this.user.id, bio).then(() => {
      if (this.user) {
        // If bio is empty, set it to empty string
        this.user.bio = bio === '' ? '' : bio;
      }
      this.showBioForm = false;

      // Force DOM update to ensure edit button visibility is correct
      setTimeout(() => {
        const editBioBtn = document.getElementById('edit-bio-btn');
        if (editBioBtn && (!this.user?.bio || this.user.bio.trim() === '')) {
          editBioBtn.style.display = 'none';
        }
      }, 0);
    });
  }

  loadPublicGoals() {
    if (!this.user?.id) return;

    this.goalService.getPublicGoals(this.user.id).pipe(
      switchMap(goals => {
        if (goals.length === 0) {
          return of([]);
        }

        // Get microgoals for each goal
        const goalObservables = goals.map(goal =>
          this.goalService.getMicroGoals(goal.id!).pipe(
            map(microgoals => ({
              ...goal,
              microgoals
            }))
          )
        );

        return combineLatest(goalObservables);
      })
    ).subscribe({
      next: goalsWithMicrogoals => {
        this.publicGoals = goalsWithMicrogoals;
      },
      error: error => {
        console.error('Error loading public goals:', error);
      }
    });
  }

  get displayedGoals() {
    return this.publicGoals.slice(0, 3);
  }

  openAllGoalsModal() {
    this.showAllGoalsModal = true;
  }

  closeAllGoalsModal() {
    this.showAllGoalsModal = false;
  }
}
