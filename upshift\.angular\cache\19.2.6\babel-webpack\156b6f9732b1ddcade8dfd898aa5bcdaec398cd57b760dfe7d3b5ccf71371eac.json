{"ast": null, "code": "var _GoalDetailPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/goals\"];\nfunction GoalDetailPage_section_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 11)(1, \"h2\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12)(4, \"p\", 13);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showDescriptionForm = true);\n    });\n    i0.ɵɵtext(7, \"Edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 15)(9, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateDescription());\n    });\n    i0.ɵɵelementStart(10, \"textarea\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_textarea_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedDescription, $event) || (ctx_r1.editedDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 18);\n    i0.ɵɵtext(12, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelEditDescription());\n    });\n    i0.ɵɵtext(14, \"Cancel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"div\", 21);\n    i0.ɵɵelement(17, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 23)(19, \"span\")(20, \"strong\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 24);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"form\", 25);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_25_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateProgress());\n    });\n    i0.ɵɵelementStart(26, \"label\", 26);\n    i0.ɵɵtext(27, \"Update value:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 27)(29, \"input\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.currentValue, $event) || (ctx_r1.currentValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 29);\n    i0.ɵɵtext(31, \"Save\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.goal.emoji, \" \", ctx_r1.goal.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showDescriptionForm ? \"none\" : \"flex\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showDescriptionForm ? \"block\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedDescription);\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.current_value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" / \", ctx_r1.goal.goal_value, \" \", ctx_r1.goal.goal_unit, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.currentValue);\n  }\n}\nfunction GoalDetailPage_section_12_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 36)(1, \"div\", 37)(2, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_2_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 41)(8, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_8_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵtext(9, \"\\u2716\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const microgoal_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"checked\", microgoal_r5.completed);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", microgoal_r5.completed ? \"\\u2714\" : \"\\u2610\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(microgoal_r5.title);\n  }\n}\nfunction GoalDetailPage_section_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 30)(1, \"h3\");\n    i0.ɵɵtext(2, \"Microgoals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 31);\n    i0.ɵɵtemplate(4, GoalDetailPage_section_12_li_4_Template, 10, 4, \"li\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 33);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_12_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addMicrogoal());\n    });\n    i0.ɵɵelementStart(6, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_12_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newMicrogoalTitle, $event) || (ctx_r1.newMicrogoalTitle = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 35);\n    i0.ɵɵtext(8, \"\\u2795 Add\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.microgoals);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newMicrogoalTitle);\n  }\n}\nfunction GoalDetailPage_section_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const entry_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83C\\uDFAF \", entry_r6.milestone_percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entry_r6.content);\n  }\n}\nfunction GoalDetailPage_section_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 43)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCDD Goal Journal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GoalDetailPage_section_13_div_3_Template, 5, 2, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.journalEntries);\n  }\n}\nfunction GoalDetailPage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83C\\uDF89 \");\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Congrats!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" You've reached \");\n    i0.ɵɵelementStart(6, \"span\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" of your goal.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"form\", 49);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_div_14_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addJournalEntry());\n    });\n    i0.ɵɵelementStart(10, \"textarea\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_div_14_Template_textarea_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.journalContent, $event) || (ctx_r1.journalContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 51);\n    i0.ɵɵtext(12, \"\\u270D\\uFE0F Add journal entry\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.nextMilestone, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.journalContent);\n  }\n}\nexport class GoalDetailPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // Goal data\n    this.goalId = null;\n    this.goal = null;\n    this.microgoals = [];\n    this.journalEntries = [];\n    // UI state\n    this.showDescriptionForm = false;\n    this.editedDescription = '';\n    this.currentValue = 0;\n    this.progressPercent = 0;\n    this.showJournalModal = false;\n    this.nextMilestone = null;\n    this.journalContent = '';\n    // New microgoal\n    this.newMicrogoalTitle = '';\n    this.supabaseService = inject(SupabaseService);\n    this.goalService = inject(GoalService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n  }\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(take(1)).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n  loadGoal() {\n    if (!this.goalId) return;\n    this.goalService.getGoal(this.goalId).pipe(take(1)).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n  loadMicrogoals() {\n    if (!this.goalId) return;\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n  loadJournalEntries() {\n    if (!this.goalId) return;\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n  calculateProgress() {\n    if (!this.goal) return;\n    this.progressPercent = this.goal.goal_value > 0 ? Math.min(100, Math.round(this.goal.current_value / this.goal.goal_value * 100)) : 0;\n  }\n  checkForMilestone() {\n    if (!this.goal) return;\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone => currentPercent >= milestone && !existingMilestones.includes(milestone));\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n  updateDescription() {\n    if (!this.goalId || !this.goal) return;\n    this.goalService.updateGoal(this.goalId, {\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.description = this.editedDescription;\n      }\n      this.showDescriptionForm = false;\n    });\n  }\n  cancelEditDescription() {\n    if (this.goal) {\n      this.editedDescription = this.goal.description;\n    }\n    this.showDescriptionForm = false;\n  }\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n  toggleMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n  deleteMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n    const newMicrogoal = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n    const newEntry = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n    this.goalService.createJournalEntry(newEntry).then(id => {\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n    const newPublicValue = !this.goal.public;\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        // Navigate back to the goal list\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('GoalDetailPage: Error deleting goal:', error);\n      });\n    }\n  }\n}\n_GoalDetailPage = GoalDetailPage;\n_GoalDetailPage.ɵfac = function GoalDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GoalDetailPage)();\n};\n_GoalDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GoalDetailPage,\n  selectors: [[\"app-goal-detail\"]],\n  decls: 19,\n  vars: 7,\n  consts: [[1, \"container\"], [1, \"logo-wrap\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"back-link\", 3, \"routerLink\"], [\"class\", \"goal-card\", 4, \"ngIf\"], [\"class\", \"microgoals-section\", 4, \"ngIf\"], [\"class\", \"journal-section\", 4, \"ngIf\"], [\"class\", \"journal-modal fancy\", 4, \"ngIf\"], [1, \"delete-goal-container\"], [\"type\", \"button\", 1, \"delete-goal-btn\", 3, \"click\"], [1, \"goal-card\"], [1, \"description-container\"], [1, \"goal-desc\"], [\"id\", \"edit-description-btn\", 1, \"edit-bio-btn\", 3, \"click\"], [\"id\", \"description-form\", 1, \"description-form\"], [3, \"ngSubmit\"], [\"name\", \"description\", \"maxlength\", \"500\", \"placeholder\", \"Enter goal description\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", \"name\", \"edit_description\", 1, \"save-btn\"], [\"type\", \"button\", \"id\", \"cancel-edit-btn\", 1, \"cancel-btn\", 3, \"click\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-info\"], [1, \"percent\"], [1, \"progress-update\", 3, \"ngSubmit\"], [\"for\", \"current_value\"], [1, \"save-bar\"], [\"type\", \"number\", \"name\", \"current_value\", \"step\", \"any\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"update_progress\", \"type\", \"submit\"], [1, \"microgoals-section\"], [1, \"microgoals-list\"], [\"class\", \"microgoal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"microgoal-add\", 3, \"ngSubmit\"], [\"name\", \"microgoal_title\", \"type\", \"text\", \"placeholder\", \"New microgoal...\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"add_microgoal\", \"type\", \"submit\"], [1, \"microgoal-item\"], [1, \"microgoal-form\"], [\"type\", \"button\", 1, \"micro-btn\", 3, \"click\"], [1, \"checkmark\"], [1, \"microgoal-title\"], [1, \"delete-form\"], [\"type\", \"button\", 1, \"delete-btn\", 3, \"click\"], [1, \"journal-section\"], [\"class\", \"journal-entry\", 4, \"ngFor\", \"ngForOf\"], [1, \"journal-entry\"], [1, \"milestone\"], [1, \"journal-modal\", \"fancy\"], [1, \"milestone-highlight\"], [1, \"inline-journal-form\", 3, \"ngSubmit\"], [\"name\", \"journal_content\", \"rows\", \"4\", \"placeholder\", \"Write how you're progressing...\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", \"name\", \"add_journal\"]],\n  template: function GoalDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1)(3, \"div\", 2);\n      i0.ɵɵelement(4, \"img\", 3);\n      i0.ɵɵelementStart(5, \"span\");\n      i0.ɵɵtext(6, \"Upshift\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(7, \"h1\");\n      i0.ɵɵtext(8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"a\", 4);\n      i0.ɵɵtext(10, \"\\u2190 Back to goals\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, GoalDetailPage_section_11_Template, 32, 15, \"section\", 5)(12, GoalDetailPage_section_12_Template, 9, 2, \"section\", 6)(13, GoalDetailPage_section_13_Template, 4, 1, \"section\", 7)(14, GoalDetailPage_div_14_Template, 13, 2, \"div\", 8);\n      i0.ɵɵelementStart(15, \"div\", 9)(16, \"button\", 10);\n      i0.ɵɵlistener(\"click\", function GoalDetailPage_Template_button_click_16_listener() {\n        return ctx.confirmDeleteGoal();\n      });\n      i0.ɵɵtext(17, \"Remove Goal\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(18, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵtextInterpolate(ctx.goal == null ? null : ctx.goal.name);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c0));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.journalEntries.length > 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showJournalModal && ctx.nextMilestone);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent],\n  styles: [\"[_nghost-%COMP%] {\\n  --bg: #0c0c0f;\\n  --card: #121217;\\n  --pill: #1c1c1e;\\n  --text: #fff;\\n  --text-muted: #8e8e93;\\n  --accent: #4d7bff;\\n  --radius: 16px;\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\nh1[_ngcontent-%COMP%], .page-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nconti[_ngcontent-%COMP%]   body.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  padding: 20px;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n  margin-top: 10px;\\n}\\n\\n.description-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-desc[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 10px;\\n  flex: 1;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--accent);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 2px 5px;\\n  margin-left: 10px;\\n}\\n\\n.description-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.description-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n}\\n\\n.save-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-right: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #2c2c2e;\\n  color: white;\\n  border: none;\\n}\\n\\n.delete-goal-container[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  text-align: center;\\n}\\n\\n.delete-goal-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 59, 48, 0.2);\\n  color: #FF3B30;\\n  border: 1px solid #FF3B30;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n}\\n\\n.progress-update[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-top: 10px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  font-weight: 600;\\n  border: none;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  cursor: pointer;\\n  margin-top: 10px;\\n}\\n\\n.microgoals-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.microgoals-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding-left: 0;\\n  margin-bottom: 12px;\\n  margin-top: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 6px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 8px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.journal-section[_ngcontent-%COMP%] {\\n  background: var(--pill);\\n  padding: 16px;\\n  border-radius: var(--radius);\\n}\\n\\n.journal-entry[_ngcontent-%COMP%] {\\n  border-top: 1px solid #2c2c2e;\\n  padding-top: 10px;\\n  margin-top: 10px;\\n}\\n\\n.journal-entry[_ngcontent-%COMP%]:first-child {\\n  border-top: none;\\n  padding-top: 0;\\n  margin-top: 0;\\n}\\n\\n.milestone[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--accent);\\n}\\n\\n.journal-modal[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: var(--card);\\n  padding: 14px;\\n  border-radius: var(--radius);\\n  text-align: center;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-top: 10px;\\n  color: var(--accent);\\n  text-decoration: underline;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n  padding: 20px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.top-date[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-muted);\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.goal-percent[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--accent);\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 12px;\\n}\\n\\n.goal-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--accent);\\n  text-decoration: none;\\n  display: inline-block;\\n}\\n\\n.goal-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.no-goals[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-muted);\\n  margin-top: 40px;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #ff4d4d;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  font-size: 16px;\\n  vertical-align: middle;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  color: red;\\n}\\n\\n.microgoal-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: var(--pill);\\n  padding: 10px 14px;\\n  margin-bottom: 8px;\\n  border-radius: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  flex: 1;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 18px;\\n  color: var(--accent);\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text-muted);\\n}\\n\\n.checkmark.checked[_ngcontent-%COMP%] {\\n  color: #4cd964;\\n}\\n\\n.microgoal-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: var(--text);\\n}\\n\\n.delete-form[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  color: #ff4d6d;\\n  cursor: pointer;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border-radius: 16px;\\n  padding: 20px;\\n  margin-top: 24px;\\n  text-align: center;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin-bottom: 12px;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  text-decoration: underline;\\n  color: var(--accent);\\n}\\n\\n.milestone-highlight[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 600;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 10px;\\n  padding: 10px;\\n  color: var(--text);\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background-color: var(--accent);\\n  border: none;\\n  padding: 10px 16px;\\n  color: white;\\n  font-weight: bold;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  transition: opacity 0.2s ease;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n\\n.save-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 0 16px 16px 0;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-radius: 16px 0 0 16px;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: inherit;\\n  display: block;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%]:hover   .goal-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 1px var(--accent-color);\\n  transition: box-shadow 0.2s;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.logo-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n}\\n\\n.back-link[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  text-decoration: none;\\n  margin-left: 2px;\\n  transition: color 0.2s ease;\\n  margin-bottom: 10px;\\n}\\n\\n.back-link[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n\\n.goal-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 1;\\n}\\n\\n.goal-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  width: 95%;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "ActivatedRoute", "Router", "GoalService", "take", "NavigationComponent", "SupabaseService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GoalDetailPage_section_11_Template_button_click_6_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showDescriptionForm", "GoalDetailPage_section_11_Template_form_ngSubmit_9_listener", "updateDescription", "ɵɵtwoWayListener", "GoalDetailPage_section_11_Template_textarea_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "editedDescription", "GoalDetailPage_section_11_Template_button_click_13_listener", "cancelEditDescription", "ɵɵelement", "GoalDetailPage_section_11_Template_form_ngSubmit_25_listener", "updateProgress", "GoalDetailPage_section_11_Template_input_ngModelChange_29_listener", "currentValue", "ɵɵadvance", "ɵɵtextInterpolate2", "goal", "emoji", "name", "ɵɵstyleProp", "ɵɵtextInterpolate", "description", "ɵɵtwoWayProperty", "progressPercent", "current_value", "goal_value", "goal_unit", "ɵɵtextInterpolate1", "GoalDetailPage_section_12_li_4_Template_button_click_2_listener", "microgoal_r5", "_r4", "$implicit", "toggleMicrogoal", "GoalDetailPage_section_12_li_4_Template_button_click_8_listener", "deleteMicrogoal", "ɵɵclassProp", "completed", "title", "ɵɵtemplate", "GoalDetailPage_section_12_li_4_Template", "GoalDetailPage_section_12_Template_form_ngSubmit_5_listener", "_r3", "addMicrogoal", "GoalDetailPage_section_12_Template_input_ngModelChange_6_listener", "newMicrogoalTitle", "ɵɵproperty", "microgoals", "entry_r6", "milestone_percentage", "content", "GoalDetailPage_section_13_div_3_Template", "journalEntries", "GoalDetailPage_div_14_Template_form_ngSubmit_9_listener", "_r7", "addJournalEntry", "GoalDetailPage_div_14_Template_textarea_ngModelChange_10_listener", "journalContent", "nextMilestone", "GoalDetailPage", "constructor", "userId", "goalId", "showJournalModal", "supabaseService", "goalService", "route", "router", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "id", "paramMap", "params", "get", "loadGoal", "loadMicrogoals", "loadJournalEntries", "getGoal", "calculateProgress", "checkForMilestone", "console", "error", "getMicroGoals", "getJournalEntries", "entries", "Math", "min", "round", "currentPercent", "milestones", "existingMilestones", "map", "entry", "reachedMilestones", "filter", "milestone", "includes", "length", "updateGoal", "then", "catch", "microgoal", "toggleMicroGoalCompletion", "completed_at", "Date", "undefined", "deleteMicroGoal", "m", "trim", "newMicrogoal", "goal_id", "createMicroGoal", "push", "newEntry", "createJournalEntry", "togglePublic", "newPublicValue", "public", "confirmDeleteGoal", "confirm", "deleteGoal", "navigate", "selectors", "decls", "vars", "consts", "template", "GoalDetailPage_Template", "rf", "ctx", "GoalDetailPage_section_11_Template", "GoalDetailPage_section_12_Template", "GoalDetailPage_section_13_Template", "GoalDetailPage_div_14_Template", "GoalDetailPage_Template_button_click_16_listener", "ɵɵpureFunction0", "_c0", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal, GoalJournalEntry, MicroGoal } from '../../../models/goal.model';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\n\n@Component({\n  selector: 'app-goal-detail',\n  templateUrl: './goal-detail.page.html',\n  styleUrls: ['./goal-detail.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]\n})\nexport class GoalDetailPage implements OnInit {\n  // User data\n  userId: string | null = null;\n\n  // Goal data\n  goalId: string | null = null;\n  goal: Goal | null = null;\n  microgoals: MicroGoal[] = [];\n  journalEntries: GoalJournalEntry[] = [];\n\n  // UI state\n  showDescriptionForm = false;\n  editedDescription = '';\n  currentValue = 0;\n  progressPercent = 0;\n  showJournalModal = false;\n  nextMilestone: number | null = null;\n  journalContent = '';\n\n  // New microgoal\n  newMicrogoalTitle = '';\n\n  private supabaseService = inject(SupabaseService);\n  private goalService = inject(GoalService);\n  private route = inject(ActivatedRoute);\n  private router = inject(Router);\n\n  constructor() {}\n\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(\n          take(1)\n        ).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n\n  loadGoal() {\n    if (!this.goalId) return;\n\n    this.goalService.getGoal(this.goalId).pipe(\n      take(1)\n    ).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n\n  loadMicrogoals() {\n    if (!this.goalId) return;\n\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n\n  loadJournalEntries() {\n    if (!this.goalId) return;\n\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n\n  calculateProgress() {\n    if (!this.goal) return;\n\n    this.progressPercent = this.goal.goal_value > 0\n      ? Math.min(100, Math.round((this.goal.current_value / this.goal.goal_value) * 100))\n      : 0;\n  }\n\n  checkForMilestone() {\n    if (!this.goal) return;\n\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone =>\n      currentPercent >= milestone &&\n      !existingMilestones.includes(milestone)\n    );\n\n\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n\n  updateDescription() {\n    if (!this.goalId || !this.goal) return;\n\n    this.goalService.updateGoal(this.goalId, {\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.description = this.editedDescription;\n      }\n      this.showDescriptionForm = false;\n    });\n  }\n\n  cancelEditDescription() {\n    if (this.goal) {\n      this.editedDescription = this.goal.description;\n    }\n    this.showDescriptionForm = false;\n  }\n\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n\n\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n\n  toggleMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n\n  deleteMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n\n    const newMicrogoal: Omit<MicroGoal, 'id'> = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n\n    const newEntry: Omit<GoalJournalEntry, 'id' | 'created_at'> = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n\n    this.goalService.createJournalEntry(newEntry).then(id => {\n\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n\n    const newPublicValue = !this.goal.public;\n\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        // Navigate back to the goal list\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('GoalDetailPage: Error deleting goal:', error);\n      });\n    }\n  }\n}\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo-wrap\">\r\n            <div class=\"logo\">\r\n                <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n                <span>Upshift</span>\r\n            </div>\r\n        </div>\r\n        <h1>{{ goal?.name }}</h1>\r\n    </header>\r\n    <a [routerLink]=\"['/goals']\" class=\"back-link\">&larr; Back to goals</a>\r\n\r\n    <section class=\"goal-card\" *ngIf=\"goal\">\r\n        <h2>{{goal.emoji}} {{ goal.name }}</h2>\r\n        <div class=\"description-container\" [style.display]=\"showDescriptionForm ? 'none' : 'flex'\">\r\n            <p class=\"goal-desc\">{{ goal.description }}</p>\r\n            <button id=\"edit-description-btn\" class=\"edit-bio-btn\" (click)=\"showDescriptionForm = true\">Edit</button>\r\n        </div>\r\n\r\n        <div id=\"description-form\" class=\"description-form\" [style.display]=\"showDescriptionForm ? 'block' : 'none'\">\r\n            <form (ngSubmit)=\"updateDescription()\">\r\n                <textarea name=\"description\" maxlength=\"500\" placeholder=\"Enter goal description\" [(ngModel)]=\"editedDescription\"></textarea>\r\n                <button type=\"submit\" name=\"edit_description\" class=\"save-btn\">Save</button>\r\n                <button type=\"button\" id=\"cancel-edit-btn\" class=\"cancel-btn\" (click)=\"cancelEditDescription()\">Cancel</button>\r\n            </form>\r\n        </div>\r\n\r\n        <div class=\"progress-container\">\r\n            <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" [style.width.%]=\"progressPercent\"></div>\r\n            </div>\r\n            <div class=\"progress-info\">\r\n                <span><strong>{{ goal.current_value }}</strong> / {{ goal.goal_value }} {{ goal.goal_unit }}</span>\r\n                <span class=\"percent\">{{ progressPercent }}%</span>\r\n            </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"updateProgress()\" class=\"progress-update\">\r\n            <label for=\"current_value\">Update value:</label>\r\n            <div class=\"save-bar\">\r\n                <input type=\"number\" name=\"current_value\" step=\"any\" [(ngModel)]=\"currentValue\">\r\n                <button name=\"update_progress\" type=\"submit\">Save</button>\r\n            </div>\r\n        </form>\r\n    </section>\r\n\r\n    <section class=\"microgoals-section\" *ngIf=\"goal\">\r\n        <h3>Microgoals</h3>\r\n        <ul class=\"microgoals-list\">\r\n            <li class=\"microgoal-item\" *ngFor=\"let microgoal of microgoals\">\r\n                <div class=\"microgoal-form\">\r\n                    <button class=\"micro-btn\" type=\"button\" (click)=\"toggleMicrogoal(microgoal)\">\r\n                        <span class=\"checkmark\" [class.checked]=\"microgoal.completed\">\r\n                            {{ microgoal.completed ? '✔' : '☐' }}\r\n                        </span>\r\n                    </button>\r\n                    <span class=\"microgoal-title\">{{ microgoal.title }}</span>\r\n                </div>\r\n                <div class=\"delete-form\">\r\n                    <button class=\"delete-btn\" type=\"button\" (click)=\"deleteMicrogoal(microgoal)\">✖</button>\r\n                </div>\r\n            </li>\r\n        </ul>\r\n        <form (ngSubmit)=\"addMicrogoal()\" class=\"microgoal-add\">\r\n            <input name=\"microgoal_title\" type=\"text\" placeholder=\"New microgoal...\" [(ngModel)]=\"newMicrogoalTitle\">\r\n            <button name=\"add_microgoal\" type=\"submit\">➕ Add</button>\r\n        </form>\r\n    </section>\r\n\r\n    <section class=\"journal-section\" *ngIf=\"journalEntries.length > 0\">\r\n        <h3>📝 Goal Journal:</h3>\r\n        <div class=\"journal-entry\" *ngFor=\"let entry of journalEntries\">\r\n            <div class=\"milestone\">🎯 {{ entry.milestone_percentage }}%</div>\r\n            <p>{{ entry.content }}</p>\r\n        </div>\r\n    </section>\r\n\r\n    <div class=\"journal-modal fancy\" *ngIf=\"showJournalModal && nextMilestone\">\r\n        <p>🎉 <strong>Congrats!</strong> You've reached <span class=\"milestone-highlight\">{{ nextMilestone }}%</span> of your goal.</p>\r\n\r\n        <form (ngSubmit)=\"addJournalEntry()\" class=\"inline-journal-form\">\r\n            <textarea name=\"journal_content\" rows=\"4\" placeholder=\"Write how you're progressing...\" [(ngModel)]=\"journalContent\"></textarea>\r\n            <button type=\"submit\" name=\"add_journal\">✍️ Add journal entry</button>\r\n        </form>\r\n    </div>\r\n\r\n    <div class=\"delete-goal-container\">\r\n        <button type=\"button\" class=\"delete-goal-btn\" (click)=\"confirmDeleteGoal()\">Remove Goal</button>\r\n    </div>\r\n</div>\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACtE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,eAAe,QAAQ,oCAAoC;;;;;;;;;;ICK5DC,EADJ,CAAAC,cAAA,kBAAwC,SAChC;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EADJ,CAAAC,cAAA,cAA2F,YAClE;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,iBAA4F;IAArCD,EAAA,CAAAI,UAAA,mBAAAC,2DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,mBAAA,GAA+B,IAAI;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,WAAI;IACpGF,EADoG,CAAAG,YAAA,EAAS,EACvG;IAGFH,EADJ,CAAAC,cAAA,cAA6G,eAClE;IAAjCD,EAAA,CAAAI,UAAA,sBAAAQ,4DAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAK,iBAAA,EAAmB;IAAA,EAAC;IAClCb,EAAA,CAAAC,cAAA,oBAAkH;IAAhCD,EAAA,CAAAc,gBAAA,2BAAAC,sEAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAU,iBAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,iBAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAAChB,EAAA,CAAAG,YAAA,EAAW;IAC7HH,EAAA,CAAAC,cAAA,kBAA+D;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5EH,EAAA,CAAAC,cAAA,kBAAgG;IAAlCD,EAAA,CAAAI,UAAA,mBAAAe,4DAAA;MAAAnB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IAACpB,EAAA,CAAAE,MAAA,cAAM;IAE9GF,EAF8G,CAAAG,YAAA,EAAS,EAC5G,EACL;IAGFH,EADJ,CAAAC,cAAA,eAAgC,eACF;IACtBD,EAAA,CAAAqB,SAAA,eAAmE;IACvErB,EAAA,CAAAG,YAAA,EAAM;IAEIH,EADV,CAAAC,cAAA,eAA2B,YACjB,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnGH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAEpDF,EAFoD,CAAAG,YAAA,EAAO,EACjD,EACJ;IAENH,EAAA,CAAAC,cAAA,gBAA4D;IAAtDD,EAAA,CAAAI,UAAA,sBAAAkB,6DAAA;MAAAtB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAe,cAAA,EAAgB;IAAA,EAAC;IAC/BvB,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE5CH,EADJ,CAAAC,cAAA,eAAsB,iBAC8D;IAA3BD,EAAA,CAAAc,gBAAA,2BAAAU,mEAAAR,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAiB,YAAA,EAAAT,MAAA,MAAAR,MAAA,CAAAiB,YAAA,GAAAT,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA0B;IAA/EhB,EAAA,CAAAG,YAAA,EAAgF;IAChFH,EAAA,CAAAC,cAAA,kBAA6C;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAG7DF,EAH6D,CAAAG,YAAA,EAAS,EACxD,EACH,EACD;;;;IA/BFH,EAAA,CAAA0B,SAAA,GAA8B;IAA9B1B,EAAA,CAAA2B,kBAAA,KAAAnB,MAAA,CAAAoB,IAAA,CAAAC,KAAA,OAAArB,MAAA,CAAAoB,IAAA,CAAAE,IAAA,KAA8B;IACC9B,EAAA,CAAA0B,SAAA,EAAuD;IAAvD1B,EAAA,CAAA+B,WAAA,YAAAvB,MAAA,CAAAG,mBAAA,mBAAuD;IACjEX,EAAA,CAAA0B,SAAA,GAAsB;IAAtB1B,EAAA,CAAAgC,iBAAA,CAAAxB,MAAA,CAAAoB,IAAA,CAAAK,WAAA,CAAsB;IAIKjC,EAAA,CAAA0B,SAAA,GAAwD;IAAxD1B,EAAA,CAAA+B,WAAA,YAAAvB,MAAA,CAAAG,mBAAA,oBAAwD;IAElBX,EAAA,CAAA0B,SAAA,GAA+B;IAA/B1B,EAAA,CAAAkC,gBAAA,YAAA1B,MAAA,CAAAU,iBAAA,CAA+B;IAQtFlB,EAAA,CAAA0B,SAAA,GAAiC;IAAjC1B,EAAA,CAAA+B,WAAA,UAAAvB,MAAA,CAAA2B,eAAA,MAAiC;IAG9CnC,EAAA,CAAA0B,SAAA,GAAwB;IAAxB1B,EAAA,CAAAgC,iBAAA,CAAAxB,MAAA,CAAAoB,IAAA,CAAAQ,aAAA,CAAwB;IAAUpC,EAAA,CAAA0B,SAAA,EAA4C;IAA5C1B,EAAA,CAAA2B,kBAAA,QAAAnB,MAAA,CAAAoB,IAAA,CAAAS,UAAA,OAAA7B,MAAA,CAAAoB,IAAA,CAAAU,SAAA,KAA4C;IACtEtC,EAAA,CAAA0B,SAAA,GAAsB;IAAtB1B,EAAA,CAAAuC,kBAAA,KAAA/B,MAAA,CAAA2B,eAAA,MAAsB;IAOSnC,EAAA,CAAA0B,SAAA,GAA0B;IAA1B1B,EAAA,CAAAkC,gBAAA,YAAA1B,MAAA,CAAAiB,YAAA,CAA0B;;;;;;IAW3EzB,EAFR,CAAAC,cAAA,aAAgE,cAChC,iBACqD;IAArCD,EAAA,CAAAI,UAAA,mBAAAoC,gEAAA;MAAA,MAAAC,YAAA,GAAAzC,EAAA,CAAAM,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoC,eAAA,CAAAH,YAAA,CAA0B;IAAA,EAAC;IACxEzC,EAAA,CAAAC,cAAA,eAA8D;IAC1DD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACF;IACTH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;IAEFH,EADJ,CAAAC,cAAA,cAAyB,iBACyD;IAArCD,EAAA,CAAAI,UAAA,mBAAAyC,gEAAA;MAAA,MAAAJ,YAAA,GAAAzC,EAAA,CAAAM,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsC,eAAA,CAAAL,YAAA,CAA0B;IAAA,EAAC;IAACzC,EAAA,CAAAE,MAAA,aAAC;IAEvFF,EAFuF,CAAAG,YAAA,EAAS,EACtF,EACL;;;;IAT+BH,EAAA,CAAA0B,SAAA,GAAqC;IAArC1B,EAAA,CAAA+C,WAAA,YAAAN,YAAA,CAAAO,SAAA,CAAqC;IACzDhD,EAAA,CAAA0B,SAAA,EACJ;IADI1B,EAAA,CAAAuC,kBAAA,MAAAE,YAAA,CAAAO,SAAA,4BACJ;IAE0BhD,EAAA,CAAA0B,SAAA,GAAqB;IAArB1B,EAAA,CAAAgC,iBAAA,CAAAS,YAAA,CAAAQ,KAAA,CAAqB;;;;;;IAT/DjD,EADJ,CAAAC,cAAA,kBAAiD,SACzC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,aAA4B;IACxBD,EAAA,CAAAkD,UAAA,IAAAC,uCAAA,kBAAgE;IAapEnD,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAwD;IAAlDD,EAAA,CAAAI,UAAA,sBAAAgD,4DAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAA8C,YAAA,EAAc;IAAA,EAAC;IAC7BtD,EAAA,CAAAC,cAAA,gBAAyG;IAAhCD,EAAA,CAAAc,gBAAA,2BAAAyC,kEAAAvC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAA+C,GAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAgD,iBAAA,EAAAxC,MAAA,MAAAR,MAAA,CAAAgD,iBAAA,GAAAxC,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAAxGhB,EAAA,CAAAG,YAAA,EAAyG;IACzGH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAExDF,EAFwD,CAAAG,YAAA,EAAS,EACtD,EACD;;;;IAlB+CH,EAAA,CAAA0B,SAAA,GAAa;IAAb1B,EAAA,CAAAyD,UAAA,YAAAjD,MAAA,CAAAkD,UAAA,CAAa;IAeW1D,EAAA,CAAA0B,SAAA,GAA+B;IAA/B1B,EAAA,CAAAkC,gBAAA,YAAA1B,MAAA,CAAAgD,iBAAA,CAA+B;;;;;IAQxGxD,EADJ,CAAAC,cAAA,cAAgE,cACrC;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC1BF,EAD0B,CAAAG,YAAA,EAAI,EACxB;;;;IAFqBH,EAAA,CAAA0B,SAAA,GAAoC;IAApC1B,EAAA,CAAAuC,kBAAA,kBAAAoB,QAAA,CAAAC,oBAAA,MAAoC;IACxD5D,EAAA,CAAA0B,SAAA,GAAmB;IAAnB1B,EAAA,CAAAgC,iBAAA,CAAA2B,QAAA,CAAAE,OAAA,CAAmB;;;;;IAH1B7D,EADJ,CAAAC,cAAA,kBAAmE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,iCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAkD,UAAA,IAAAY,wCAAA,kBAAgE;IAIpE9D,EAAA,CAAAG,YAAA,EAAU;;;;IAJuCH,EAAA,CAAA0B,SAAA,GAAiB;IAAjB1B,EAAA,CAAAyD,UAAA,YAAAjD,MAAA,CAAAuD,cAAA,CAAiB;;;;;;IAO9D/D,EADJ,CAAAC,cAAA,cAA2E,QACpE;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/HH,EAAA,CAAAC,cAAA,eAAiE;IAA3DD,EAAA,CAAAI,UAAA,sBAAA4D,wDAAA;MAAAhE,EAAA,CAAAM,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAA0D,eAAA,EAAiB;IAAA,EAAC;IAChClE,EAAA,CAAAC,cAAA,oBAAqH;IAA7BD,EAAA,CAAAc,gBAAA,2BAAAqD,kEAAAnD,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAA4D,cAAA,EAAApD,MAAA,MAAAR,MAAA,CAAA4D,cAAA,GAAApD,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA4B;IAAChB,EAAA,CAAAG,YAAA,EAAW;IAChIH,EAAA,CAAAC,cAAA,kBAAyC;IAAAD,EAAA,CAAAE,MAAA,sCAAoB;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EACnE,EACL;;;;IANgFH,EAAA,CAAA0B,SAAA,GAAoB;IAApB1B,EAAA,CAAAuC,kBAAA,KAAA/B,MAAA,CAAA6D,aAAA,MAAoB;IAGVrE,EAAA,CAAA0B,SAAA,GAA4B;IAA5B1B,EAAA,CAAAkC,gBAAA,YAAA1B,MAAA,CAAA4D,cAAA,CAA4B;;;ADhEhI,OAAM,MAAOE,cAAc;EA2BzBC,YAAA;IA1BA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAA7C,IAAI,GAAgB,IAAI;IACxB,KAAA8B,UAAU,GAAgB,EAAE;IAC5B,KAAAK,cAAc,GAAuB,EAAE;IAEvC;IACA,KAAApD,mBAAmB,GAAG,KAAK;IAC3B,KAAAO,iBAAiB,GAAG,EAAE;IACtB,KAAAO,YAAY,GAAG,CAAC;IAChB,KAAAU,eAAe,GAAG,CAAC;IACnB,KAAAuC,gBAAgB,GAAG,KAAK;IACxB,KAAAL,aAAa,GAAkB,IAAI;IACnC,KAAAD,cAAc,GAAG,EAAE;IAEnB;IACA,KAAAZ,iBAAiB,GAAG,EAAE;IAEd,KAAAmB,eAAe,GAAGtF,MAAM,CAACU,eAAe,CAAC;IACzC,KAAA6E,WAAW,GAAGvF,MAAM,CAACO,WAAW,CAAC;IACjC,KAAAiF,KAAK,GAAGxF,MAAM,CAACK,cAAc,CAAC;IAC9B,KAAAoF,MAAM,GAAGzF,MAAM,CAACM,MAAM,CAAC;EAEhB;EAEfoF,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,eAAe,CAACK,YAAY,CAACC,IAAI,CACpCpF,IAAI,CAAC,CAAC,CAAC,CACR,CAACqF,SAAS,CAACC,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACX,MAAM,GAAGW,IAAI,CAACC,EAAE;QAErB;QACA,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACJ,IAAI,CACtBpF,IAAI,CAAC,CAAC,CAAC,CACR,CAACqF,SAAS,CAACI,MAAM,IAAG;UACnB,IAAI,CAACb,MAAM,GAAGa,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;UAC9B,IAAI,IAAI,CAACd,MAAM,EAAE;YACf,IAAI,CAACe,QAAQ,EAAE;YACf,IAAI,CAACC,cAAc,EAAE;YACrB,IAAI,CAACC,kBAAkB,EAAE;UAC3B;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACe,OAAO,CAAC,IAAI,CAAClB,MAAM,CAAC,CAACQ,IAAI,CACxCpF,IAAI,CAAC,CAAC,CAAC,CACR,CAACqF,SAAS,CAACtD,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACV,iBAAiB,GAAGU,IAAI,CAACK,WAAW;QACzC,IAAI,CAACR,YAAY,GAAGG,IAAI,CAACQ,aAAa;QACtC,IAAI,CAACwD,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAACtB,MAAM,CAAC;MACvD;IACF,CAAC,CAAC;EACJ;EAEAgB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACoB,aAAa,CAAC,IAAI,CAACvB,MAAM,CAAC,CAACS,SAAS,CAACxB,UAAU,IAAG;MACjE,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC9B,CAAC,CAAC;EACJ;EAEAgC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACqB,iBAAiB,CAAC,IAAI,CAACxB,MAAM,CAAC,CAACS,SAAS,CAACgB,OAAO,IAAG;MAClE,IAAI,CAACnC,cAAc,GAAGmC,OAAO;MAC7B;MACA,IAAI,CAACL,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAChE,IAAI,EAAE;IAEhB,IAAI,CAACO,eAAe,GAAG,IAAI,CAACP,IAAI,CAACS,UAAU,GAAG,CAAC,GAC3C8D,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAE,IAAI,CAACzE,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACR,IAAI,CAACS,UAAU,GAAI,GAAG,CAAC,CAAC,GACjF,CAAC;EACP;EAEAwD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjE,IAAI,EAAE;IAEhB;IACA,MAAM0E,cAAc,GAAG,IAAI,CAACnE,eAAe;IAE3C;IACA,MAAMoE,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAExC;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACzC,cAAc,CAAC0C,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAC9C,oBAAoB,CAAC;IAEvF;IACA,MAAM+C,iBAAiB,GAAGJ,UAAU,CAACK,MAAM,CAACC,SAAS,IACnDP,cAAc,IAAIO,SAAS,IAC3B,CAACL,kBAAkB,CAACM,QAAQ,CAACD,SAAS,CAAC,CACxC;IAGD;IACA,IAAIF,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC1C,aAAa,GAAGsC,iBAAiB,CAAC,CAAC,CAAC;MACzC,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC9B,CAAC,MAAM;MACL,IAAI,CAACL,aAAa,GAAG,IAAI;MACzB,IAAI,CAACK,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEA7D,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC4D,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAEhC,IAAI,CAACgD,WAAW,CAACoC,UAAU,CAAC,IAAI,CAACvC,MAAM,EAAE;MACvCxC,WAAW,EAAE,IAAI,CAACf;KACnB,CAAC,CAAC+F,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAACrF,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACK,WAAW,GAAG,IAAI,CAACf,iBAAiB;MAChD;MACA,IAAI,CAACP,mBAAmB,GAAG,KAAK;IAClC,CAAC,CAAC;EACJ;EAEAS,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACQ,IAAI,EAAE;MACb,IAAI,CAACV,iBAAiB,GAAG,IAAI,CAACU,IAAI,CAACK,WAAW;IAChD;IACA,IAAI,CAACtB,mBAAmB,GAAG,KAAK;EAClC;EAEAY,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACkD,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAGhC,IAAI,CAACgD,WAAW,CAACoC,UAAU,CAAC,IAAI,CAACvC,MAAM,EAAE;MACvCrC,aAAa,EAAE,IAAI,CAACX;KACrB,CAAC,CAACwF,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAACrF,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACX,YAAY;QAC3C,IAAI,CAACmE,iBAAiB,EAAE;QAExB;QACA,IAAI,CAACF,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC,CAACwB,KAAK,CAACnB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAnD,eAAeA,CAACuE,SAAoB;IAClC,IAAI,CAACA,SAAS,CAAC/B,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAACwC,yBAAyB,CAACD,SAAS,CAAC/B,EAAE,CAAC,CAAC6B,IAAI,CAAC,MAAK;MACjE;MACAE,SAAS,CAACnE,SAAS,GAAG,CAACmE,SAAS,CAACnE,SAAS;MAC1CmE,SAAS,CAACE,YAAY,GAAGF,SAAS,CAACnE,SAAS,GAAG,IAAIsE,IAAI,EAAE,GAAGC,SAAS;IACvE,CAAC,CAAC;EACJ;EAEAzE,eAAeA,CAACqE,SAAoB;IAClC,IAAI,CAACA,SAAS,CAAC/B,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAAC4C,eAAe,CAACL,SAAS,CAAC/B,EAAE,CAAC,CAAC6B,IAAI,CAAC,MAAK;MACvD;MACA,IAAI,CAACvD,UAAU,GAAG,IAAI,CAACA,UAAU,CAACkD,MAAM,CAACa,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAK+B,SAAS,CAAC/B,EAAE,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA9B,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACmB,MAAM,IAAI,CAAC,IAAI,CAACjB,iBAAiB,CAACkE,IAAI,EAAE,EAAE;IAEpD,MAAMC,YAAY,GAA0B;MAC1CC,OAAO,EAAE,IAAI,CAACnD,MAAM;MACpBxB,KAAK,EAAE,IAAI,CAACO,iBAAiB,CAACkE,IAAI,EAAE;MACpC1E,SAAS,EAAE;KACZ;IAED,IAAI,CAAC4B,WAAW,CAACiD,eAAe,CAACF,YAAY,CAAC,CAACV,IAAI,CAAC7B,EAAE,IAAG;MACvD;MACA,IAAI,CAAC1B,UAAU,CAACoE,IAAI,CAAC;QACnB,GAAGH,YAAY;QACfvC;OACD,CAAC;MAEF;MACA,IAAI,CAAC5B,iBAAiB,GAAG,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAU,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACO,MAAM,IAAI,CAAC,IAAI,CAACJ,aAAa,IAAI,CAAC,IAAI,CAACD,cAAc,CAACsD,IAAI,EAAE,EAAE;IAExE,MAAMK,QAAQ,GAAgD;MAC5DH,OAAO,EAAE,IAAI,CAACnD,MAAM;MACpBb,oBAAoB,EAAE,IAAI,CAACS,aAAa;MACxCR,OAAO,EAAE,IAAI,CAACO,cAAc,CAACsD,IAAI;KAClC;IAED,IAAI,CAAC9C,WAAW,CAACoD,kBAAkB,CAACD,QAAQ,CAAC,CAACd,IAAI,CAAC7B,EAAE,IAAG;MAEtD;MACA,IAAI,CAACV,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACN,cAAc,GAAG,EAAE;MAExB;MACA,IAAI,CAACsB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,CAACwB,KAAK,CAACnB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,CAAC;EACJ;EAEAkC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACxD,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAEhC,MAAMsG,cAAc,GAAG,CAAC,IAAI,CAACtG,IAAI,CAACuG,MAAM;IAExC,IAAI,CAACvD,WAAW,CAACoC,UAAU,CAAC,IAAI,CAACvC,MAAM,EAAE;MACvC0D,MAAM,EAAED;KACT,CAAC,CAACjB,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAACrF,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACuG,MAAM,GAAGD,cAAc;MACnC;IACF,CAAC,CAAC,CAAChB,KAAK,CAACnB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D,CAAC,CAAC;EACJ;EAEAqC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC3D,MAAM,EAAE;IAElB,IAAI4D,OAAO,CAAC,0EAA0E,CAAC,EAAE;MACvF,IAAI,CAACzD,WAAW,CAAC0D,UAAU,CAAC,IAAI,CAAC7D,MAAM,CAAC,CAACwC,IAAI,CAAC,MAAK;QACjD,IAAI,CAACnC,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC,CAAC,CAACrB,KAAK,CAACnB,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;EAEAqC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC3D,MAAM,EAAE;IAElB,IAAI4D,OAAO,CAAC,0EAA0E,CAAC,EAAE;MACvF,IAAI,CAACzD,WAAW,CAAC0D,UAAU,CAAC,IAAI,CAAC7D,MAAM,CAAC,CAACwC,IAAI,CAAC,MAAK;QACjD;QACA,IAAI,CAACnC,MAAM,CAACyD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC,CAAC,CAACrB,KAAK,CAACnB,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC9D,CAAC,CAAC;IACJ;EACF;;kBAzQWzB,cAAc;;mCAAdA,eAAc;AAAA;;QAAdA,eAAc;EAAAkE,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCdf9I,EAHZ,CAAAC,cAAA,aAAuB,aACX,aACmB,aACD;MACdD,EAAA,CAAAqB,SAAA,aAA6D;MAC7DrB,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MAErBF,EAFqB,CAAAG,YAAA,EAAO,EAClB,EACJ;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,GAAgB;MACxBF,EADwB,CAAAG,YAAA,EAAK,EACpB;MACTH,EAAA,CAAAC,cAAA,WAA+C;MAAAD,EAAA,CAAAE,MAAA,4BAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAmEvEH,EAjEA,CAAAkD,UAAA,KAAA8F,kCAAA,uBAAwC,KAAAC,kCAAA,qBAkCS,KAAAC,kCAAA,qBAuBkB,KAAAC,8BAAA,kBAQQ;MAUvEnJ,EADJ,CAAAC,cAAA,cAAmC,kBAC6C;MAA9BD,EAAA,CAAAI,UAAA,mBAAAgJ,iDAAA;QAAA,OAASL,GAAA,CAAAX,iBAAA,EAAmB;MAAA,EAAC;MAACpI,EAAA,CAAAE,MAAA,mBAAW;MAE/FF,EAF+F,CAAAG,YAAA,EAAS,EAC9F,EACJ;MAGNH,EAAA,CAAAqB,SAAA,sBAAiC;;;MApFrBrB,EAAA,CAAA0B,SAAA,GAAgB;MAAhB1B,EAAA,CAAAgC,iBAAA,CAAA+G,GAAA,CAAAnH,IAAA,kBAAAmH,GAAA,CAAAnH,IAAA,CAAAE,IAAA,CAAgB;MAErB9B,EAAA,CAAA0B,SAAA,EAAyB;MAAzB1B,EAAA,CAAAyD,UAAA,eAAAzD,EAAA,CAAAqJ,eAAA,IAAAC,GAAA,EAAyB;MAEAtJ,EAAA,CAAA0B,SAAA,GAAU;MAAV1B,EAAA,CAAAyD,UAAA,SAAAsF,GAAA,CAAAnH,IAAA,CAAU;MAkCD5B,EAAA,CAAA0B,SAAA,EAAU;MAAV1B,EAAA,CAAAyD,UAAA,SAAAsF,GAAA,CAAAnH,IAAA,CAAU;MAuBb5B,EAAA,CAAA0B,SAAA,EAA+B;MAA/B1B,EAAA,CAAAyD,UAAA,SAAAsF,GAAA,CAAAhF,cAAA,CAAAgD,MAAA,KAA+B;MAQ/B/G,EAAA,CAAA0B,SAAA,EAAuC;MAAvC1B,EAAA,CAAAyD,UAAA,SAAAsF,GAAA,CAAArE,gBAAA,IAAAqE,GAAA,CAAA1E,aAAA,CAAuC;;;iBD9DjE7E,WAAW,EAAA+J,EAAA,CAAAC,0BAAA,EAAElK,YAAY,EAAAmK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEpK,WAAW,EAAAqK,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,mBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAE3K,YAAY,EAAA4K,EAAA,CAAAC,UAAA,EAAExK,mBAAmB;EAAAyK,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}