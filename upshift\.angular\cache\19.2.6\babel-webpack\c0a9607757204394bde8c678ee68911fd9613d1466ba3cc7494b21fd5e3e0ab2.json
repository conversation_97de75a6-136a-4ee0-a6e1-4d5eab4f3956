{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/Upshift/upshift/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _JournalEntryDetailPage;\nimport { IonicModule } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/goal.service\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = [\"richEditor\"];\nfunction JournalEntryDetailPage_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.startEditing());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 11);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JournalEntryDetailPage_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteEntry());\n    });\n    i0.ɵɵelement(1, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83C\\uDFAF \", ctx_r1.entry.milestone_percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 2, ctx_r1.entry.created_at, \"medium\"));\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.entry.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"p\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(2, \"Normal text\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h1\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(4, \"Heading 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h2\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(6, \"Heading 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h3\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(8, \"Heading 3\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h4\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(10, \"Heading 4\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.formatText(\"formatBlock\", \"h5\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵtext(12, \"Heading 5\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"span\", 64);\n    i0.ɵɵtext(3, \"Text Color\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 65)(5, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"default\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ff453a\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ff9500\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#ffcc02\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#30d158\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#007aff\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setTextColor(\"#af52de\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"span\", 64);\n    i0.ɵɵtext(14, \"Background\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 65)(16, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"default\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ff453a20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ff950020\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#ffcc0220\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#30d15820\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#007aff20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      ctx_r1.setBackgroundColor(\"#af52de20\");\n      return i0.ɵɵresetView(ctx_r1.closeDropdown());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction JournalEntryDetailPage_div_10_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"bold\"));\n    });\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"italic\"));\n    });\n    i0.ɵɵelementStart(7, \"em\");\n    i0.ɵɵtext(8, \"I\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"underline\"));\n    });\n    i0.ɵɵelementStart(10, \"u\");\n    i0.ɵɵtext(11, \"U\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"strikeThrough\"));\n    });\n    i0.ɵɵelementStart(13, \"s\");\n    i0.ɵɵtext(14, \"S\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 24)(16, \"div\", 29)(17, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(\"headings\"));\n    });\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"H\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"ion-icon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, JournalEntryDetailPage_div_10_div_3_div_21_Template, 13, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 24)(23, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertList(false));\n    });\n    i0.ɵɵelement(24, \"ion-icon\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertList(true));\n    });\n    i0.ɵɵelement(26, \"ion-icon\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertCheckList());\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 24)(30, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyLeft\"));\n    });\n    i0.ɵɵelementStart(31, \"span\", 40);\n    i0.ɵɵtext(32, \"\\u2B05\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyCenter\"));\n    });\n    i0.ɵɵelementStart(34, \"span\", 40);\n    i0.ɵɵtext(35, \"\\u2B0C\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"justifyRight\"));\n    });\n    i0.ɵɵelementStart(37, \"span\", 40);\n    i0.ɵɵtext(38, \"\\u27A1\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 24)(40, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertLink());\n    });\n    i0.ɵɵelement(41, \"ion-icon\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertCode());\n    });\n    i0.ɵɵelement(43, \"ion-icon\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.insertDivider());\n    });\n    i0.ɵɵelement(45, \"ion-icon\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 24)(47, \"div\", 29)(48, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleDropdown(\"colors\"));\n    });\n    i0.ɵɵelement(49, \"ion-icon\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, JournalEntryDetailPage_div_10_div_3_div_50_Template, 23, 0, \"div\", 51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 24)(52, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"undo\"));\n    });\n    i0.ɵɵelement(53, \"ion-icon\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.formatText(\"redo\"));\n    });\n    i0.ɵɵelement(55, \"ion-icon\", 55);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"div\", 56, 0);\n    i0.ɵɵlistener(\"input\", function JournalEntryDetailPage_div_10_div_3_Template_div_input_56_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentInput($event));\n    })(\"blur\", function JournalEntryDetailPage_div_10_div_3_Template_div_blur_56_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContentBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 57)(59, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cancelEditing());\n    });\n    i0.ɵɵtext(60, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_div_10_div_3_Template_button_click_61_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.saveEntry());\n    });\n    i0.ɵɵtext(62, \"Save\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeDropdown === \"headings\");\n    i0.ɵɵadvance(29);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.activeDropdown === \"colors\");\n  }\n}\nfunction JournalEntryDetailPage_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, JournalEntryDetailPage_div_10_div_1_Template, 6, 5, \"div\", 15)(2, JournalEntryDetailPage_div_10_div_2_Template, 1, 1, \"div\", 16)(3, JournalEntryDetailPage_div_10_div_3_Template, 63, 2, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.entry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isEditing && ctx_r1.entry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isEditing);\n  }\n}\nfunction JournalEntryDetailPage_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵelement(1, \"ion-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading journal entry...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class JournalEntryDetailPage {\n  constructor(route, router, goalService, toastController, sanitizer) {\n    this.route = route;\n    this.router = router;\n    this.goalService = goalService;\n    this.toastController = toastController;\n    this.sanitizer = sanitizer;\n    this.goalId = '';\n    this.entryId = '';\n    this.entry = null;\n    this.isEditing = false;\n    this.editContent = '';\n    this.isLoading = true;\n    this.activeDropdown = null;\n  }\n  ngOnInit() {\n    this.goalId = this.route.snapshot.paramMap.get('goalId') || '';\n    this.entryId = this.route.snapshot.paramMap.get('entryId') || '';\n    if (this.goalId && this.entryId) {\n      this.loadJournalEntry();\n    } else {\n      this.router.navigate(['/goals']);\n    }\n  }\n  ngAfterViewInit() {\n    // Initialize rich editor content after view is ready\n    if (this.isEditing && this.richEditor) {\n      this.richEditor.nativeElement.innerHTML = this.editContent;\n    }\n    // Add keyboard shortcuts\n    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    // Add click outside handler for dropdowns\n    document.addEventListener('click', this.handleClickOutside.bind(this));\n  }\n  ngOnDestroy() {\n    // Clean up event listeners\n    document.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    document.removeEventListener('click', this.handleClickOutside.bind(this));\n  }\n  loadJournalEntry() {\n    this.isLoading = true;\n    // Get all journal entries for the goal and find the specific one\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.entry = entries.find(e => e.id === this.entryId) || null;\n      this.isLoading = false;\n      if (!this.entry) {\n        this.showToast('Journal entry not found');\n        this.router.navigate(['/goals', this.goalId]);\n      }\n    });\n  }\n  startEditing() {\n    if (this.entry) {\n      this.editContent = this.entry.content;\n      this.isEditing = true;\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.richEditor) {\n          this.richEditor.nativeElement.innerHTML = this.editContent;\n        }\n      }, 0);\n    }\n  }\n  cancelEditing() {\n    this.isEditing = false;\n    this.editContent = '';\n  }\n  saveEntry() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.entry || !_this.editContent.trim()) return;\n      try {\n        yield _this.goalService.updateJournalEntry(_this.entry.id, {\n          content: _this.editContent.trim()\n        });\n        _this.entry.content = _this.editContent.trim();\n        _this.isEditing = false;\n        _this.editContent = '';\n        _this.showToast('Journal entry updated successfully');\n      } catch (error) {\n        console.error('Error updating journal entry:', error);\n        _this.showToast('Error updating journal entry');\n      }\n    })();\n  }\n  deleteEntry() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.entry) return;\n      const confirmed = confirm('Are you sure you want to delete this journal entry? This action cannot be undone.');\n      if (!confirmed) return;\n      try {\n        yield _this2.goalService.deleteJournalEntry(_this2.entry.id);\n        _this2.showToast('Journal entry deleted successfully');\n        _this2.router.navigate(['/goals', _this2.goalId]);\n      } catch (error) {\n        console.error('Error deleting journal entry:', error);\n        _this2.showToast('Error deleting journal entry');\n      }\n    })();\n  }\n  goBack() {\n    this.router.navigate(['/goals', this.goalId]);\n  }\n  showToast(message) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const toast = yield _this3.toastController.create({\n        message,\n        duration: 2000,\n        position: 'bottom'\n      });\n      toast.present();\n    })();\n  }\n  getSafeHtml(content) {\n    return this.sanitizer.bypassSecurityTrustHtml(content);\n  }\n  // Rich text formatting methods\n  formatText(command, value) {\n    document.execCommand(command, false, value);\n  }\n  insertList(ordered = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n  onContentInput(event) {\n    this.editContent = event.target.innerHTML;\n  }\n  onContentBlur(event) {\n    this.editContent = event.target.innerHTML;\n  }\n  // Dropdown management\n  toggleDropdown(dropdown) {\n    this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;\n  }\n  closeDropdown() {\n    this.activeDropdown = null;\n  }\n  // Advanced formatting methods\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n      range.deleteContents();\n      range.insertNode(checklistItem);\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n    }\n  }\n  insertLink() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      const url = prompt('Enter URL:', 'https://');\n      if (url && url.trim()) {\n        const link = document.createElement('a');\n        link.href = url.trim();\n        link.target = '_blank';\n        link.rel = 'noopener noreferrer';\n        link.className = 'notion-link';\n        link.textContent = selectedText || url.trim();\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(link);\n        // Move cursor after link\n        range.setStartAfter(link);\n        range.collapse(true);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    }\n  }\n  insertCode() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      if (selectedText) {\n        // Inline code\n        const code = document.createElement('code');\n        code.className = 'notion-code';\n        code.textContent = selectedText;\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(code);\n      } else {\n        // Code block\n        const range = selection.getRangeAt(0);\n        const codeBlock = document.createElement('pre');\n        codeBlock.className = 'notion-code-block';\n        codeBlock.innerHTML = '<code contenteditable=\"true\">// Your code here</code>';\n        range.deleteContents();\n        range.insertNode(codeBlock);\n        // Select the code content\n        const codeElement = codeBlock.querySelector('code');\n        if (codeElement) {\n          const newRange = document.createRange();\n          newRange.selectNodeContents(codeElement);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n        }\n      }\n    }\n  }\n  insertDivider() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const divider = document.createElement('hr');\n      divider.className = 'notion-divider';\n      range.deleteContents();\n      range.insertNode(divider);\n      // Move cursor after divider\n      range.setStartAfter(divider);\n      range.collapse(true);\n      selection.removeAllRanges();\n      selection.addRange(range);\n    }\n  }\n  setTextColor(color) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n      this.formatText('foreColor', '#ffffff');\n    } else {\n      this.formatText('foreColor', color);\n    }\n  }\n  setBackgroundColor(color) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n    } else {\n      this.formatText('backColor', color);\n    }\n  }\n  // Keyboard shortcuts\n  handleKeyboardShortcuts(event) {\n    if (!this.isEditing) return;\n    const isCtrl = event.ctrlKey || event.metaKey;\n    if (isCtrl) {\n      switch (event.key.toLowerCase()) {\n        case 'b':\n          event.preventDefault();\n          this.formatText('bold');\n          break;\n        case 'i':\n          event.preventDefault();\n          this.formatText('italic');\n          break;\n        case 'u':\n          event.preventDefault();\n          this.formatText('underline');\n          break;\n        case 'z':\n          event.preventDefault();\n          this.formatText('undo');\n          break;\n        case 'y':\n          event.preventDefault();\n          this.formatText('redo');\n          break;\n        case 's':\n          event.preventDefault();\n          this.saveEntry();\n          break;\n      }\n    }\n    // Escape to close dropdowns\n    if (event.key === 'Escape') {\n      this.closeDropdown();\n    }\n  }\n  // Click outside handler\n  handleClickOutside(event) {\n    const target = event.target;\n    if (!target.closest('.dropdown-container')) {\n      this.closeDropdown();\n    }\n  }\n}\n_JournalEntryDetailPage = JournalEntryDetailPage;\n_JournalEntryDetailPage.ɵfac = function JournalEntryDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _JournalEntryDetailPage)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.GoalService), i0.ɵɵdirectiveInject(i3.ToastController), i0.ɵɵdirectiveInject(i4.DomSanitizer));\n};\n_JournalEntryDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _JournalEntryDetailPage,\n  selectors: [[\"app-journal-entry-detail\"]],\n  viewQuery: function JournalEntryDetailPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.richEditor = _t.first);\n    }\n  },\n  decls: 13,\n  vars: 4,\n  consts: [[\"richEditor\", \"\"], [1, \"container\"], [1, \"header-content\"], [1, \"back-btn\", 3, \"click\"], [\"name\", \"arrow-back\"], [1, \"header-actions\"], [\"class\", \"edit-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"delete-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"content\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"edit-btn\", 3, \"click\"], [\"name\", \"create-outline\"], [1, \"delete-btn\", 3, \"click\"], [\"name\", \"trash-outline\"], [1, \"content\"], [\"class\", \"milestone-info\", 4, \"ngIf\"], [\"class\", \"journal-content\", 3, \"innerHTML\", 4, \"ngIf\"], [\"class\", \"edit-container\", 4, \"ngIf\"], [1, \"milestone-info\"], [1, \"milestone-badge\"], [1, \"entry-date\"], [1, \"journal-content\", 3, \"innerHTML\"], [1, \"edit-container\"], [1, \"toolbar\"], [1, \"toolbar-group\"], [\"type\", \"button\", \"title\", \"Bold (Ctrl+B)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Italic (Ctrl+I)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Underline (Ctrl+U)\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Strikethrough\", 1, \"toolbar-btn\", 3, \"click\"], [1, \"dropdown-container\"], [\"type\", \"button\", \"title\", \"Headings\", 1, \"toolbar-btn\", \"dropdown-btn\", 3, \"click\"], [\"name\", \"chevron-down-outline\"], [\"class\", \"dropdown-menu\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Bullet List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"list-outline\"], [\"type\", \"button\", \"title\", \"Numbered List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"list-circle-outline\"], [\"type\", \"button\", \"title\", \"To-do List\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"checkbox-outline\"], [\"type\", \"button\", \"title\", \"Align Left\", 1, \"toolbar-btn\", 3, \"click\"], [1, \"align-icon\"], [\"type\", \"button\", \"title\", \"Align Center\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Align Right\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Link\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"link-outline\"], [\"type\", \"button\", \"title\", \"Code\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"code-outline\"], [\"type\", \"button\", \"title\", \"Divider\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"remove-outline\"], [\"type\", \"button\", \"title\", \"Text Color\", 1, \"toolbar-btn\", \"dropdown-btn\", 3, \"click\"], [\"name\", \"color-palette-outline\"], [\"class\", \"dropdown-menu color-menu\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Undo (Ctrl+Z)\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"arrow-undo-outline\"], [\"type\", \"button\", \"title\", \"Redo (Ctrl+Y)\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [\"contenteditable\", \"true\", \"placeholder\", \"Write your journal entry here...\", 1, \"rich-editor\", 3, \"input\", \"blur\"], [1, \"edit-actions\"], [1, \"cancel-btn\", 3, \"click\"], [1, \"save-btn\", 3, \"click\"], [1, \"dropdown-menu\"], [\"type\", \"button\", 3, \"click\"], [1, \"dropdown-menu\", \"color-menu\"], [1, \"color-section\"], [1, \"color-label\"], [1, \"color-grid\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffffff\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff453a\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff9500\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffcc02\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#30d158\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#007aff\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#af52de\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"transparent\", \"border\", \"1px solid #666\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff453a20\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ff950020\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#ffcc0220\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#30d15820\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#007aff20\", 3, \"click\"], [\"type\", \"button\", 1, \"color-btn\", 2, \"background\", \"#af52de20\", 3, \"click\"], [1, \"loading-container\"]],\n  template: function JournalEntryDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\")(2, \"div\", 2)(3, \"button\", 3);\n      i0.ɵɵlistener(\"click\", function JournalEntryDetailPage_Template_button_click_3_listener() {\n        return ctx.goBack();\n      });\n      i0.ɵɵelement(4, \"ion-icon\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"h1\");\n      i0.ɵɵtext(6, \"Journal Entry\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵtemplate(8, JournalEntryDetailPage_button_8_Template, 2, 0, \"button\", 6)(9, JournalEntryDetailPage_button_9_Template, 2, 0, \"button\", 7);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(10, JournalEntryDetailPage_div_10_Template, 4, 3, \"div\", 8)(11, JournalEntryDetailPage_div_11_Template, 4, 0, \"div\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(12, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"ngIf\", !ctx.isEditing && ctx.entry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isEditing && ctx.entry);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n    }\n  },\n  dependencies: [IonicModule, i3.IonIcon, i3.IonSpinner, CommonModule, i5.NgIf, i5.DatePipe, FormsModule, NavigationComponent],\n  styles: [\"[_nghost-%COMP%] {\\n  --background: #1c1c1e;\\n  --text: #ffffff;\\n  --text-muted: #8e8e93;\\n  --accent: #007aff;\\n  --card: #2c2c2e;\\n  --border: #3a3a3c;\\n  --success: #30d158;\\n  --danger: #ff453a;\\n  --radius: 12px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  min-height: 100vh;\\n  background: var(--background);\\n  color: var(--text);\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  gap: 16px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: none;\\n  border-radius: 8px;\\n  padding: 8px;\\n  color: var(--accent);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n\\n.back-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n  flex: 1;\\n  text-align: center;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: none;\\n  border-radius: 8px;\\n  padding: 8px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 40px;\\n  height: 40px;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  color: var(--danger);\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover, .delete-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border-radius: var(--radius);\\n  padding: 24px;\\n  margin-bottom: 100px;\\n}\\n\\n.milestone-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n  padding-bottom: 16px;\\n  border-bottom: 1px solid var(--border);\\n}\\n\\n.milestone-badge[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 16px;\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.entry-date[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 14px;\\n}\\n\\n.journal-content[_ngcontent-%COMP%] {\\n  line-height: 1.6;\\n  font-size: 16px;\\n}\\n.journal-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 24px 0 12px 0;\\n  font-weight: 600;\\n}\\n.journal-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: var(--text);\\n}\\n.journal-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .journal-content[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 12px 0;\\n  padding-left: 24px;\\n}\\n.journal-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 6px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.journal-content[_ngcontent-%COMP%]   em[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.journal-content[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.journal-content[_ngcontent-%COMP%]   s[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n  opacity: 0.7;\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin: 8px 0;\\n  padding: 4px 0;\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   .checklist-checkbox[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n  accent-color: var(--accent);\\n}\\n.journal-content[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent);\\n  padding: 12px 16px;\\n  margin: 16px 0;\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 0 8px 8px 0;\\n  font-style: italic;\\n  color: var(--text-muted);\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 4px;\\n  padding: 2px 6px;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 13px;\\n  color: #ff6b6b;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n  overflow-x: auto;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 14px;\\n  color: #a8e6cf;\\n  white-space: pre;\\n  display: block;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-divider[_ngcontent-%COMP%] {\\n  border: none;\\n  height: 1px;\\n  background: var(--border);\\n  margin: 24px 0;\\n  opacity: 0.6;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-link[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  text-decoration: underline;\\n  cursor: pointer;\\n}\\n.journal-content[_ngcontent-%COMP%]   .notion-link[_ngcontent-%COMP%]:hover {\\n  opacity: 0.8;\\n}\\n\\n.edit-container[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 16px;\\n  background: var(--background);\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n  border: 1px solid var(--border);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  border-right: 1px solid var(--border);\\n  padding-right: 12px;\\n  align-items: center;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n  padding-right: 0;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 0;\\n  color: var(--text);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 36px;\\n  height: 36px;\\n  font-size: 14px;\\n  transition: all 0.2s ease;\\n  position: relative;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--card);\\n  border-color: var(--accent);\\n  transform: translateY(-1px);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:active {\\n  background: var(--border);\\n  transform: translateY(0);\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn.dropdown-btn[_ngcontent-%COMP%] {\\n  gap: 4px;\\n  padding: 8px 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn.dropdown-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]   .align-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  line-height: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  height: 100%;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 8px;\\n  min-width: 160px;\\n  z-index: 1000;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n  margin-top: 4px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: transparent;\\n  border: none;\\n  padding: 8px 12px;\\n  color: var(--text);\\n  text-align: left;\\n  cursor: pointer;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: background-color 0.2s ease;\\n}\\n.edit-container[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background: var(--background);\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-menu[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n  padding: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-section[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-section[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: var(--text-muted);\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(7, 1fr);\\n  gap: 6px;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 4px;\\n  border: 1px solid var(--border);\\n  cursor: pointer;\\n  transition: transform 0.2s ease;\\n}\\n.edit-container[_ngcontent-%COMP%]   .color-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.rich-editor[_ngcontent-%COMP%] {\\n  min-height: 300px;\\n  padding: 16px;\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  background: var(--background);\\n  color: var(--text);\\n  font-size: 16px;\\n  line-height: 1.6;\\n  outline: none;\\n  margin-bottom: 16px;\\n}\\n.rich-editor[_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent);\\n}\\n.rich-editor[contenteditable][_ngcontent-%COMP%]:empty::before {\\n  content: attr(placeholder);\\n  color: var(--text-muted);\\n  font-style: italic;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 16px 0 8px 0;\\n  font-weight: 600;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .rich-editor[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  padding-left: 24px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 4px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin: 8px 0;\\n  padding: 4px 0;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   .checklist-checkbox[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n  accent-color: var(--accent);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  outline: none;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:focus {\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 4px;\\n  padding: 2px 4px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent);\\n  padding: 12px 16px;\\n  margin: 16px 0;\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 0 8px 8px 0;\\n  font-style: italic;\\n  color: var(--text-muted);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background: rgba(0, 122, 255, 0.15);\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 4px;\\n  padding: 2px 6px;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 13px;\\n  color: #ff6b6b;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  border: 1px solid var(--border);\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n  overflow-x: auto;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  padding: 0;\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 14px;\\n  color: #a8e6cf;\\n  outline: none;\\n  white-space: pre;\\n  display: block;\\n  min-height: 20px;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-divider[_ngcontent-%COMP%] {\\n  border: none;\\n  height: 1px;\\n  background: var(--border);\\n  margin: 24px 0;\\n  opacity: 0.6;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-link[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  text-decoration: underline;\\n  cursor: pointer;\\n}\\n.rich-editor[_ngcontent-%COMP%]   .notion-link[_ngcontent-%COMP%]:hover {\\n  opacity: 0.8;\\n}\\n\\n.edit-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%], .save-btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  color: var(--text);\\n}\\n.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--border);\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n}\\n.save-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  color: var(--text-muted);\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 16px;\\n}\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .toolbar[_ngcontent-%COMP%] {\\n    gap: 8px !important;\\n  }\\n  .toolbar[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%] {\\n    gap: 2px !important;\\n    padding-right: 8px !important;\\n  }\\n  .toolbar[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%] {\\n    min-width: 32px !important;\\n    height: 32px !important;\\n    padding: 6px 8px !important;\\n  }\\n  .edit-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .edit-actions[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .edit-actions[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZ29hbHMvam91cm5hbC1lbnRyeS1kZXRhaWwvam91cm5hbC1lbnRyeS1kZXRhaWwucGFnZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0kscUJBQUE7RUFDQSxlQUFBO0VBQ0EscUJBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBQ0o7O0FBRUE7RUFDSSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7QUFDSjs7QUFFQTtFQUNJLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLFNBQUE7QUFDSjs7QUFFQTtFQUNJLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLG9CQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFDSjs7QUFFQTtFQUNJLHlCQUFBO0FBQ0o7O0FBRUE7RUFDSSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtFQUNBLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxhQUFBO0VBQ0EsUUFBQTtBQUNKOztBQUVBO0VBQ0ksdUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFDSjs7QUFFQTtFQUNJLG9CQUFBO0FBQ0o7O0FBRUE7RUFDSSxvQkFBQTtBQUNKOztBQUVBO0VBQ0kseUJBQUE7QUFDSjs7QUFFQTtFQUNJLHVCQUFBO0VBQ0EsNEJBQUE7RUFDQSxhQUFBO0VBQ0Esb0JBQUE7QUFDSjs7QUFFQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQkFBQTtFQUNBLHNDQUFBO0FBQ0o7O0FBRUE7RUFDSSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBQ0o7O0FBRUE7RUFDSSx3QkFBQTtFQUNBLGVBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0EsZUFBQTtBQUNKO0FBQ0k7RUFDSSxxQkFBQTtFQUNBLGdCQUFBO0FBQ1I7QUFFSTtFQUNJLGVBQUE7RUFDQSxrQkFBQTtBQUFSO0FBR0k7RUFDSSxlQUFBO0VBQ0Esa0JBQUE7QUFEUjtBQUlJO0VBQ0ksZUFBQTtFQUNBLGtCQUFBO0FBRlI7QUFLSTtFQUNJLGVBQUE7RUFDQSxrQkFBQTtBQUhSO0FBTUk7RUFDSSxlQUFBO0VBQ0Esa0JBQUE7QUFKUjtBQU9JO0VBQ0ksY0FBQTtBQUxSO0FBUUk7RUFDSSxjQUFBO0VBQ0Esa0JBQUE7QUFOUjtBQVNJO0VBQ0ksYUFBQTtBQVBSO0FBVUk7RUFDSSxnQkFBQTtBQVJSO0FBV0k7RUFDSSxrQkFBQTtBQVRSO0FBWUk7RUFDSSwwQkFBQTtBQVZSO0FBYUk7RUFDSSw2QkFBQTtFQUNBLFlBQUE7QUFYUjtBQWVJO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0FBYlI7QUFlUTtFQUNJLGVBQUE7RUFDQSwyQkFBQTtBQWJaO0FBZ0JRO0VBQ0ksT0FBQTtBQWRaO0FBa0JJO0VBQ0ksb0NBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxrQ0FBQTtFQUNBLDBCQUFBO0VBQ0Esa0JBQUE7RUFDQSx3QkFBQTtBQWhCUjtBQW1CSTtFQUNJLHVCQUFBO0VBQ0EsK0JBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esd0RBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQWpCUjtBQW9CSTtFQUNJLHVCQUFBO0VBQ0EsK0JBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUFsQlI7QUFvQlE7RUFDSSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0VBQ0Esd0RBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0VBQ0EsY0FBQTtBQWxCWjtBQXNCSTtFQUNJLFlBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7RUFDQSxjQUFBO0VBQ0EsWUFBQTtBQXBCUjtBQXVCSTtFQUNJLG9CQUFBO0VBQ0EsMEJBQUE7RUFDQSxlQUFBO0FBckJSO0FBdUJRO0VBQ0ksWUFBQTtBQXJCWjs7QUEyQkk7RUFDSSxhQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSw2QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsK0JBQUE7RUFDQSx3Q0FBQTtBQXhCUjtBQTJCSTtFQUNJLGFBQUE7RUFDQSxRQUFBO0VBQ0EscUNBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBekJSO0FBMkJRO0VBQ0ksa0JBQUE7RUFDQSxnQkFBQTtBQXpCWjtBQTZCSTtFQUNJLHVCQUFBO0VBQ0EsK0JBQUE7RUFDQSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7QUEzQlI7QUE2QlE7RUFDSSx1QkFBQTtFQUNBLDJCQUFBO0VBQ0EsMkJBQUE7QUEzQlo7QUE4QlE7RUFDSSx5QkFBQTtFQUNBLHdCQUFBO0FBNUJaO0FBK0JRO0VBQ0ksZUFBQTtBQTdCWjtBQWdDUTtFQUNJLFFBQUE7RUFDQSxpQkFBQTtBQTlCWjtBQWdDWTtFQUNJLGVBQUE7QUE5QmhCO0FBa0NRO0VBQ0ksZUFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBaENaO0FBcUNJO0VBQ0ksa0JBQUE7QUFuQ1I7QUFzQ0k7RUFDSSxrQkFBQTtFQUNBLFNBQUE7RUFDQSxPQUFBO0VBQ0EsdUJBQUE7RUFDQSwrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLHlDQUFBO0VBQ0EsZUFBQTtBQXBDUjtBQXNDUTtFQUNJLFdBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0Esc0NBQUE7QUFwQ1o7QUFzQ1k7RUFDSSw2QkFBQTtBQXBDaEI7QUF5Q0k7RUFDSSxnQkFBQTtFQUNBLGFBQUE7QUF2Q1I7QUEwQ0k7RUFDSSxtQkFBQTtBQXhDUjtBQTBDUTtFQUNJLGdCQUFBO0FBeENaO0FBNENJO0VBQ0ksY0FBQTtFQUNBLGVBQUE7RUFDQSx3QkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUExQ1I7QUE2Q0k7RUFDSSxhQUFBO0VBQ0EscUNBQUE7RUFDQSxRQUFBO0FBM0NSO0FBOENJO0VBQ0ksV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLCtCQUFBO0VBQ0EsZUFBQTtFQUNBLCtCQUFBO0FBNUNSO0FBOENRO0VBQ0kscUJBQUE7QUE1Q1o7O0FBaURBO0VBQ0ksaUJBQUE7RUFDQSxhQUFBO0VBQ0EsK0JBQUE7RUFDQSxrQkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUE5Q0o7QUFnREk7RUFDSSwyQkFBQTtBQTlDUjtBQWlESTtFQUNJLDBCQUFBO0VBQ0Esd0JBQUE7RUFDQSxrQkFBQTtBQS9DUjtBQWtESTtFQUNJLG9CQUFBO0VBQ0EsZ0JBQUE7QUFoRFI7QUFtREk7RUFDSSxlQUFBO0FBakRSO0FBb0RJO0VBQ0ksZUFBQTtBQWxEUjtBQXFESTtFQUNJLGVBQUE7QUFuRFI7QUFzREk7RUFDSSxhQUFBO0FBcERSO0FBdURJO0VBQ0ksYUFBQTtFQUNBLGtCQUFBO0FBckRSO0FBd0RJO0VBQ0ksYUFBQTtBQXREUjtBQTBESTtFQUNJLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFFBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtBQXhEUjtBQTBEUTtFQUNJLGVBQUE7RUFDQSwyQkFBQTtBQXhEWjtBQTJEUTtFQUNJLE9BQUE7RUFDQSxhQUFBO0FBekRaO0FBMkRZO0VBQ0kscUNBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBekRoQjtBQThESTtFQUNJLG9DQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0NBQUE7RUFDQSwwQkFBQTtFQUNBLGtCQUFBO0VBQ0Esd0JBQUE7QUE1RFI7QUE4RFE7RUFDSSxhQUFBO0VBQ0EsbUNBQUE7QUE1RFo7QUFnRUk7RUFDSSx1QkFBQTtFQUNBLCtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHdEQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7QUE5RFI7QUFpRUk7RUFDSSx1QkFBQTtFQUNBLCtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0VBQ0EsY0FBQTtFQUNBLGdCQUFBO0FBL0RSO0FBaUVRO0VBQ0ksdUJBQUE7RUFDQSxZQUFBO0VBQ0EsVUFBQTtFQUNBLHdEQUFBO0VBQ0EsZUFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsZ0JBQUE7QUEvRFo7QUFtRUk7RUFDSSxZQUFBO0VBQ0EsV0FBQTtFQUNBLHlCQUFBO0VBQ0EsY0FBQTtFQUNBLFlBQUE7QUFqRVI7QUFvRUk7RUFDSSxvQkFBQTtFQUNBLDBCQUFBO0VBQ0EsZUFBQTtBQWxFUjtBQW9FUTtFQUNJLFlBQUE7QUFsRVo7O0FBdUVBO0VBQ0ksYUFBQTtFQUNBLFNBQUE7RUFDQSx5QkFBQTtBQXBFSjs7QUF1RUE7RUFDSSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxzQ0FBQTtBQXBFSjs7QUF1RUE7RUFDSSx1QkFBQTtFQUNBLGtCQUFBO0FBcEVKO0FBc0VJO0VBQ0kseUJBQUE7QUFwRVI7O0FBd0VBO0VBQ0kseUJBQUE7RUFDQSxZQUFBO0FBckVKO0FBdUVJO0VBQ0ksbUJBQUE7QUFyRVI7O0FBeUVBO0VBQ0ksYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0Esd0JBQUE7QUF0RUo7QUF3RUk7RUFDSSxtQkFBQTtBQXRFUjtBQXlFSTtFQUNJLFNBQUE7RUFDQSxlQUFBO0FBdkVSOztBQTRFQTtFQUNJO0lBQ0ksYUFBQTtFQXpFTjtFQTRFRTtJQUNJLGFBQUE7RUExRU47RUE2RUU7SUFDSSxtQkFBQTtFQTNFTjtFQTZFTTtJQUNJLG1CQUFBO0lBQ0EsNkJBQUE7RUEzRVY7RUE4RU07SUFDSSwwQkFBQTtJQUNBLHVCQUFBO0lBQ0EsMkJBQUE7RUE1RVY7RUFnRkU7SUFDSSxzQkFBQTtFQTlFTjtFQWdGTTtJQUNJLFdBQUE7RUE5RVY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgICAtLWJhY2tncm91bmQ6ICMxYzFjMWU7XG4gICAgLS10ZXh0OiAjZmZmZmZmO1xuICAgIC0tdGV4dC1tdXRlZDogIzhlOGU5MztcbiAgICAtLWFjY2VudDogIzAwN2FmZjtcbiAgICAtLWNhcmQ6ICMyYzJjMmU7XG4gICAgLS1ib3JkZXI6ICMzYTNhM2M7XG4gICAgLS1zdWNjZXNzOiAjMzBkMTU4O1xuICAgIC0tZGFuZ2VyOiAjZmY0NTNhO1xuICAgIC0tcmFkaXVzOiAxMnB4O1xufVxuXG4uY29udGFpbmVyIHtcbiAgICB3aWR0aDogNDgwcHg7XG4gICAgbWFyZ2luOiAwIGF1dG87XG4gICAgcGFkZGluZzogMjBweDtcbiAgICBtaW4taGVpZ2h0OiAxMDB2aDtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1iYWNrZ3JvdW5kKTtcbiAgICBjb2xvcjogdmFyKC0tdGV4dCk7XG59XG5cbmhlYWRlciB7XG4gICAgbWFyZ2luLWJvdHRvbTogMzBweDtcbn1cblxuLmhlYWRlci1jb250ZW50IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGdhcDogMTZweDtcbn1cblxuLmJhY2stYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIHBhZGRpbmc6IDhweDtcbiAgICBjb2xvcjogdmFyKC0tYWNjZW50KTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIG1pbi13aWR0aDogNDBweDtcbiAgICBoZWlnaHQ6IDQwcHg7XG59XG5cbi5iYWNrLWJ0bjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tYm9yZGVyKTtcbn1cblxuaDEge1xuICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIG1hcmdpbjogMDtcbiAgICBmbGV4OiAxO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLmhlYWRlci1hY3Rpb25zIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogOHB4O1xufVxuXG4uZWRpdC1idG4sIC5kZWxldGUtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIHBhZGRpbmc6IDhweDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIG1pbi13aWR0aDogNDBweDtcbiAgICBoZWlnaHQ6IDQwcHg7XG59XG5cbi5lZGl0LWJ0biB7XG4gICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG59XG5cbi5kZWxldGUtYnRuIHtcbiAgICBjb2xvcjogdmFyKC0tZGFuZ2VyKTtcbn1cblxuLmVkaXQtYnRuOmhvdmVyLCAuZGVsZXRlLWJ0bjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tYm9yZGVyKTtcbn1cblxuLmNvbnRlbnQge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWNhcmQpO1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cyk7XG4gICAgcGFkZGluZzogMjRweDtcbiAgICBtYXJnaW4tYm90dG9tOiAxMDBweDtcbn1cblxuLm1pbGVzdG9uZS1pbmZvIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgcGFkZGluZy1ib3R0b206IDE2cHg7XG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7XG59XG5cbi5taWxlc3RvbmUtYmFkZ2Uge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWFjY2VudCk7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIHBhZGRpbmc6IDZweCAxMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5lbnRyeS1kYXRlIHtcbiAgICBjb2xvcjogdmFyKC0tdGV4dC1tdXRlZCk7XG4gICAgZm9udC1zaXplOiAxNHB4O1xufVxuXG4uam91cm5hbC1jb250ZW50IHtcbiAgICBsaW5lLWhlaWdodDogMS42O1xuICAgIGZvbnQtc2l6ZTogMTZweDtcblxuICAgIGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYge1xuICAgICAgICBtYXJnaW46IDI0cHggMCAxMnB4IDA7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgfVxuXG4gICAgaDEge1xuICAgICAgICBmb250LXNpemU6IDI4cHg7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0KTtcbiAgICB9XG5cbiAgICBoMiB7XG4gICAgICAgIGZvbnQtc2l6ZTogMjRweDtcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQpO1xuICAgIH1cblxuICAgIGgzIHtcbiAgICAgICAgZm9udC1zaXplOiAyMHB4O1xuICAgICAgICBjb2xvcjogdmFyKC0tdGV4dCk7XG4gICAgfVxuXG4gICAgaDQge1xuICAgICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0KTtcbiAgICB9XG5cbiAgICBoNSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQpO1xuICAgIH1cblxuICAgIHAge1xuICAgICAgICBtYXJnaW46IDEycHggMDtcbiAgICB9XG5cbiAgICB1bCwgb2wge1xuICAgICAgICBtYXJnaW46IDEycHggMDtcbiAgICAgICAgcGFkZGluZy1sZWZ0OiAyNHB4O1xuICAgIH1cblxuICAgIGxpIHtcbiAgICAgICAgbWFyZ2luOiA2cHggMDtcbiAgICB9XG5cbiAgICBzdHJvbmcge1xuICAgICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cblxuICAgIGVtIHtcbiAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xuICAgIH1cblxuICAgIHUge1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICB9XG5cbiAgICBzIHtcbiAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBsaW5lLXRocm91Z2g7XG4gICAgICAgIG9wYWNpdHk6IDAuNztcbiAgICB9XG5cbiAgICAvLyBOb3Rpb24tbGlrZSBlbGVtZW50cyBpbiB2aWV3IG1vZGVcbiAgICAuY2hlY2tsaXN0LWl0ZW0ge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgICAgZ2FwOiA4cHg7XG4gICAgICAgIG1hcmdpbjogOHB4IDA7XG4gICAgICAgIHBhZGRpbmc6IDRweCAwO1xuXG4gICAgICAgIC5jaGVja2xpc3QtY2hlY2tib3gge1xuICAgICAgICAgICAgbWFyZ2luLXRvcDogMnB4O1xuICAgICAgICAgICAgYWNjZW50LWNvbG9yOiB2YXIoLS1hY2NlbnQpO1xuICAgICAgICB9XG5cbiAgICAgICAgc3BhbiB7XG4gICAgICAgICAgICBmbGV4OiAxO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLm5vdGlvbi1xdW90ZSB7XG4gICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgdmFyKC0tYWNjZW50KTtcbiAgICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xuICAgICAgICBtYXJnaW46IDE2cHggMDtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxMjIsIDI1NSwgMC4xKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMCA4cHggOHB4IDA7XG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuICAgIH1cblxuICAgIC5ub3Rpb24tY29kZSB7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWNhcmQpO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICAgIHBhZGRpbmc6IDJweCA2cHg7XG4gICAgICAgIGZvbnQtZmFtaWx5OiAnTW9uYWNvJywgJ01lbmxvJywgJ1VidW50dSBNb25vJywgbW9ub3NwYWNlO1xuICAgICAgICBmb250LXNpemU6IDEzcHg7XG4gICAgICAgIGNvbG9yOiAjZmY2YjZiO1xuICAgIH1cblxuICAgIC5ub3Rpb24tY29kZS1ibG9jayB7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWNhcmQpO1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICAgIG1hcmdpbjogMTZweCAwO1xuICAgICAgICBvdmVyZmxvdy14OiBhdXRvO1xuXG4gICAgICAgIGNvZGUge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgICBwYWRkaW5nOiAwO1xuICAgICAgICAgICAgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7XG4gICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICBjb2xvcjogI2E4ZTZjZjtcbiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBwcmU7XG4gICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC5ub3Rpb24tZGl2aWRlciB7XG4gICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgaGVpZ2h0OiAxcHg7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWJvcmRlcik7XG4gICAgICAgIG1hcmdpbjogMjRweCAwO1xuICAgICAgICBvcGFjaXR5OiAwLjY7XG4gICAgfVxuXG4gICAgLm5vdGlvbi1saW5rIHtcbiAgICAgICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICBvcGFjaXR5OiAwLjg7XG4gICAgICAgIH1cbiAgICB9XG59XG5cbi5lZGl0LWNvbnRhaW5lciB7XG4gICAgLnRvb2xiYXIge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDEycHg7XG4gICAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWJhY2tncm91bmQpO1xuICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgfVxuXG4gICAgLnRvb2xiYXItZ3JvdXAge1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBnYXA6IDRweDtcbiAgICAgICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgdmFyKC0tYm9yZGVyKTtcbiAgICAgICAgcGFkZGluZy1yaWdodDogMTJweDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAgICAgICAmOmxhc3QtY2hpbGQge1xuICAgICAgICAgICAgYm9yZGVyLXJpZ2h0OiBub25lO1xuICAgICAgICAgICAgcGFkZGluZy1yaWdodDogMDtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC50b29sYmFyLWJ0biB7XG4gICAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgIHBhZGRpbmc6IDA7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0KTtcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgbWluLXdpZHRoOiAzNnB4O1xuICAgICAgICBoZWlnaHQ6IDM2cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZCk7XG4gICAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7XG4gICAgICAgIH1cblxuICAgICAgICAmOmFjdGl2ZSB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1ib3JkZXIpO1xuICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICAgICAgICB9XG5cbiAgICAgICAgaW9uLWljb24ge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xuICAgICAgICB9XG5cbiAgICAgICAgJi5kcm9wZG93bi1idG4ge1xuICAgICAgICAgICAgZ2FwOiA0cHg7XG4gICAgICAgICAgICBwYWRkaW5nOiA4cHggMTJweDtcblxuICAgICAgICAgICAgaW9uLWljb24ge1xuICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5hbGlnbi1pY29uIHtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxO1xuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8gRHJvcGRvd24gc3R5bGVzXG4gICAgLmRyb3Bkb3duLWNvbnRhaW5lciB7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB9XG5cbiAgICAuZHJvcGRvd24tbWVudSB7XG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgdG9wOiAxMDAlO1xuICAgICAgICBsZWZ0OiAwO1xuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tYm9yZGVyKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgICAgICBwYWRkaW5nOiA4cHg7XG4gICAgICAgIG1pbi13aWR0aDogMTYwcHg7XG4gICAgICAgIHotaW5kZXg6IDEwMDA7XG4gICAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjIpO1xuICAgICAgICBtYXJnaW4tdG9wOiA0cHg7XG5cbiAgICAgICAgYnV0dG9uIHtcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgICAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0KTtcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGxlZnQ7XG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICAgICAgICBmb250LXNpemU6IDE0cHg7XG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnMgZWFzZTtcblxuICAgICAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tYmFja2dyb3VuZCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAuY29sb3ItbWVudSB7XG4gICAgICAgIG1pbi13aWR0aDogMjAwcHg7XG4gICAgICAgIHBhZGRpbmc6IDEycHg7XG4gICAgfVxuXG4gICAgLmNvbG9yLXNlY3Rpb24ge1xuICAgICAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xuXG4gICAgICAgICY6bGFzdC1jaGlsZCB7XG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLmNvbG9yLWxhYmVsIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICAgIGZvbnQtc2l6ZTogMTJweDtcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuICAgICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLmNvbG9yLWdyaWQge1xuICAgICAgICBkaXNwbGF5OiBncmlkO1xuICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCg3LCAxZnIpO1xuICAgICAgICBnYXA6IDZweDtcbiAgICB9XG5cbiAgICAuY29sb3ItYnRuIHtcbiAgICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICAgIGhlaWdodDogMjRweDtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpO1xuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjJzIGVhc2U7XG5cbiAgICAgICAgJjpob3ZlciB7XG4gICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XG4gICAgICAgIH1cbiAgICB9XG59XG5cbi5yaWNoLWVkaXRvciB7XG4gICAgbWluLWhlaWdodDogMzAwcHg7XG4gICAgcGFkZGluZzogMTZweDtcbiAgICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1ib3JkZXIpO1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1iYWNrZ3JvdW5kKTtcbiAgICBjb2xvcjogdmFyKC0tdGV4dCk7XG4gICAgZm9udC1zaXplOiAxNnB4O1xuICAgIGxpbmUtaGVpZ2h0OiAxLjY7XG4gICAgb3V0bGluZTogbm9uZTtcbiAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuXG4gICAgJjpmb2N1cyB7XG4gICAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0tYWNjZW50KTtcbiAgICB9XG5cbiAgICAmW2NvbnRlbnRlZGl0YWJsZV06ZW1wdHk6OmJlZm9yZSB7XG4gICAgICAgIGNvbnRlbnQ6IGF0dHIocGxhY2Vob2xkZXIpO1xuICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1tdXRlZCk7XG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgICB9XG5cbiAgICBoMSwgaDIsIGgzLCBoNCwgaDUsIGg2IHtcbiAgICAgICAgbWFyZ2luOiAxNnB4IDAgOHB4IDA7XG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgfVxuXG4gICAgaDMge1xuICAgICAgICBmb250LXNpemU6IDIwcHg7XG4gICAgfVxuXG4gICAgaDQge1xuICAgICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgfVxuXG4gICAgaDUge1xuICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgfVxuXG4gICAgcCB7XG4gICAgICAgIG1hcmdpbjogOHB4IDA7XG4gICAgfVxuXG4gICAgdWwsIG9sIHtcbiAgICAgICAgbWFyZ2luOiA4cHggMDtcbiAgICAgICAgcGFkZGluZy1sZWZ0OiAyNHB4O1xuICAgIH1cblxuICAgIGxpIHtcbiAgICAgICAgbWFyZ2luOiA0cHggMDtcbiAgICB9XG5cbiAgICAvLyBOb3Rpb24tbGlrZSBlbGVtZW50c1xuICAgIC5jaGVja2xpc3QtaXRlbSB7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgICBnYXA6IDhweDtcbiAgICAgICAgbWFyZ2luOiA4cHggMDtcbiAgICAgICAgcGFkZGluZzogNHB4IDA7XG5cbiAgICAgICAgLmNoZWNrbGlzdC1jaGVja2JveCB7XG4gICAgICAgICAgICBtYXJnaW4tdG9wOiAycHg7XG4gICAgICAgICAgICBhY2NlbnQtY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgICAgIH1cblxuICAgICAgICBzcGFuIHtcbiAgICAgICAgICAgIGZsZXg6IDE7XG4gICAgICAgICAgICBvdXRsaW5lOiBub25lO1xuXG4gICAgICAgICAgICAmOmZvY3VzIHtcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDUpO1xuICAgICAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAycHggNHB4O1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLm5vdGlvbi1xdW90ZSB7XG4gICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgdmFyKC0tYWNjZW50KTtcbiAgICAgICAgcGFkZGluZzogMTJweCAxNnB4O1xuICAgICAgICBtYXJnaW46IDE2cHggMDtcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxMjIsIDI1NSwgMC4xKTtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMCA4cHggOHB4IDA7XG4gICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuXG4gICAgICAgICY6Zm9jdXMge1xuICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTIyLCAyNTUsIDAuMTUpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLm5vdGlvbi1jb2RlIHtcbiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZCk7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICAgICAgcGFkZGluZzogMnB4IDZweDtcbiAgICAgICAgZm9udC1mYW1pbHk6ICdNb25hY28nLCAnTWVubG8nLCAnVWJ1bnR1IE1vbm8nLCBtb25vc3BhY2U7XG4gICAgICAgIGZvbnQtc2l6ZTogMTNweDtcbiAgICAgICAgY29sb3I6ICNmZjZiNmI7XG4gICAgfVxuXG4gICAgLm5vdGlvbi1jb2RlLWJsb2NrIHtcbiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZCk7XG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWJvcmRlcik7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgICAgbWFyZ2luOiAxNnB4IDA7XG4gICAgICAgIG92ZXJmbG93LXg6IGF1dG87XG5cbiAgICAgICAgY29kZSB7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgICAgIHBhZGRpbmc6IDA7XG4gICAgICAgICAgICBmb250LWZhbWlseTogJ01vbmFjbycsICdNZW5sbycsICdVYnVudHUgTW9ubycsIG1vbm9zcGFjZTtcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICAgICAgICAgIGNvbG9yOiAjYThlNmNmO1xuICAgICAgICAgICAgb3V0bGluZTogbm9uZTtcbiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBwcmU7XG4gICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgICAgIG1pbi1oZWlnaHQ6IDIwcHg7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAubm90aW9uLWRpdmlkZXIge1xuICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgIGhlaWdodDogMXB4O1xuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1ib3JkZXIpO1xuICAgICAgICBtYXJnaW46IDI0cHggMDtcbiAgICAgICAgb3BhY2l0eTogMC42O1xuICAgIH1cblxuICAgIC5ub3Rpb24tbGluayB7XG4gICAgICAgIGNvbG9yOiB2YXIoLS1hY2NlbnQpO1xuICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xuXG4gICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgb3BhY2l0eTogMC44O1xuICAgICAgICB9XG4gICAgfVxufVxuXG4uZWRpdC1hY3Rpb25zIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGdhcDogMTJweDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kO1xufVxuXG4uY2FuY2VsLWJ0biwgLnNhdmUtYnRuIHtcbiAgICBwYWRkaW5nOiAxMnB4IDI0cHg7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICBmb250LXNpemU6IDE2cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzIGVhc2U7XG59XG5cbi5jYW5jZWwtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICBjb2xvcjogdmFyKC0tdGV4dCk7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tYm9yZGVyKTtcbiAgICB9XG59XG5cbi5zYXZlLWJ0biB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tYWNjZW50KTtcbiAgICBjb2xvcjogd2hpdGU7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgICAgYmFja2dyb3VuZDogIzAwNTZiMztcbiAgICB9XG59XG5cbi5sb2FkaW5nLWNvbnRhaW5lciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgcGFkZGluZzogNjBweCAyMHB4O1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LW11dGVkKTtcblxuICAgIGlvbi1zcGlubmVyIHtcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICB9XG5cbiAgICBwIHtcbiAgICAgICAgbWFyZ2luOiAwO1xuICAgICAgICBmb250LXNpemU6IDE2cHg7XG4gICAgfVxufVxuXG4vLyBNb2JpbGUgcmVzcG9uc2l2ZW5lc3NcbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAgIC5jb250YWluZXIge1xuICAgICAgICBwYWRkaW5nOiAxNnB4O1xuICAgIH1cblxuICAgIC5jb250ZW50IHtcbiAgICAgICAgcGFkZGluZzogMTZweDtcbiAgICB9XG5cbiAgICAudG9vbGJhciB7XG4gICAgICAgIGdhcDogOHB4ICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgLnRvb2xiYXItZ3JvdXAge1xuICAgICAgICAgICAgZ2FwOiAycHggIWltcG9ydGFudDtcbiAgICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDhweCAhaW1wb3J0YW50O1xuICAgICAgICB9XG5cbiAgICAgICAgLnRvb2xiYXItYnRuIHtcbiAgICAgICAgICAgIG1pbi13aWR0aDogMzJweCAhaW1wb3J0YW50O1xuICAgICAgICAgICAgaGVpZ2h0OiAzMnB4ICFpbXBvcnRhbnQ7XG4gICAgICAgICAgICBwYWRkaW5nOiA2cHggOHB4ICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAuZWRpdC1hY3Rpb25zIHtcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcblxuICAgICAgICAuY2FuY2VsLWJ0biwgLnNhdmUtYnRuIHtcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgICB9XG4gICAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "names": ["IonicModule", "CommonModule", "FormsModule", "NavigationComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "JournalEntryDetailPage_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "startEditing", "ɵɵelement", "ɵɵelementEnd", "JournalEntryDetailPage_button_9_Template_button_click_0_listener", "_r3", "deleteEntry", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "entry", "milestone_percentage", "ɵɵtextInterpolate", "ɵɵpipeBind2", "created_at", "ɵɵproperty", "content", "ɵɵsanitizeHtml", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_1_listener", "_r5", "formatText", "closeDropdown", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_3_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_5_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_7_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_9_listener", "JournalEntryDetailPage_div_10_div_3_div_21_Template_button_click_11_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_5_listener", "_r6", "setTextColor", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_6_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_7_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_8_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_9_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_10_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_11_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_16_listener", "setBackgroundColor", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_17_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_18_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_19_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_20_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_21_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template_button_click_22_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_3_listener", "_r4", "JournalEntryDetailPage_div_10_div_3_Template_button_click_6_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_9_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_12_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_17_listener", "toggleDropdown", "ɵɵtemplate", "JournalEntryDetailPage_div_10_div_3_div_21_Template", "JournalEntryDetailPage_div_10_div_3_Template_button_click_23_listener", "insertList", "JournalEntryDetailPage_div_10_div_3_Template_button_click_25_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_27_listener", "insertCheckList", "JournalEntryDetailPage_div_10_div_3_Template_button_click_30_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_33_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_36_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_40_listener", "insertLink", "JournalEntryDetailPage_div_10_div_3_Template_button_click_42_listener", "insertCode", "JournalEntryDetailPage_div_10_div_3_Template_button_click_44_listener", "insertDivider", "JournalEntryDetailPage_div_10_div_3_Template_button_click_48_listener", "JournalEntryDetailPage_div_10_div_3_div_50_Template", "JournalEntryDetailPage_div_10_div_3_Template_button_click_52_listener", "JournalEntryDetailPage_div_10_div_3_Template_button_click_54_listener", "JournalEntryDetailPage_div_10_div_3_Template_div_input_56_listener", "$event", "onContentInput", "JournalEntryDetailPage_div_10_div_3_Template_div_blur_56_listener", "onContentBlur", "JournalEntryDetailPage_div_10_div_3_Template_button_click_59_listener", "cancelEditing", "JournalEntryDetailPage_div_10_div_3_Template_button_click_61_listener", "saveEntry", "activeDropdown", "JournalEntryDetailPage_div_10_div_1_Template", "JournalEntryDetailPage_div_10_div_2_Template", "JournalEntryDetailPage_div_10_div_3_Template", "isEditing", "JournalEntryDetailPage", "constructor", "route", "router", "goalService", "toastController", "sanitizer", "goalId", "entryId", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "ngOnInit", "snapshot", "paramMap", "get", "loadJournalEntry", "navigate", "ngAfterViewInit", "rich<PERSON><PERSON><PERSON>", "nativeElement", "innerHTML", "document", "addEventListener", "handleKeyboardShortcuts", "bind", "handleClickOutside", "ngOnDestroy", "removeEventListener", "getJournalEntries", "subscribe", "entries", "find", "e", "id", "showToast", "setTimeout", "_this", "_asyncToGenerator", "trim", "updateJournalEntry", "error", "console", "_this2", "confirmed", "confirm", "deleteJournalEntry", "goBack", "message", "_this3", "toast", "create", "duration", "position", "present", "getSafeHtml", "bypassSecurityTrustHtml", "command", "value", "execCommand", "ordered", "event", "target", "dropdown", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "checklistItem", "createElement", "className", "deleteContents", "insertNode", "textSpan", "querySelector", "newRange", "createRange", "selectNodeContents", "removeAllRanges", "addRange", "selectedText", "toString", "url", "prompt", "link", "href", "rel", "textContent", "setStartAfter", "collapse", "code", "codeBlock", "codeElement", "divider", "color", "isCtrl", "ctrl<PERSON>ey", "metaKey", "key", "toLowerCase", "preventDefault", "closest", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "GoalService", "i3", "ToastController", "i4", "Dom<PERSON><PERSON><PERSON>zer", "selectors", "viewQuery", "JournalEntryDetailPage_Query", "rf", "ctx", "JournalEntryDetailPage_Template_button_click_3_listener", "JournalEntryDetailPage_button_8_Template", "JournalEntryDetailPage_button_9_Template", "JournalEntryDetailPage_div_10_Template", "JournalEntryDetailPage_div_11_Template", "IonIcon", "Ion<PERSON><PERSON><PERSON>", "i5", "NgIf", "DatePipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\journal-entry-detail\\journal-entry-detail.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\journal-entry-detail\\journal-entry-detail.page.html"], "sourcesContent": ["import { <PERSON>mponent, OnInit, ViewChild, ElementRef, AfterViewInit, <PERSON><PERSON>estroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { IonicModule, ToastController } from '@ionic/angular';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { GoalJournalEntry } from '../../../models/goal.model';\nimport { GoalService } from '../../../services/goal.service';\n\n@Component({\n  selector: 'app-journal-entry-detail',\n  templateUrl: './journal-entry-detail.page.html',\n  styleUrls: ['./journal-entry-detail.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, NavigationComponent]\n})\nexport class JournalEntryDetailPage implements OnInit, AfterViewInit, OnD<PERSON>roy {\n  @ViewChild('richEditor', { static: false }) richEditor!: ElementRef<HTMLDivElement>;\n\n  goalId: string = '';\n  entryId: string = '';\n  entry: GoalJournalEntry | null = null;\n  isEditing: boolean = false;\n  editContent: string = '';\n  isLoading: boolean = true;\n  activeDropdown: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private goalService: GoalService,\n    private toastController: ToastController,\n    private sanitizer: DomSanitizer\n  ) {}\n\n  ngOnInit() {\n    this.goalId = this.route.snapshot.paramMap.get('goalId') || '';\n    this.entryId = this.route.snapshot.paramMap.get('entryId') || '';\n\n    if (this.goalId && this.entryId) {\n      this.loadJournalEntry();\n    } else {\n      this.router.navigate(['/goals']);\n    }\n  }\n\n  ngAfterViewInit() {\n    // Initialize rich editor content after view is ready\n    if (this.isEditing && this.richEditor) {\n      this.richEditor.nativeElement.innerHTML = this.editContent;\n    }\n\n    // Add keyboard shortcuts\n    document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n\n    // Add click outside handler for dropdowns\n    document.addEventListener('click', this.handleClickOutside.bind(this));\n  }\n\n  ngOnDestroy() {\n    // Clean up event listeners\n    document.removeEventListener('keydown', this.handleKeyboardShortcuts.bind(this));\n    document.removeEventListener('click', this.handleClickOutside.bind(this));\n  }\n\n  loadJournalEntry() {\n    this.isLoading = true;\n\n    // Get all journal entries for the goal and find the specific one\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.entry = entries.find(e => e.id === this.entryId) || null;\n      this.isLoading = false;\n\n      if (!this.entry) {\n        this.showToast('Journal entry not found');\n        this.router.navigate(['/goals', this.goalId]);\n      }\n    });\n  }\n\n  startEditing() {\n    if (this.entry) {\n      this.editContent = this.entry.content;\n      this.isEditing = true;\n\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.richEditor) {\n          this.richEditor.nativeElement.innerHTML = this.editContent;\n        }\n      }, 0);\n    }\n  }\n\n  cancelEditing() {\n    this.isEditing = false;\n    this.editContent = '';\n  }\n\n  async saveEntry() {\n    if (!this.entry || !this.editContent.trim()) return;\n\n    try {\n      await this.goalService.updateJournalEntry(this.entry.id!, { content: this.editContent.trim() });\n      this.entry.content = this.editContent.trim();\n      this.isEditing = false;\n      this.editContent = '';\n      this.showToast('Journal entry updated successfully');\n    } catch (error) {\n      console.error('Error updating journal entry:', error);\n      this.showToast('Error updating journal entry');\n    }\n  }\n\n  async deleteEntry() {\n    if (!this.entry) return;\n\n    const confirmed = confirm('Are you sure you want to delete this journal entry? This action cannot be undone.');\n    if (!confirmed) return;\n\n    try {\n      await this.goalService.deleteJournalEntry(this.entry.id!);\n      this.showToast('Journal entry deleted successfully');\n      this.router.navigate(['/goals', this.goalId]);\n    } catch (error) {\n      console.error('Error deleting journal entry:', error);\n      this.showToast('Error deleting journal entry');\n    }\n  }\n\n  goBack() {\n    this.router.navigate(['/goals', this.goalId]);\n  }\n\n  private async showToast(message: string) {\n    const toast = await this.toastController.create({\n      message,\n      duration: 2000,\n      position: 'bottom'\n    });\n    toast.present();\n  }\n\n  getSafeHtml(content: string): SafeHtml {\n    return this.sanitizer.bypassSecurityTrustHtml(content);\n  }\n\n  // Rich text formatting methods\n  formatText(command: string, value?: string) {\n    document.execCommand(command, false, value);\n  }\n\n  insertList(ordered: boolean = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n\n  onContentInput(event: any) {\n    this.editContent = event.target.innerHTML;\n  }\n\n  onContentBlur(event: any) {\n    this.editContent = event.target.innerHTML;\n  }\n\n  // Dropdown management\n  toggleDropdown(dropdown: string) {\n    this.activeDropdown = this.activeDropdown === dropdown ? null : dropdown;\n  }\n\n  closeDropdown() {\n    this.activeDropdown = null;\n  }\n\n  // Advanced formatting methods\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n\n      range.deleteContents();\n      range.insertNode(checklistItem);\n\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n    }\n  }\n\n  insertLink() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      const url = prompt('Enter URL:', 'https://');\n\n      if (url && url.trim()) {\n        const link = document.createElement('a');\n        link.href = url.trim();\n        link.target = '_blank';\n        link.rel = 'noopener noreferrer';\n        link.className = 'notion-link';\n        link.textContent = selectedText || url.trim();\n\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(link);\n\n        // Move cursor after link\n        range.setStartAfter(link);\n        range.collapse(true);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    }\n  }\n\n  insertCode() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const selectedText = selection.toString();\n      if (selectedText) {\n        // Inline code\n        const code = document.createElement('code');\n        code.className = 'notion-code';\n        code.textContent = selectedText;\n\n        const range = selection.getRangeAt(0);\n        range.deleteContents();\n        range.insertNode(code);\n      } else {\n        // Code block\n        const range = selection.getRangeAt(0);\n        const codeBlock = document.createElement('pre');\n        codeBlock.className = 'notion-code-block';\n        codeBlock.innerHTML = '<code contenteditable=\"true\">// Your code here</code>';\n\n        range.deleteContents();\n        range.insertNode(codeBlock);\n\n        // Select the code content\n        const codeElement = codeBlock.querySelector('code');\n        if (codeElement) {\n          const newRange = document.createRange();\n          newRange.selectNodeContents(codeElement);\n          selection.removeAllRanges();\n          selection.addRange(newRange);\n        }\n      }\n    }\n  }\n\n  insertDivider() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const divider = document.createElement('hr');\n      divider.className = 'notion-divider';\n\n      range.deleteContents();\n      range.insertNode(divider);\n\n      // Move cursor after divider\n      range.setStartAfter(divider);\n      range.collapse(true);\n      selection.removeAllRanges();\n      selection.addRange(range);\n    }\n  }\n\n  setTextColor(color: string) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n      this.formatText('foreColor', '#ffffff');\n    } else {\n      this.formatText('foreColor', color);\n    }\n  }\n\n  setBackgroundColor(color: string) {\n    if (color === 'default') {\n      this.formatText('removeFormat');\n    } else {\n      this.formatText('backColor', color);\n    }\n  }\n\n  // Keyboard shortcuts\n  handleKeyboardShortcuts(event: KeyboardEvent) {\n    if (!this.isEditing) return;\n\n    const isCtrl = event.ctrlKey || event.metaKey;\n\n    if (isCtrl) {\n      switch (event.key.toLowerCase()) {\n        case 'b':\n          event.preventDefault();\n          this.formatText('bold');\n          break;\n        case 'i':\n          event.preventDefault();\n          this.formatText('italic');\n          break;\n        case 'u':\n          event.preventDefault();\n          this.formatText('underline');\n          break;\n        case 'z':\n          event.preventDefault();\n          this.formatText('undo');\n          break;\n        case 'y':\n          event.preventDefault();\n          this.formatText('redo');\n          break;\n        case 's':\n          event.preventDefault();\n          this.saveEntry();\n          break;\n      }\n    }\n\n    // Escape to close dropdowns\n    if (event.key === 'Escape') {\n      this.closeDropdown();\n    }\n  }\n\n  // Click outside handler\n  handleClickOutside(event: Event) {\n    const target = event.target as HTMLElement;\n    if (!target.closest('.dropdown-container')) {\n      this.closeDropdown();\n    }\n  }\n}\n", "<div class=\"container\">\n    <header>\n        <div class=\"header-content\">\n            <button class=\"back-btn\" (click)=\"goBack()\">\n                <ion-icon name=\"arrow-back\"></ion-icon>\n            </button>\n            <h1>Journal Entry</h1>\n            <div class=\"header-actions\">\n                <button class=\"edit-btn\" *ngIf=\"!isEditing && entry\" (click)=\"startEditing()\">\n                    <ion-icon name=\"create-outline\"></ion-icon>\n                </button>\n                <button class=\"delete-btn\" *ngIf=\"!isEditing && entry\" (click)=\"deleteEntry()\">\n                    <ion-icon name=\"trash-outline\"></ion-icon>\n                </button>\n            </div>\n        </div>\n    </header>\n\n    <div class=\"content\" *ngIf=\"!isLoading\">\n        <div class=\"milestone-info\" *ngIf=\"entry\">\n            <div class=\"milestone-badge\">🎯 {{ entry.milestone_percentage }}%</div>\n            <div class=\"entry-date\">{{ entry.created_at | date:'medium' }}</div>\n        </div>\n\n        <!-- View Mode -->\n        <div class=\"journal-content\" *ngIf=\"!isEditing && entry\" [innerHTML]=\"entry.content\"></div>\n\n        <!-- Edit Mode -->\n        <div class=\"edit-container\" *ngIf=\"isEditing\">\n            <!-- Rich Text Toolbar -->\n            <div class=\"toolbar\">\n                <!-- Text Formatting -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('bold')\" title=\"Bold (Ctrl+B)\">\n                        <strong>B</strong>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('italic')\" title=\"Italic (Ctrl+I)\">\n                        <em>I</em>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('underline')\" title=\"Underline (Ctrl+U)\">\n                        <u>U</u>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('strikeThrough')\" title=\"Strikethrough\">\n                        <s>S</s>\n                    </button>\n                </div>\n\n                <!-- Headings -->\n                <div class=\"toolbar-group\">\n                    <div class=\"dropdown-container\">\n                        <button type=\"button\" class=\"toolbar-btn dropdown-btn\" (click)=\"toggleDropdown('headings')\" title=\"Headings\">\n                            <span>H</span>\n                            <ion-icon name=\"chevron-down-outline\"></ion-icon>\n                        </button>\n                        <div class=\"dropdown-menu\" *ngIf=\"activeDropdown === 'headings'\">\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'p'); closeDropdown()\">Normal text</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h1'); closeDropdown()\">Heading 1</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h2'); closeDropdown()\">Heading 2</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h3'); closeDropdown()\">Heading 3</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h4'); closeDropdown()\">Heading 4</button>\n                            <button type=\"button\" (click)=\"formatText('formatBlock', 'h5'); closeDropdown()\">Heading 5</button>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Lists -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(false)\" title=\"Bullet List\">\n                        <ion-icon name=\"list-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(true)\" title=\"Numbered List\">\n                        <ion-icon name=\"list-circle-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertCheckList()\" title=\"To-do List\">\n                        <ion-icon name=\"checkbox-outline\"></ion-icon>\n                    </button>\n                </div>\n\n                <!-- Alignment -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyLeft')\" title=\"Align Left\">\n                        <span class=\"align-icon\">⬅</span>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyCenter')\" title=\"Align Center\">\n                        <span class=\"align-icon\">⬌</span>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('justifyRight')\" title=\"Align Right\">\n                        <span class=\"align-icon\">➡</span>\n                    </button>\n                </div>\n\n                <!-- Special Elements -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertLink()\" title=\"Link\">\n                        <ion-icon name=\"link-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertCode()\" title=\"Code\">\n                        <ion-icon name=\"code-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertDivider()\" title=\"Divider\">\n                        <ion-icon name=\"remove-outline\"></ion-icon>\n                    </button>\n                </div>\n\n                <!-- Colors -->\n                <div class=\"toolbar-group\">\n                    <div class=\"dropdown-container\">\n                        <button type=\"button\" class=\"toolbar-btn dropdown-btn\" (click)=\"toggleDropdown('colors')\" title=\"Text Color\">\n                            <ion-icon name=\"color-palette-outline\"></ion-icon>\n                        </button>\n                        <div class=\"dropdown-menu color-menu\" *ngIf=\"activeDropdown === 'colors'\">\n                            <div class=\"color-section\">\n                                <span class=\"color-label\">Text Color</span>\n                                <div class=\"color-grid\">\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('default'); closeDropdown()\" style=\"background: #ffffff\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ff453a'); closeDropdown()\" style=\"background: #ff453a\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ff9500'); closeDropdown()\" style=\"background: #ff9500\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#ffcc02'); closeDropdown()\" style=\"background: #ffcc02\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#30d158'); closeDropdown()\" style=\"background: #30d158\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#007aff'); closeDropdown()\" style=\"background: #007aff\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setTextColor('#af52de'); closeDropdown()\" style=\"background: #af52de\"></button>\n                                </div>\n                            </div>\n                            <div class=\"color-section\">\n                                <span class=\"color-label\">Background</span>\n                                <div class=\"color-grid\">\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('default'); closeDropdown()\" style=\"background: transparent; border: 1px solid #666\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ff453a20'); closeDropdown()\" style=\"background: #ff453a20\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ff950020'); closeDropdown()\" style=\"background: #ff950020\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#ffcc0220'); closeDropdown()\" style=\"background: #ffcc0220\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#30d15820'); closeDropdown()\" style=\"background: #30d15820\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#007aff20'); closeDropdown()\" style=\"background: #007aff20\"></button>\n                                    <button type=\"button\" class=\"color-btn\" (click)=\"setBackgroundColor('#af52de20'); closeDropdown()\" style=\"background: #af52de20\"></button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- More Options -->\n                <div class=\"toolbar-group\">\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('undo')\" title=\"Undo (Ctrl+Z)\">\n                        <ion-icon name=\"arrow-undo-outline\"></ion-icon>\n                    </button>\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('redo')\" title=\"Redo (Ctrl+Y)\">\n                        <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n                    </button>\n                </div>\n            </div>\n\n            <!-- Rich Text Editor -->\n            <div\n                class=\"rich-editor\"\n                contenteditable=\"true\"\n                (input)=\"onContentInput($event)\"\n                (blur)=\"onContentBlur($event)\"\n                #richEditor\n                placeholder=\"Write your journal entry here...\">\n            </div>\n\n            <!-- Action Buttons -->\n            <div class=\"edit-actions\">\n                <button class=\"cancel-btn\" (click)=\"cancelEditing()\">Cancel</button>\n                <button class=\"save-btn\" (click)=\"saveEntry()\">Save</button>\n            </div>\n        </div>\n    </div>\n\n    <!-- Loading State -->\n    <div class=\"loading-container\" *ngIf=\"isLoading\">\n        <ion-spinner></ion-spinner>\n        <p>Loading journal entry...</p>\n    </div>\n</div>\n\n<!-- Navigation -->\n<app-navigation></app-navigation>\n"], "mappings": ";;AAEA,SAASA,WAAW,QAAyB,gBAAgB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,mBAAmB,QAAQ,qDAAqD;;;;;;;;;;;ICEzEC,EAAA,CAAAC,cAAA,iBAA8E;IAAzBD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACzET,EAAA,CAAAU,SAAA,mBAA2C;IAC/CV,EAAA,CAAAW,YAAA,EAAS;;;;;;IACTX,EAAA,CAAAC,cAAA,iBAA+E;IAAxBD,EAAA,CAAAE,UAAA,mBAAAU,iEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,WAAA,EAAa;IAAA,EAAC;IAC1Ed,EAAA,CAAAU,SAAA,mBAA0C;IAC9CV,EAAA,CAAAW,YAAA,EAAS;;;;;IAObX,EADJ,CAAAC,cAAA,cAA0C,cACT;IAAAD,EAAA,CAAAe,MAAA,GAAoC;IAAAf,EAAA,CAAAW,YAAA,EAAM;IACvEX,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAe,MAAA,GAAsC;;IAClEf,EADkE,CAAAW,YAAA,EAAM,EAClE;;;;IAF2BX,EAAA,CAAAgB,SAAA,GAAoC;IAApChB,EAAA,CAAAiB,kBAAA,kBAAAX,MAAA,CAAAY,KAAA,CAAAC,oBAAA,MAAoC;IACzCnB,EAAA,CAAAgB,SAAA,GAAsC;IAAtChB,EAAA,CAAAoB,iBAAA,CAAApB,EAAA,CAAAqB,WAAA,OAAAf,MAAA,CAAAY,KAAA,CAAAI,UAAA,YAAsC;;;;;IAIlEtB,EAAA,CAAAU,SAAA,cAA2F;;;;IAAlCV,EAAA,CAAAuB,UAAA,cAAAjB,MAAA,CAAAY,KAAA,CAAAM,OAAA,EAAAxB,EAAA,CAAAyB,cAAA,CAA2B;;;;;;IA8BhEzB,EADJ,CAAAC,cAAA,cAAiE,iBACmB;IAA1DD,EAAA,CAAAE,UAAA,mBAAAwB,4EAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,GAAG,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,kBAAW;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACpGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA4B,4EAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA6B,4EAAA;MAAA/B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA8B,4EAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,gBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,iBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAA+B,4EAAA;MAAAjC,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,iBAAS;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACnGX,EAAA,CAAAC,cAAA,kBAAiF;IAA3DD,EAAA,CAAAE,UAAA,mBAAAgC,6EAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAsB,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;MAAA,OAAA5B,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAC7B,EAAA,CAAAe,MAAA,iBAAS;IAC9Ff,EAD8F,CAAAW,YAAA,EAAS,EACjG;;;;;;IAmDEX,EAFR,CAAAC,cAAA,cAA0E,cAC3C,eACG;IAAAD,EAAA,CAAAe,MAAA,iBAAU;IAAAf,EAAA,CAAAW,YAAA,EAAO;IAEvCX,EADJ,CAAAC,cAAA,cAAwB,iBACmG;IAA/ED,EAAA,CAAAE,UAAA,mBAAAiC,4EAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAoC,4EAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAqC,4EAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAsC,4EAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,iBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAuC,4EAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,kBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAwC,6EAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA6B7B,EAAA,CAAAW,YAAA,EAAS;IAChIX,EAAA,CAAAC,cAAA,kBAAuH;IAA/ED,EAAA,CAAAE,UAAA,mBAAAyC,6EAAA;MAAA3C,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAA+B,YAAA,CAAa,SAAS,CAAC;MAAA,OAAArC,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAElG7B,EAF+H,CAAAW,YAAA,EAAS,EAC9H,EACJ;IAEFX,EADJ,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAe,MAAA,kBAAU;IAAAf,EAAA,CAAAW,YAAA,EAAO;IAEvCX,EADJ,CAAAC,cAAA,eAAwB,kBACqI;IAAjHD,EAAA,CAAAE,UAAA,mBAAA0C,6EAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,SAAS,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAAyD7B,EAAA,CAAAW,YAAA,EAAS;IAClKX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA4C,6EAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA6C,6EAAA;MAAA/C,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA8C,6EAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAA+C,6EAAA;MAAAjD,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAAgD,6EAAA;MAAAlD,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAA+B7B,EAAA,CAAAW,YAAA,EAAS;IAC1IX,EAAA,CAAAC,cAAA,kBAAiI;IAAzFD,EAAA,CAAAE,UAAA,mBAAAiD,6EAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAASD,MAAA,CAAAuC,kBAAA,CAAmB,WAAW,CAAC;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAEF,MAAA,CAAAuB,aAAA,EAAe;IAAA,EAAC;IAG9G7B,EAH6I,CAAAW,YAAA,EAAS,EACxI,EACJ,EACJ;;;;;;IAtGVX,EALZ,CAAAC,cAAA,cAA8C,cAErB,cAEU,iBACsE;IAAnDD,EAAA,CAAAE,UAAA,mBAAAkD,qEAAA;MAAApD,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAe,MAAA,QAAC;IACbf,EADa,CAAAW,YAAA,EAAS,EACb;IACTX,EAAA,CAAAC,cAAA,iBAAiG;IAAvDD,EAAA,CAAAE,UAAA,mBAAAoD,qEAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,QAAQ,CAAC;IAAA,EAAC;IACpE5B,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAe,MAAA,QAAC;IACTf,EADS,CAAAW,YAAA,EAAK,EACL;IACTX,EAAA,CAAAC,cAAA,iBAAuG;IAA7DD,EAAA,CAAAE,UAAA,mBAAAqD,qEAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IACvE5B,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAe,MAAA,SAAC;IACRf,EADQ,CAAAW,YAAA,EAAI,EACH;IACTX,EAAA,CAAAC,cAAA,kBAAsG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAsD,sEAAA;MAAAxD,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAC3E5B,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAe,MAAA,SAAC;IAEZf,EAFY,CAAAW,YAAA,EAAI,EACH,EACP;IAKEX,EAFR,CAAAC,cAAA,eAA2B,eACS,kBACiF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAuD,sEAAA;MAAAzD,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoD,cAAA,CAAe,UAAU,CAAC;IAAA,EAAC;IACvF1D,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAe,MAAA,SAAC;IAAAf,EAAA,CAAAW,YAAA,EAAO;IACdX,EAAA,CAAAU,SAAA,oBAAiD;IACrDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAA2D,UAAA,KAAAC,mDAAA,mBAAiE;IASzE5D,EADI,CAAAW,YAAA,EAAM,EACJ;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACmE;IAAhDD,EAAA,CAAAE,UAAA,mBAAA2D,sEAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,UAAA,CAAW,KAAK,CAAC;IAAA,EAAC;IACjE9D,EAAA,CAAAU,SAAA,oBAAyC;IAC7CV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA2F;IAAjDD,EAAA,CAAAE,UAAA,mBAAA6D,sEAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,UAAA,CAAW,IAAI,CAAC;IAAA,EAAC;IAChE9D,EAAA,CAAAU,SAAA,oBAAgD;IACpDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAyF;IAA/CD,EAAA,CAAAE,UAAA,mBAAA8D,sEAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2D,eAAA,EAAiB;IAAA,EAAC;IACjEjE,EAAA,CAAAU,SAAA,oBAA6C;IAErDV,EADI,CAAAW,YAAA,EAAS,EACP;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBAC0E;IAAvDD,EAAA,CAAAE,UAAA,mBAAAgE,sEAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,aAAa,CAAC;IAAA,EAAC;IACzE5B,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAe,MAAA,cAAC;IAC9Bf,EAD8B,CAAAW,YAAA,EAAO,EAC5B;IACTX,EAAA,CAAAC,cAAA,kBAAqG;IAA3DD,EAAA,CAAAE,UAAA,mBAAAiE,sEAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAC3E5B,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAe,MAAA,cAAC;IAC9Bf,EAD8B,CAAAW,YAAA,EAAO,EAC5B;IACTX,EAAA,CAAAC,cAAA,kBAAmG;IAAzDD,EAAA,CAAAE,UAAA,mBAAAkE,sEAAA;MAAApE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,cAAc,CAAC;IAAA,EAAC;IAC1E5B,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAe,MAAA,cAAC;IAElCf,EAFkC,CAAAW,YAAA,EAAO,EAC5B,EACP;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACuD;IAApCD,EAAA,CAAAE,UAAA,mBAAAmE,sEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgE,UAAA,EAAY;IAAA,EAAC;IAC5DtE,EAAA,CAAAU,SAAA,oBAAyC;IAC7CV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA8E;IAApCD,EAAA,CAAAE,UAAA,mBAAAqE,sEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkE,UAAA,EAAY;IAAA,EAAC;IAC5DxE,EAAA,CAAAU,SAAA,oBAAyC;IAC7CV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAAoF;IAA1CD,EAAA,CAAAE,UAAA,mBAAAuE,sEAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoE,aAAA,EAAe;IAAA,EAAC;IAC/D1E,EAAA,CAAAU,SAAA,oBAA2C;IAEnDV,EADI,CAAAW,YAAA,EAAS,EACP;IAKEX,EAFR,CAAAC,cAAA,eAA2B,eACS,kBACiF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAyE,sEAAA;MAAA3E,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoD,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IACrF1D,EAAA,CAAAU,SAAA,oBAAkD;IACtDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAA2D,UAAA,KAAAiB,mDAAA,mBAA0E;IA2BlF5E,EADI,CAAAW,YAAA,EAAM,EACJ;IAIFX,EADJ,CAAAC,cAAA,eAA2B,kBACsE;IAAnDD,EAAA,CAAAE,UAAA,mBAAA2E,sEAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAU,SAAA,oBAA+C;IACnDV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAC,cAAA,kBAA6F;IAAnDD,EAAA,CAAAE,UAAA,mBAAA4E,sEAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE5B,EAAA,CAAAU,SAAA,oBAA+C;IAG3DV,EAFQ,CAAAW,YAAA,EAAS,EACP,EACJ;IAGNX,EAAA,CAAAC,cAAA,kBAMmD;IAF/CD,EADA,CAAAE,UAAA,mBAAA6E,mEAAAC,MAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2E,cAAA,CAAAD,MAAA,CAAsB;IAAA,EAAC,kBAAAE,kEAAAF,MAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACxBF,MAAA,CAAA6E,aAAA,CAAAH,MAAA,CAAqB;IAAA,EAAC;IAGlChF,EAAA,CAAAW,YAAA,EAAM;IAIFX,EADJ,CAAAC,cAAA,eAA0B,kBAC+B;IAA1BD,EAAA,CAAAE,UAAA,mBAAAkF,sEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,aAAA,EAAe;IAAA,EAAC;IAACrF,EAAA,CAAAe,MAAA,cAAM;IAAAf,EAAA,CAAAW,YAAA,EAAS;IACpEX,EAAA,CAAAC,cAAA,kBAA+C;IAAtBD,EAAA,CAAAE,UAAA,mBAAAoF,sEAAA;MAAAtF,EAAA,CAAAI,aAAA,CAAAiD,GAAA;MAAA,MAAA/C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiF,SAAA,EAAW;IAAA,EAAC;IAACvF,EAAA,CAAAe,MAAA,YAAI;IAE3Df,EAF2D,CAAAW,YAAA,EAAS,EAC1D,EACJ;;;;IA/GsCX,EAAA,CAAAgB,SAAA,IAAmC;IAAnChB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkF,cAAA,gBAAmC;IAwDxBxF,EAAA,CAAAgB,SAAA,IAAiC;IAAjChB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAkF,cAAA,cAAiC;;;;;IA5F5FxF,EAAA,CAAAC,cAAA,cAAwC;IAUpCD,EATA,CAAA2D,UAAA,IAAA8B,4CAAA,kBAA0C,IAAAC,4CAAA,kBAM2C,IAAAC,4CAAA,mBAGvC;IA0IlD3F,EAAA,CAAAW,YAAA,EAAM;;;;IAnJ2BX,EAAA,CAAAgB,SAAA,EAAW;IAAXhB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAY,KAAA,CAAW;IAMVlB,EAAA,CAAAgB,SAAA,EAAyB;IAAzBhB,EAAA,CAAAuB,UAAA,UAAAjB,MAAA,CAAAsF,SAAA,IAAAtF,MAAA,CAAAY,KAAA,CAAyB;IAG1BlB,EAAA,CAAAgB,SAAA,EAAe;IAAfhB,EAAA,CAAAuB,UAAA,SAAAjB,MAAA,CAAAsF,SAAA,CAAe;;;;;IA6IhD5F,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAU,SAAA,kBAA2B;IAC3BV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAe,MAAA,+BAAwB;IAC/Bf,EAD+B,CAAAW,YAAA,EAAI,EAC7B;;;AD3JV,OAAM,MAAOkF,sBAAsB;EAWjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,eAAgC,EAChCC,SAAuB;IAJvB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IAbnB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAnF,KAAK,GAA4B,IAAI;IACrC,KAAA0E,SAAS,GAAY,KAAK;IAC1B,KAAAU,WAAW,GAAW,EAAE;IACxB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAf,cAAc,GAAkB,IAAI;EAQjC;EAEHgB,QAAQA,CAAA;IACN,IAAI,CAACJ,MAAM,GAAG,IAAI,CAACL,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;IAC9D,IAAI,CAACN,OAAO,GAAG,IAAI,CAACN,KAAK,CAACU,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;IAEhE,IAAI,IAAI,CAACP,MAAM,IAAI,IAAI,CAACC,OAAO,EAAE;MAC/B,IAAI,CAACO,gBAAgB,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;IAClC;EACF;EAEAC,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAClB,SAAS,IAAI,IAAI,CAACmB,UAAU,EAAE;MACrC,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACX,WAAW;IAC5D;IAEA;IACAY,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE7E;IACAH,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;EAEAE,WAAWA,CAAA;IACT;IACAL,QAAQ,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACJ,uBAAuB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChFH,QAAQ,CAACM,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3E;EAEAT,gBAAgBA,CAAA;IACd,IAAI,CAACL,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACN,WAAW,CAACwB,iBAAiB,CAAC,IAAI,CAACrB,MAAM,CAAC,CAACsB,SAAS,CAACC,OAAO,IAAG;MAClE,IAAI,CAACzG,KAAK,GAAGyG,OAAO,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK,IAAI,CAACzB,OAAO,CAAC,IAAI,IAAI;MAC7D,IAAI,CAACE,SAAS,GAAG,KAAK;MAEtB,IAAI,CAAC,IAAI,CAACrF,KAAK,EAAE;QACf,IAAI,CAAC6G,SAAS,CAAC,yBAAyB,CAAC;QACzC,IAAI,CAAC/B,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACT,MAAM,CAAC,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEA3F,YAAYA,CAAA;IACV,IAAI,IAAI,CAACS,KAAK,EAAE;MACd,IAAI,CAACoF,WAAW,GAAG,IAAI,CAACpF,KAAK,CAACM,OAAO;MACrC,IAAI,CAACoE,SAAS,GAAG,IAAI;MAErB;MACAoC,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACjB,UAAU,EAAE;UACnB,IAAI,CAACA,UAAU,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACX,WAAW;QAC5D;MACF,CAAC,EAAE,CAAC,CAAC;IACP;EACF;EAEAjB,aAAaA,CAAA;IACX,IAAI,CAACO,SAAS,GAAG,KAAK;IACtB,IAAI,CAACU,WAAW,GAAG,EAAE;EACvB;EAEMf,SAASA,CAAA;IAAA,IAAA0C,KAAA;IAAA,OAAAC,iBAAA;MACb,IAAI,CAACD,KAAI,CAAC/G,KAAK,IAAI,CAAC+G,KAAI,CAAC3B,WAAW,CAAC6B,IAAI,EAAE,EAAE;MAE7C,IAAI;QACF,MAAMF,KAAI,CAAChC,WAAW,CAACmC,kBAAkB,CAACH,KAAI,CAAC/G,KAAK,CAAC4G,EAAG,EAAE;UAAEtG,OAAO,EAAEyG,KAAI,CAAC3B,WAAW,CAAC6B,IAAI;QAAE,CAAE,CAAC;QAC/FF,KAAI,CAAC/G,KAAK,CAACM,OAAO,GAAGyG,KAAI,CAAC3B,WAAW,CAAC6B,IAAI,EAAE;QAC5CF,KAAI,CAACrC,SAAS,GAAG,KAAK;QACtBqC,KAAI,CAAC3B,WAAW,GAAG,EAAE;QACrB2B,KAAI,CAACF,SAAS,CAAC,oCAAoC,CAAC;MACtD,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDJ,KAAI,CAACF,SAAS,CAAC,8BAA8B,CAAC;MAChD;IAAC;EACH;EAEMjH,WAAWA,CAAA;IAAA,IAAAyH,MAAA;IAAA,OAAAL,iBAAA;MACf,IAAI,CAACK,MAAI,CAACrH,KAAK,EAAE;MAEjB,MAAMsH,SAAS,GAAGC,OAAO,CAAC,mFAAmF,CAAC;MAC9G,IAAI,CAACD,SAAS,EAAE;MAEhB,IAAI;QACF,MAAMD,MAAI,CAACtC,WAAW,CAACyC,kBAAkB,CAACH,MAAI,CAACrH,KAAK,CAAC4G,EAAG,CAAC;QACzDS,MAAI,CAACR,SAAS,CAAC,oCAAoC,CAAC;QACpDQ,MAAI,CAACvC,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,EAAE0B,MAAI,CAACnC,MAAM,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDE,MAAI,CAACR,SAAS,CAAC,8BAA8B,CAAC;MAChD;IAAC;EACH;EAEAY,MAAMA,CAAA;IACJ,IAAI,CAAC3C,MAAM,CAACa,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACT,MAAM,CAAC,CAAC;EAC/C;EAEc2B,SAASA,CAACa,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MACrC,MAAMY,KAAK,SAASD,MAAI,CAAC3C,eAAe,CAAC6C,MAAM,CAAC;QAC9CH,OAAO;QACPI,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;OACX,CAAC;MACFH,KAAK,CAACI,OAAO,EAAE;IAAC;EAClB;EAEAC,WAAWA,CAAC3H,OAAe;IACzB,OAAO,IAAI,CAAC2E,SAAS,CAACiD,uBAAuB,CAAC5H,OAAO,CAAC;EACxD;EAEA;EACAI,UAAUA,CAACyH,OAAe,EAAEC,KAAc;IACxCpC,QAAQ,CAACqC,WAAW,CAACF,OAAO,EAAE,KAAK,EAAEC,KAAK,CAAC;EAC7C;EAEAxF,UAAUA,CAAC0F,OAAA,GAAmB,KAAK;IACjC,MAAMH,OAAO,GAAGG,OAAO,GAAG,mBAAmB,GAAG,qBAAqB;IACrEtC,QAAQ,CAACqC,WAAW,CAACF,OAAO,EAAE,KAAK,CAAC;EACtC;EAEApE,cAAcA,CAACwE,KAAU;IACvB,IAAI,CAACnD,WAAW,GAAGmD,KAAK,CAACC,MAAM,CAACzC,SAAS;EAC3C;EAEA9B,aAAaA,CAACsE,KAAU;IACtB,IAAI,CAACnD,WAAW,GAAGmD,KAAK,CAACC,MAAM,CAACzC,SAAS;EAC3C;EAEA;EACAvD,cAAcA,CAACiG,QAAgB;IAC7B,IAAI,CAACnE,cAAc,GAAG,IAAI,CAACA,cAAc,KAAKmE,QAAQ,GAAG,IAAI,GAAGA,QAAQ;EAC1E;EAEA9H,aAAaA,CAAA;IACX,IAAI,CAAC2D,cAAc,GAAG,IAAI;EAC5B;EAEA;EACAvB,eAAeA,CAAA;IACb,MAAM2F,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMC,aAAa,GAAGhD,QAAQ,CAACiD,aAAa,CAAC,KAAK,CAAC;MACnDD,aAAa,CAACE,SAAS,GAAG,gBAAgB;MAC1CF,aAAa,CAACjD,SAAS,GAAG,iGAAiG;MAE3H+C,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACJ,aAAa,CAAC;MAE/B;MACA,MAAMK,QAAQ,GAAGL,aAAa,CAACM,aAAa,CAAC,MAAM,CAAC;MACpD,IAAID,QAAQ,EAAE;QACZ,MAAME,QAAQ,GAAGvD,QAAQ,CAACwD,WAAW,EAAE;QACvCD,QAAQ,CAACE,kBAAkB,CAACJ,QAAQ,CAAC;QACrCX,SAAS,CAACgB,eAAe,EAAE;QAC3BhB,SAAS,CAACiB,QAAQ,CAACJ,QAAQ,CAAC;MAC9B;IACF;EACF;EAEAnG,UAAUA,CAAA;IACR,MAAMsF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMe,YAAY,GAAGlB,SAAS,CAACmB,QAAQ,EAAE;MACzC,MAAMC,GAAG,GAAGC,MAAM,CAAC,YAAY,EAAE,UAAU,CAAC;MAE5C,IAAID,GAAG,IAAIA,GAAG,CAAC7C,IAAI,EAAE,EAAE;QACrB,MAAM+C,IAAI,GAAGhE,QAAQ,CAACiD,aAAa,CAAC,GAAG,CAAC;QACxCe,IAAI,CAACC,IAAI,GAAGH,GAAG,CAAC7C,IAAI,EAAE;QACtB+C,IAAI,CAACxB,MAAM,GAAG,QAAQ;QACtBwB,IAAI,CAACE,GAAG,GAAG,qBAAqB;QAChCF,IAAI,CAACd,SAAS,GAAG,aAAa;QAC9Bc,IAAI,CAACG,WAAW,GAAGP,YAAY,IAAIE,GAAG,CAAC7C,IAAI,EAAE;QAE7C,MAAM6B,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;QACrCD,KAAK,CAACK,cAAc,EAAE;QACtBL,KAAK,CAACM,UAAU,CAACY,IAAI,CAAC;QAEtB;QACAlB,KAAK,CAACsB,aAAa,CAACJ,IAAI,CAAC;QACzBlB,KAAK,CAACuB,QAAQ,CAAC,IAAI,CAAC;QACpB3B,SAAS,CAACgB,eAAe,EAAE;QAC3BhB,SAAS,CAACiB,QAAQ,CAACb,KAAK,CAAC;MAC3B;IACF;EACF;EAEAxF,UAAUA,CAAA;IACR,MAAMoF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMe,YAAY,GAAGlB,SAAS,CAACmB,QAAQ,EAAE;MACzC,IAAID,YAAY,EAAE;QAChB;QACA,MAAMU,IAAI,GAAGtE,QAAQ,CAACiD,aAAa,CAAC,MAAM,CAAC;QAC3CqB,IAAI,CAACpB,SAAS,GAAG,aAAa;QAC9BoB,IAAI,CAACH,WAAW,GAAGP,YAAY;QAE/B,MAAMd,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;QACrCD,KAAK,CAACK,cAAc,EAAE;QACtBL,KAAK,CAACM,UAAU,CAACkB,IAAI,CAAC;MACxB,CAAC,MAAM;QACL;QACA,MAAMxB,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;QACrC,MAAMwB,SAAS,GAAGvE,QAAQ,CAACiD,aAAa,CAAC,KAAK,CAAC;QAC/CsB,SAAS,CAACrB,SAAS,GAAG,mBAAmB;QACzCqB,SAAS,CAACxE,SAAS,GAAG,uDAAuD;QAE7E+C,KAAK,CAACK,cAAc,EAAE;QACtBL,KAAK,CAACM,UAAU,CAACmB,SAAS,CAAC;QAE3B;QACA,MAAMC,WAAW,GAAGD,SAAS,CAACjB,aAAa,CAAC,MAAM,CAAC;QACnD,IAAIkB,WAAW,EAAE;UACf,MAAMjB,QAAQ,GAAGvD,QAAQ,CAACwD,WAAW,EAAE;UACvCD,QAAQ,CAACE,kBAAkB,CAACe,WAAW,CAAC;UACxC9B,SAAS,CAACgB,eAAe,EAAE;UAC3BhB,SAAS,CAACiB,QAAQ,CAACJ,QAAQ,CAAC;QAC9B;MACF;IACF;EACF;EAEA/F,aAAaA,CAAA;IACX,MAAMkF,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAM0B,OAAO,GAAGzE,QAAQ,CAACiD,aAAa,CAAC,IAAI,CAAC;MAC5CwB,OAAO,CAACvB,SAAS,GAAG,gBAAgB;MAEpCJ,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACqB,OAAO,CAAC;MAEzB;MACA3B,KAAK,CAACsB,aAAa,CAACK,OAAO,CAAC;MAC5B3B,KAAK,CAACuB,QAAQ,CAAC,IAAI,CAAC;MACpB3B,SAAS,CAACgB,eAAe,EAAE;MAC3BhB,SAAS,CAACiB,QAAQ,CAACb,KAAK,CAAC;IAC3B;EACF;EAEA3H,YAAYA,CAACuJ,KAAa;IACxB,IAAIA,KAAK,KAAK,SAAS,EAAE;MACvB,IAAI,CAAChK,UAAU,CAAC,cAAc,CAAC;MAC/B,IAAI,CAACA,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC;IACzC,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,CAAC,WAAW,EAAEgK,KAAK,CAAC;IACrC;EACF;EAEA/I,kBAAkBA,CAAC+I,KAAa;IAC9B,IAAIA,KAAK,KAAK,SAAS,EAAE;MACvB,IAAI,CAAChK,UAAU,CAAC,cAAc,CAAC;IACjC,CAAC,MAAM;MACL,IAAI,CAACA,UAAU,CAAC,WAAW,EAAEgK,KAAK,CAAC;IACrC;EACF;EAEA;EACAxE,uBAAuBA,CAACqC,KAAoB;IAC1C,IAAI,CAAC,IAAI,CAAC7D,SAAS,EAAE;IAErB,MAAMiG,MAAM,GAAGpC,KAAK,CAACqC,OAAO,IAAIrC,KAAK,CAACsC,OAAO;IAE7C,IAAIF,MAAM,EAAE;MACV,QAAQpC,KAAK,CAACuC,GAAG,CAACC,WAAW,EAAE;QAC7B,KAAK,GAAG;UACNxC,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAACtK,UAAU,CAAC,MAAM,CAAC;UACvB;QACF,KAAK,GAAG;UACN6H,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAACtK,UAAU,CAAC,QAAQ,CAAC;UACzB;QACF,KAAK,GAAG;UACN6H,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAACtK,UAAU,CAAC,WAAW,CAAC;UAC5B;QACF,KAAK,GAAG;UACN6H,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAACtK,UAAU,CAAC,MAAM,CAAC;UACvB;QACF,KAAK,GAAG;UACN6H,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAACtK,UAAU,CAAC,MAAM,CAAC;UACvB;QACF,KAAK,GAAG;UACN6H,KAAK,CAACyC,cAAc,EAAE;UACtB,IAAI,CAAC3G,SAAS,EAAE;UAChB;MACJ;IACF;IAEA;IACA,IAAIkE,KAAK,CAACuC,GAAG,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAACnK,aAAa,EAAE;IACtB;EACF;EAEA;EACAyF,kBAAkBA,CAACmC,KAAY;IAC7B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,IAAI,CAACA,MAAM,CAACyC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MAC1C,IAAI,CAACtK,aAAa,EAAE;IACtB;EACF;;0BArUWgE,sBAAsB;;mCAAtBA,uBAAsB,EAAA7F,EAAA,CAAAoM,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtM,EAAA,CAAAoM,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvM,EAAA,CAAAoM,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAzM,EAAA,CAAAoM,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAA3M,EAAA,CAAAoM,iBAAA,CAAAQ,EAAA,CAAAC,YAAA;AAAA;;QAAtBhH,uBAAsB;EAAAiH,SAAA;EAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MCdvBjN,EAHZ,CAAAC,cAAA,aAAuB,aACX,aACwB,gBACoB;MAAnBD,EAAA,CAAAE,UAAA,mBAAAiN,wDAAA;QAAA,OAASD,GAAA,CAAAvE,MAAA,EAAQ;MAAA,EAAC;MACvC3I,EAAA,CAAAU,SAAA,kBAAuC;MAC3CV,EAAA,CAAAW,YAAA,EAAS;MACTX,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAe,MAAA,oBAAa;MAAAf,EAAA,CAAAW,YAAA,EAAK;MACtBX,EAAA,CAAAC,cAAA,aAA4B;MAIxBD,EAHA,CAAA2D,UAAA,IAAAyJ,wCAAA,oBAA8E,IAAAC,wCAAA,oBAGC;MAK3FrN,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACD;MAyJTX,EAvJA,CAAA2D,UAAA,KAAA2J,sCAAA,iBAAwC,KAAAC,sCAAA,iBAuJS;MAIrDvN,EAAA,CAAAW,YAAA,EAAM;MAGNX,EAAA,CAAAU,SAAA,sBAAiC;;;MAxKSV,EAAA,CAAAgB,SAAA,GAAyB;MAAzBhB,EAAA,CAAAuB,UAAA,UAAA2L,GAAA,CAAAtH,SAAA,IAAAsH,GAAA,CAAAhM,KAAA,CAAyB;MAGvBlB,EAAA,CAAAgB,SAAA,EAAyB;MAAzBhB,EAAA,CAAAuB,UAAA,UAAA2L,GAAA,CAAAtH,SAAA,IAAAsH,GAAA,CAAAhM,KAAA,CAAyB;MAO3ClB,EAAA,CAAAgB,SAAA,EAAgB;MAAhBhB,EAAA,CAAAuB,UAAA,UAAA2L,GAAA,CAAA3G,SAAA,CAAgB;MAuJNvG,EAAA,CAAAgB,SAAA,EAAe;MAAfhB,EAAA,CAAAuB,UAAA,SAAA2L,GAAA,CAAA3G,SAAA,CAAe;;;iBD1JvC3G,WAAW,EAAA8M,EAAA,CAAAc,OAAA,EAAAd,EAAA,CAAAe,UAAA,EAAE5N,YAAY,EAAA6N,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,QAAA,EAAE9N,WAAW,EAAEC,mBAAmB;EAAA8N,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}