<div class="container">
    <header>
        <div class="header-content">
            <button class="back-btn" (click)="goBack()">
                <ion-icon name="arrow-back"></ion-icon>
            </button>
            <h1>Journal Entry</h1>
            <div class="header-actions">
                <button class="edit-btn" *ngIf="!isEditing && entry" (click)="startEditing()">
                    <ion-icon name="create-outline"></ion-icon>
                </button>
                <button class="delete-btn" *ngIf="!isEditing && entry" (click)="deleteEntry()">
                    <ion-icon name="trash-outline"></ion-icon>
                </button>
            </div>
        </div>
    </header>

    <div class="content" *ngIf="!isLoading">
        <div class="milestone-info" *ngIf="entry">
            <div class="milestone-badge">🎯 {{ entry.milestone_percentage }}%</div>
            <div class="entry-date">{{ entry.created_at | date:'medium' }}</div>
        </div>

        <!-- View Mode -->
        <div class="journal-content" *ngIf="!isEditing && entry" [innerHTML]="entry.content"></div>

        <!-- Edit Mode -->
        <div class="edit-container" *ngIf="isEditing">
            <!-- Rich Text Toolbar -->
            <div class="toolbar">
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('bold')" title="Bold">
                        <ion-icon name="text-outline"></ion-icon>
                        <strong>B</strong>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('italic')" title="Italic">
                        <em>I</em>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('underline')" title="Underline">
                        <u>U</u>
                    </button>
                </div>

                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('formatBlock', 'h3')" title="Heading">
                        <strong>H</strong>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertList(false)" title="Bullet List">
                        <ion-icon name="list-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertList(true)" title="Numbered List">
                        <ion-icon name="list-circle-outline"></ion-icon>
                    </button>
                </div>

                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('justifyLeft')" title="Align Left">
                        <ion-icon name="text-left-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('justifyCenter')" title="Align Center">
                        <ion-icon name="text-center-outline"></ion-icon>
                    </button>
                </div>
            </div>

            <!-- Rich Text Editor -->
            <div
                class="rich-editor"
                contenteditable="true"
                (input)="onContentInput($event)"
                (blur)="onContentBlur($event)"
                #richEditor
                placeholder="Write your journal entry here...">
            </div>

            <!-- Action Buttons -->
            <div class="edit-actions">
                <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
                <button class="save-btn" (click)="saveEntry()">Save</button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div class="loading-container" *ngIf="isLoading">
        <ion-spinner></ion-spinner>
        <p>Loading journal entry...</p>
    </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
