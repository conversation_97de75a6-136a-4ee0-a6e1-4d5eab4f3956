<div class="container">
    <header>
        <div class="header-content">
            <button class="back-btn" (click)="goBack()">
                <ion-icon name="arrow-back"></ion-icon>
            </button>
            <h1>Journal Entry</h1>
            <div class="header-actions">
                <button class="edit-btn" *ngIf="!isEditing && entry" (click)="startEditing()">
                    <ion-icon name="create-outline"></ion-icon>
                </button>
                <button class="delete-btn" *ngIf="!isEditing && entry" (click)="deleteEntry()">
                    <ion-icon name="trash-outline"></ion-icon>
                </button>
            </div>
        </div>
    </header>

    <div class="content" *ngIf="!isLoading">
        <div class="milestone-info" *ngIf="entry">
            <div class="milestone-badge">🎯 {{ entry.milestone_percentage }}%</div>
            <div class="entry-date">{{ entry.created_at | date:'medium' }}</div>
        </div>

        <!-- View Mode -->
        <div class="journal-content" *ngIf="!isEditing && entry" [innerHTML]="getSafeHtml(entry.content)"></div>

        <!-- Edit Mode -->
        <div class="edit-container" *ngIf="isEditing">
            <!-- Rich Text Toolbar -->
            <div class="toolbar">
                <!-- Text Formatting -->
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('bold')" title="Bold (Ctrl+B)">
                        <strong>B</strong>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('italic')" title="Italic (Ctrl+I)">
                        <em>I</em>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('underline')" title="Underline (Ctrl+U)">
                        <u>U</u>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('strikeThrough')" title="Strikethrough">
                        <s>S</s>
                    </button>
                </div>

                <!-- Headings -->
                <div class="toolbar-group">
                    <div class="dropdown-container">
                        <button type="button" class="toolbar-btn dropdown-btn" (click)="toggleDropdown('headings')" title="Headings">
                            <span>H</span>
                            <ion-icon name="chevron-down-outline"></ion-icon>
                        </button>
                        <div class="dropdown-menu" *ngIf="activeDropdown === 'headings'">
                            <button type="button" (click)="formatText('formatBlock', 'p'); closeDropdown()">Normal text</button>
                            <button type="button" (click)="formatText('formatBlock', 'h1'); closeDropdown()">Heading 1</button>
                            <button type="button" (click)="formatText('formatBlock', 'h2'); closeDropdown()">Heading 2</button>
                            <button type="button" (click)="formatText('formatBlock', 'h3'); closeDropdown()">Heading 3</button>
                            <button type="button" (click)="formatText('formatBlock', 'h4'); closeDropdown()">Heading 4</button>
                            <button type="button" (click)="formatText('formatBlock', 'h5'); closeDropdown()">Heading 5</button>
                        </div>
                    </div>
                </div>

                <!-- Lists -->
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="insertList(false)" title="Bullet List">
                        <ion-icon name="list-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertList(true)" title="Numbered List">
                        <ion-icon name="list-circle-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertCheckList()" title="To-do List">
                        <ion-icon name="checkbox-outline"></ion-icon>
                    </button>
                </div>

                <!-- Alignment -->
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('justifyLeft')" title="Align Left">
                        <span class="align-icon">⬅</span>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('justifyCenter')" title="Align Center">
                        <span class="align-icon">⬌</span>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('justifyRight')" title="Align Right">
                        <span class="align-icon">➡</span>
                    </button>
                </div>

                <!-- Special Elements -->
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="insertLink()" title="Link">
                        <ion-icon name="link-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertCode()" title="Code">
                        <ion-icon name="code-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertDivider()" title="Divider">
                        <ion-icon name="remove-outline"></ion-icon>
                    </button>
                </div>

                <!-- Colors -->
                <div class="toolbar-group">
                    <div class="dropdown-container">
                        <button type="button" class="toolbar-btn dropdown-btn" (click)="toggleDropdown('colors')" title="Text Color">
                            <ion-icon name="color-palette-outline"></ion-icon>
                        </button>
                        <div class="dropdown-menu color-menu" *ngIf="activeDropdown === 'colors'">
                            <div class="color-section">
                                <span class="color-label">Text Color</span>
                                <div class="color-grid">
                                    <button type="button" class="color-btn" (click)="setTextColor('default'); closeDropdown()" style="background: #ffffff"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#ff453a'); closeDropdown()" style="background: #ff453a"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#ff9500'); closeDropdown()" style="background: #ff9500"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#ffcc02'); closeDropdown()" style="background: #ffcc02"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#30d158'); closeDropdown()" style="background: #30d158"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#007aff'); closeDropdown()" style="background: #007aff"></button>
                                    <button type="button" class="color-btn" (click)="setTextColor('#af52de'); closeDropdown()" style="background: #af52de"></button>
                                </div>
                            </div>
                            <div class="color-section">
                                <span class="color-label">Background</span>
                                <div class="color-grid">
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('default'); closeDropdown()" style="background: transparent; border: 1px solid #666"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#ff453a20'); closeDropdown()" style="background: #ff453a20"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#ff950020'); closeDropdown()" style="background: #ff950020"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#ffcc0220'); closeDropdown()" style="background: #ffcc0220"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#30d15820'); closeDropdown()" style="background: #30d15820"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#007aff20'); closeDropdown()" style="background: #007aff20"></button>
                                    <button type="button" class="color-btn" (click)="setBackgroundColor('#af52de20'); closeDropdown()" style="background: #af52de20"></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- More Options -->
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('undo')" title="Undo (Ctrl+Z)">
                        <ion-icon name="arrow-undo-outline"></ion-icon>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('redo')" title="Redo (Ctrl+Y)">
                        <ion-icon name="arrow-redo-outline"></ion-icon>
                    </button>
                </div>
            </div>

            <!-- Rich Text Editor -->
            <div
                class="rich-editor"
                contenteditable="true"
                (input)="onContentInput($event)"
                (blur)="onContentBlur($event)"
                #richEditor
                placeholder="Write your journal entry here...">
            </div>

            <!-- Action Buttons -->
            <div class="edit-actions">
                <button class="cancel-btn" (click)="cancelEditing()">Cancel</button>
                <button class="save-btn" (click)="saveEntry()">Save</button>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div class="loading-container" *ngIf="isLoading">
        <ion-spinner></ion-spinner>
        <p>Loading journal entry...</p>
    </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
