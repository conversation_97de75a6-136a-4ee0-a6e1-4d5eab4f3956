{"ast": null, "code": "var _GoalListPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal } from '../../../models/goal.model';\nimport { combineLatest, map, of, switchMap, take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/goals\", a0];\nfunction GoalListPage_ng_container_15_a_1_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const goal_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"Micro goals: \", goal_r2.completedMicrogoals, \"/\", goal_r2.totalMicrogoals, \" \");\n  }\n}\nfunction GoalListPage_ng_container_15_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"div\", 48)(4, \"span\", 49);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 50);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 51);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 52);\n    i0.ɵɵelement(11, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 54)(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, GoalListPage_ng_container_15_a_1_span_15_Template, 2, 2, \"span\", 55);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const goal_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(10, _c0, goal_r2.id));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(goal_r2.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", goal_r2.progressPercent, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", goal_r2.progressPercent, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", goal_r2.current_value, \"/\", goal_r2.goal_value, \" \", goal_r2.goal_unit, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", goal_r2.totalMicrogoals > 0);\n  }\n}\nfunction GoalListPage_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GoalListPage_ng_container_15_a_1_Template, 16, 12, \"a\", 44);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.goals);\n  }\n}\nfunction GoalListPage_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 56);\n    i0.ɵɵtext(1, \"You have no goals yet.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class GoalListPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // Goals\n    this.goals = [];\n    // Add Goal Modal\n    this.showAddGoalModal = false;\n    this.newGoal = this.getEmptyGoal();\n    // Subscriptions\n    this.userSubscription = null;\n    this.supabaseService = inject(SupabaseService);\n    this.goalService = inject(GoalService);\n  }\n  ngOnInit() {\n    // Get the current user\n    this.userSubscription = this.supabaseService.currentUser$.pipe(take(1)).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.loadGoals();\n      }\n    });\n  }\n  ionViewWillEnter() {\n    // Reload goals data when the page is about to be displayed\n    if (this.userId) {\n      this.loadGoals();\n    }\n  }\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n  loadGoals() {\n    if (!this.userId) {\n      console.error('Cannot load goals: No user ID available');\n      return;\n    }\n    // Clear existing goals to show loading state\n    this.goals = [];\n    this.goalService.getGoals(this.userId).pipe(switchMap(goals => {\n      if (goals.length === 0) {\n        return of([]);\n      }\n      // Get microgoals for each goal\n      const goalObservables = goals.map(goal => this.goalService.getMicroGoals(goal.id).pipe(map(microgoals => {\n        const totalMicrogoals = microgoals.length;\n        const completedMicrogoals = microgoals.filter(m => m.completed).length;\n        const progressPercent = goal.goal_value > 0 ? Math.min(100, Math.round(goal.current_value / goal.goal_value * 100)) : 0;\n        return {\n          ...goal,\n          progressPercent,\n          totalMicrogoals,\n          completedMicrogoals\n        };\n      })));\n      return combineLatest(goalObservables);\n    })).subscribe({\n      next: goalsWithMicrogoals => {\n        this.goals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('GoalListPage: Error loading goals:', error);\n        this.goals = [];\n      }\n    });\n  }\n  openAddGoalModal(event) {\n    event.preventDefault();\n    this.showAddGoalModal = true;\n    this.newGoal = this.getEmptyGoal();\n  }\n  closeAddGoalModal() {\n    this.showAddGoalModal = false;\n  }\n  createGoal() {\n    if (!this.userId) {\n      console.error('Cannot create goal: No user ID available');\n      return;\n    }\n    if (!this.newGoal.name) {\n      console.error('Cannot create goal: Goal name is required');\n      return;\n    }\n    const goalToCreate = {\n      name: this.newGoal.name,\n      description: this.newGoal.description || '',\n      emoji: this.newGoal.emoji || '🎯',\n      goal_value: this.newGoal.goal_value || 0,\n      goal_unit: this.newGoal.goal_unit || 'count',\n      user_id: this.userId,\n      start_date: new Date(),\n      current_value: 0,\n      public: false\n    };\n    this.goalService.createGoal(goalToCreate).then(goalId => {\n      this.closeAddGoalModal();\n      this.loadGoals();\n    }).catch(error => {\n      console.error('Error creating goal:', error);\n    });\n  }\n  getEmptyGoal() {\n    return {\n      name: '',\n      description: '',\n      emoji: '🎯',\n      goal_value: 100,\n      goal_unit: 'count'\n    };\n  }\n}\n_GoalListPage = GoalListPage;\n_GoalListPage.ɵfac = function GoalListPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GoalListPage)();\n};\n_GoalListPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GoalListPage,\n  selectors: [[\"app-goal-list\"]],\n  decls: 84,\n  vars: 9,\n  consts: [[\"noGoals\", \"\"], [\"goalForm\", \"ngForm\"], [1, \"container\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [2, \"display\", \"flex\", \"justify-content\", \"space-between\", \"align-items\", \"center\", \"margin-bottom\", \"20px\"], [\"href\", \"#\", \"id\", \"add-goal-btn\", 1, \"add-quest-link\", 3, \"click\"], [1, \"add-quest-icon\"], [4, \"ngIf\", \"ngIfElse\"], [\"id\", \"add-goal-modal\", 1, \"modal\"], [1, \"modal-content\"], [1, \"close-modal\", 3, \"click\"], [3, \"ngSubmit\"], [2, \"display\", \"flex\", \"gap\", \"10px\"], [1, \"form-group\"], [\"type\", \"text\", \"id\", \"emoji\", \"name\", \"emoji\", \"value\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Enter goal name\", \"required\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"description\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Enter goal description\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-group\", \"goal-settings\"], [1, \"goal-inputs\"], [\"type\", \"number\", \"id\", \"goal_value\", \"name\", \"goal_value\", \"value\", \"100\", \"min\", \"1\", 3, \"ngModelChange\", \"ngModel\"], [\"id\", \"goal_unit\", \"name\", \"goal_unit\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"count\"], [\"value\", \"steps\"], [\"value\", \"m\"], [\"value\", \"km\"], [\"value\", \"sec\"], [\"value\", \"min\"], [\"value\", \"hr\"], [\"value\", \"days\"], [\"value\", \"weeks\"], [\"value\", \"months\"], [\"value\", \"years\"], [\"value\", \"Cal\"], [\"value\", \"g\"], [\"value\", \"mg\"], [\"value\", \"pages\"], [\"value\", \"books\"], [\"value\", \"%\"], [\"value\", \"\\u20AC\"], [\"value\", \"$\"], [\"value\", \"\\u00A3\"], [\"type\", \"submit\", 1, \"submit-btn\"], [\"class\", \"goal-card-link\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"goal-card-link\", 3, \"routerLink\"], [1, \"goal-card\"], [1, \"goal-header\"], [1, \"goal-title\"], [1, \"goal-emoji\"], [1, \"goal-name\"], [1, \"goal-percent\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"goal-value\"], [4, \"ngIf\"], [1, \"no-goals\"]],\n  template: function GoalListPage_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r1 = i0.ɵɵgetCurrentView();\n      i0.ɵɵelementStart(0, \"div\", 2)(1, \"header\")(2, \"div\", 3);\n      i0.ɵɵelement(3, \"img\", 4);\n      i0.ɵɵelementStart(4, \"span\");\n      i0.ɵɵtext(5, \"Upshift\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(6, \"h1\");\n      i0.ɵɵtext(7, \"Goals\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(8, \"div\", 5)(9, \"h2\");\n      i0.ɵɵtext(10, \"My Goals\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"a\", 6);\n      i0.ɵɵlistener(\"click\", function GoalListPage_Template_a_click_11_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.openAddGoalModal($event));\n      });\n      i0.ɵɵelementStart(12, \"span\", 7);\n      i0.ɵɵtext(13, \"+\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(14, \" Add Goal \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(15, GoalListPage_ng_container_15_Template, 2, 1, \"ng-container\", 8)(16, GoalListPage_ng_template_16_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"span\", 11);\n      i0.ɵɵlistener(\"click\", function GoalListPage_Template_span_click_20_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.closeAddGoalModal());\n      });\n      i0.ɵɵtext(21, \"\\u00D7\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"h2\");\n      i0.ɵɵtext(23, \"Add New Goal\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"form\", 12, 1);\n      i0.ɵɵlistener(\"ngSubmit\", function GoalListPage_Template_form_ngSubmit_24_listener() {\n        i0.ɵɵrestoreView(_r1);\n        return i0.ɵɵresetView(ctx.createGoal());\n      });\n      i0.ɵɵelementStart(26, \"div\", 13)(27, \"div\", 14)(28, \"input\", 15);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_28_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.emoji, $event) || (ctx.newGoal.emoji = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(29, \"div\", 14)(30, \"input\", 16);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_30_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.name, $event) || (ctx.newGoal.name = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\", 17);\n      i0.ɵɵtext(33, \"Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"textarea\", 18);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_textarea_ngModelChange_34_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.description, $event) || (ctx.newGoal.description = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(35, \"div\", 19)(36, \"label\");\n      i0.ɵɵtext(37, \"Goal Target\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"div\", 20)(39, \"input\", 21);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_input_ngModelChange_39_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.goal_value, $event) || (ctx.newGoal.goal_value = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(40, \"select\", 22);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalListPage_Template_select_ngModelChange_40_listener($event) {\n        i0.ɵɵrestoreView(_r1);\n        i0.ɵɵtwoWayBindingSet(ctx.newGoal.goal_unit, $event) || (ctx.newGoal.goal_unit = $event);\n        return i0.ɵɵresetView($event);\n      });\n      i0.ɵɵelementStart(41, \"option\", 23);\n      i0.ɵɵtext(42, \"count\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"option\", 24);\n      i0.ɵɵtext(44, \"steps\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"option\", 25);\n      i0.ɵɵtext(46, \"meters\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"option\", 26);\n      i0.ɵɵtext(48, \"kilometers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"option\", 27);\n      i0.ɵɵtext(50, \"seconds\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"option\", 28);\n      i0.ɵɵtext(52, \"minutes\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"option\", 29);\n      i0.ɵɵtext(54, \"hours\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"option\", 30);\n      i0.ɵɵtext(56, \"days\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"option\", 31);\n      i0.ɵɵtext(58, \"weeks\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(59, \"option\", 32);\n      i0.ɵɵtext(60, \"months\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"option\", 33);\n      i0.ɵɵtext(62, \"years\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"option\", 34);\n      i0.ɵɵtext(64, \"calories\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(65, \"option\", 35);\n      i0.ɵɵtext(66, \"grams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(67, \"option\", 36);\n      i0.ɵɵtext(68, \"milligrams\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(69, \"option\", 37);\n      i0.ɵɵtext(70, \"pages\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"option\", 38);\n      i0.ɵɵtext(72, \"books\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"option\", 39);\n      i0.ɵɵtext(74, \"percent\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(75, \"option\", 40);\n      i0.ɵɵtext(76, \"euros\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(77, \"option\", 41);\n      i0.ɵɵtext(78, \"dollars\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(79, \"option\", 42);\n      i0.ɵɵtext(80, \"pounds\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(81, \"button\", 43);\n      i0.ɵɵtext(82, \"Create Goal\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelement(83, \"app-navigation\");\n    }\n    if (rf & 2) {\n      const noGoals_r4 = i0.ɵɵreference(17);\n      i0.ɵɵadvance(15);\n      i0.ɵɵproperty(\"ngIf\", ctx.goals.length > 0)(\"ngIfElse\", noGoals_r4);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"display\", ctx.showAddGoalModal ? \"block\" : \"none\");\n      i0.ɵɵadvance(10);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.emoji);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.name);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.description);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.goal_value);\n      i0.ɵɵadvance();\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.newGoal.goal_unit);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent, EmojiInputDirective],\n  styles: [\"[_nghost-%COMP%] {\\n  --bg: #0c0c0f;\\n  --card: #121217;\\n  --pill: #1c1c1e;\\n  --text: #fff;\\n  --text-muted: #8e8e93;\\n  --accent: #4d7bff;\\n  --radius: 16px;\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n}\\n\\nbody.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  padding: 20px;\\n}\\n\\nh1[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n  margin-top: 10px;\\n}\\n\\n.description-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-desc[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 10px;\\n  flex: 1;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--accent);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 2px 5px;\\n  margin-left: 10px;\\n}\\n\\n.description-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.description-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n}\\n\\n.save-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-right: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #2c2c2e;\\n  color: white;\\n  border: none;\\n}\\n\\n.delete-goal-container[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  text-align: center;\\n  margin-bottom: 100px;\\n}\\n\\n.delete-goal-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 59, 48, 0.2);\\n  color: #FF3B30;\\n  border: 1px solid #FF3B30;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n}\\n\\n.progress-update[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-top: 10px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  font-weight: 600;\\n  border: none;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  cursor: pointer;\\n  margin-top: 10px;\\n}\\n\\n.microgoals-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.microgoals-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding-left: 0;\\n  margin-bottom: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 6px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 8px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.journal-section[_ngcontent-%COMP%] {\\n  background: var(--pill);\\n  padding: 16px;\\n  border-radius: var(--radius);\\n}\\n\\n.journal-entry[_ngcontent-%COMP%] {\\n  border-top: 1px solid #2c2c2e;\\n  padding-top: 10px;\\n  margin-top: 10px;\\n}\\n\\n.journal-entry[_ngcontent-%COMP%]:first-child {\\n  border-top: none;\\n  padding-top: 0;\\n  margin-top: 0;\\n}\\n\\n.milestone[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--accent);\\n}\\n\\n.journal-modal[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: var(--card);\\n  padding: 14px;\\n  border-radius: var(--radius);\\n  text-align: center;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-top: 10px;\\n  color: var(--accent);\\n  text-decoration: underline;\\n}\\n\\n[_ngcontent-%COMP%]:root {\\n  --bg: #0c0c0f;\\n  --card: #121217;\\n  --pill: #1c1c1e;\\n  --text: #fff;\\n  --text-muted: #8e8e93;\\n  --accent: #4d7bff;\\n  --radius: 16px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  padding: 20px;\\n}\\n\\n.top-date[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-muted);\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.goal-percent[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--accent);\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 12px;\\n}\\n\\n.goal-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--accent);\\n  text-decoration: none;\\n  display: inline-block;\\n}\\n\\n.goal-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.no-goals[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-muted);\\n  margin-top: 40px;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #ff4d4d;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  font-size: 16px;\\n  vertical-align: middle;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  color: red;\\n}\\n\\n.microgoal-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: var(--pill);\\n  padding: 10px 14px;\\n  margin-bottom: 8px;\\n  border-radius: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  flex: 1;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 18px;\\n  color: var(--accent);\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text-muted);\\n}\\n\\n.checkmark.checked[_ngcontent-%COMP%] {\\n  color: #4cd964;\\n}\\n\\n.microgoal-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: var(--text);\\n}\\n\\n.delete-form[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  color: #ff4d6d;\\n  cursor: pointer;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border-radius: 16px;\\n  padding: 20px;\\n  margin-top: 24px;\\n  text-align: center;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin-bottom: 12px;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  text-decoration: underline;\\n  color: var(--accent);\\n}\\n\\n.milestone-highlight[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 600;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 10px;\\n  padding: 10px;\\n  color: var(--text);\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background-color: var(--accent);\\n  border: none;\\n  padding: 10px 16px;\\n  color: white;\\n  font-weight: bold;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  transition: opacity 0.2s ease;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n\\n.save-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 0 16px 16px 0;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-radius: 16px 0 0 16px;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: inherit;\\n  display: block;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%]:hover   .goal-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 1px var(--accent-color);\\n  transition: box-shadow 0.2s;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.logo-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n}\\n\\n.back-link[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  text-decoration: none;\\n  margin-left: 2px;\\n  transition: color 0.2s ease;\\n  margin-bottom: 10px;\\n}\\n\\n.back-link[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n\\n.goal-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 1;\\n}\\n\\n.goal-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  width: 95%;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZ29hbHMvZ29hbC1saXN0L2dvYWwtbGlzdC5wYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EscUJBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7RUFDQSwyQkFBQTtFQUNBLHFCQUFBO0VBQ0EseUJBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUFDSjs7QUFHQTtFQUNJLHlDQUFBO0VBQ0Esd0JBQUE7RUFDQSxpQkFBQTtBQUFKOztBQUVBO0VBQ0ksU0FBQTtFQUNBLFVBQUE7RUFDQSxzQkFBQTtFQUNBLHdJQUFBO0FBQ0o7O0FBRUE7RUFDSSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0FBQ0o7O0FBQ0E7RUFDSSxlQUFBO0VBQ0EsZ0JBQUE7QUFFSjs7QUFDQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFFSjs7QUFBQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFHSjs7QUFBQTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUFHSjs7QUFBQTtFQUNJLFlBQUE7QUFHSjs7QUFBQTtFQUNJLGVBQUE7RUFDQSxnQkFBQTtBQUdKOztBQURBO0VBQ0ksdUJBQUE7RUFDQSxhQUFBO0VBQ0EsNEJBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0VBQ0EsZ0JBQUE7QUFJSjs7QUFEQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7QUFJSjs7QUFEQTtFQUNJLGVBQUE7RUFDQSx3QkFBQTtFQUNBLG1CQUFBO0VBQ0EsT0FBQTtBQUlKOztBQURBO0VBQ0ksZ0JBQUE7RUFDQSxZQUFBO0VBQ0Esb0JBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7QUFJSjs7QUFEQTtFQUNJLG1CQUFBO0FBSUo7O0FBREE7RUFDSSxXQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0VBQ0EsNEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZ0JBQUE7QUFJSjs7QUFEQTtFQUNJLGlCQUFBO0VBQ0EsNEJBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0FBSUo7O0FBREE7RUFDSSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0FBSUo7O0FBREE7RUFDSSxtQkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0FBSUo7O0FBREE7RUFDSSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0Esb0JBQUE7QUFJSjs7QUFEQTtFQUNJLGtDQUFBO0VBQ0EsY0FBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSw0QkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0FBSUo7O0FBREE7RUFDSSxtQkFBQTtBQUlKOztBQURBO0VBQ0ksWUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBSUo7O0FBREE7RUFDSSxZQUFBO0VBQ0EseUJBQUE7RUFDQSxrQ0FBQTtBQUlKOztBQURBO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsZUFBQTtBQUlKOztBQURBO0VBQ0ksV0FBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDRCQUFBO0VBQ0EsZ0JBQUE7QUFJSjs7QUFEQTtFQUNJLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsNEJBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFJSjs7QUFEQTtFQUNJLG1CQUFBO0FBSUo7O0FBREE7RUFDSSxnQkFBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtBQUlKOztBQURBO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0FBSUo7O0FBREE7RUFDSSxXQUFBO0VBQ0EsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0VBQ0EsNEJBQUE7RUFDQSxrQkFBQTtBQUlKOztBQURBO0VBQ0ksV0FBQTtBQUlKOztBQURBO0VBQ0ksdUJBQUE7RUFDQSxhQUFBO0VBQ0EsNEJBQUE7QUFJSjs7QUFEQTtFQUNJLDZCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQUlKOztBQURBO0VBQ0ksZ0JBQUE7RUFDQSxjQUFBO0VBQ0EsYUFBQTtBQUlKOztBQURBO0VBQ0ksZ0JBQUE7RUFDQSxvQkFBQTtBQUlKOztBQURBO0VBQ0ksZ0JBQUE7RUFDQSx1QkFBQTtFQUNBLGFBQUE7RUFDQSw0QkFBQTtFQUNBLGtCQUFBO0FBSUo7O0FBREE7RUFDSSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7RUFDQSwwQkFBQTtBQUlKOztBQUZBO0VBQ0ksYUFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsWUFBQTtFQUNBLHFCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBS0o7O0FBREE7RUFDSSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7QUFJSjs7QUFFQTtFQUNJLGVBQUE7RUFDQSxnQkFBQTtFQUNBLHdCQUFBO0FBQ0o7O0FBRUE7RUFDSSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtBQUNKOztBQUVBO0VBQ0ksdUJBQUE7RUFDQSxhQUFBO0VBQ0EsNEJBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0FBQ0o7O0FBRUE7RUFDSSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQ0o7O0FBRUE7RUFDSSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0FBQ0o7O0FBRUE7RUFDSSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtBQUNKOztBQUVBO0VBQ0ksWUFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0FBQ0o7O0FBRUE7RUFDSSxZQUFBO0VBQ0EseUJBQUE7RUFDQSxrQ0FBQTtBQUNKOztBQUVBO0VBQ0ksZUFBQTtFQUNBLHdCQUFBO0VBQ0EsbUJBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0VBQ0Esb0JBQUE7RUFDQSxxQkFBQTtFQUNBLHFCQUFBO0FBQ0o7O0FBRUE7RUFDSSwwQkFBQTtBQUNKOztBQUVBO0VBQ0ksa0JBQUE7RUFDQSx3QkFBQTtFQUNBLGdCQUFBO0FBQ0o7O0FBQ0E7RUFDSSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtFQUNBLHNCQUFBO0FBRUo7O0FBQ0E7RUFDSSxVQUFBO0FBRUo7O0FBQUE7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0FBR0o7O0FBQUE7RUFDSSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsT0FBQTtBQUdKOztBQUFBO0VBQ0ksZ0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7RUFDQSxvQkFBQTtBQUdKOztBQUFBO0VBQ0ksZUFBQTtFQUNBLHdCQUFBO0FBR0o7O0FBQUE7RUFDSSxjQUFBO0FBR0o7O0FBQUE7RUFDSSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtBQUdKOztBQUFBO0VBQ0ksaUJBQUE7QUFHSjs7QUFBQTtFQUNJLGdCQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQUdKOztBQUFBO0VBQ0ksNkJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGtCQUFBO0VBQ0EsNkJBQUE7QUFHSjs7QUFBQTtFQUNJLGVBQUE7RUFDQSxnQkFBQTtFQUNBLG1CQUFBO0FBR0o7O0FBQUE7RUFDSSxnQkFBQTtFQUNBLGVBQUE7RUFDQSwwQkFBQTtFQUNBLG9CQUFBO0FBR0o7O0FBQUE7RUFDSSxvQkFBQTtFQUNBLGdCQUFBO0FBR0o7O0FBQUE7RUFDSSxTQUFBO0FBR0o7O0FBREE7RUFDSSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxTQUFBO0VBQ0EsZ0JBQUE7QUFJSjs7QUFEQTtFQUNJLDZCQUFBO0VBQ0EscUNBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsZUFBQTtBQUlKOztBQURBO0VBQ0ksc0JBQUE7RUFDQSwrQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLDZCQUFBO0FBSUo7O0FBREE7RUFDSSxZQUFBO0FBSUo7O0FBRkE7RUFDSSxhQUFBO0FBS0o7O0FBSEE7RUFDSSw0QkFBQTtBQU1KOztBQUpBO0VBQ0ksNEJBQUE7QUFPSjs7QUFMQTtFQUNJLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLGNBQUE7QUFRSjs7QUFMQTtFQUNJLHlDQUFBO0VBQ0EsMkJBQUE7QUFRSjs7QUFOQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtBQVNKOztBQU5BO0VBQ0ksYUFBQTtFQUNBLHNCQUFBO0VBQ0EsdUJBQUE7RUFDQSxRQUFBO0FBU0o7O0FBTkE7RUFDSSxlQUFBO0VBQ0Esd0JBQUE7RUFDQSxxQkFBQTtFQUNBLGdCQUFBO0VBQ0EsMkJBQUE7RUFDQSxtQkFBQTtBQVNKOztBQUxBO0VBQ0ksb0JBQUE7QUFRSjs7QUFMQTtFQUNJLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFRSjs7QUFMQTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7QUFRSjs7QUFMQTtFQUNJLGVBQUE7RUFDQSxjQUFBO0FBUUo7O0FBTEE7RUFDSSxnQkFBQTtFQUNBLGVBQUE7QUFRSjs7QUFMQSxvQkFBQTtBQUNBO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0Esb0JBQUE7RUFDQSxxQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLGlDQUFBO0FBUUo7O0FBTEE7RUFDSSx5Q0FBQTtBQVFKOztBQUxBO0VBQ0ksaUJBQUE7RUFDQSxlQUFBO0FBUUo7O0FBTEEsaUJBQUE7QUFDQTtFQUNJLGFBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtFQUNBLE9BQUE7RUFDQSxNQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxjQUFBO0VBQ0Esb0NBQUE7QUFRSjs7QUFMQTtFQUNJLDZCQUFBO0VBQ0EsZUFBQTtFQUNBLGFBQUE7RUFDQSxVQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0FBUUo7O0FBTEE7RUFDSSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxRQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7QUFRSjs7QUFMQTtFQUNJLFlBQUE7QUFRSjs7QUFMQTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7QUFRSjs7QUFMQTtFQUNJLG1CQUFBO0FBUUo7O0FBTEE7RUFDSSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsZUFBQTtFQUNBLGdCQUFBO0FBUUo7O0FBTEE7Ozs7RUFJSSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSx5QkFBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxZQUFBO0VBQ0EsZUFBQTtBQVFKOztBQUxBO0VBQ0ksWUFBQTtFQUNBLFVBQUE7RUFDQSxnQkFBQTtBQVFKOztBQUxBO0VBQ0ksV0FBQTtFQUNBLGtCQUFBO0VBQ0EseUJBQUE7RUFDQSxrQkFBQTtFQUNBLGFBQUE7QUFRSjs7QUFMQTtFQUNJLGFBQUE7RUFDQSxRQUFBO0FBUUo7O0FBTEE7RUFDSSxXQUFBO0FBUUo7O0FBTEE7RUFDSSxZQUFBO0FBUUo7O0FBTEE7RUFDSSx5QkFBQTtFQUNBLFlBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSwyQkFBQTtBQVFKOztBQUxBO0VBQ0kseUJBQUE7QUFRSiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcbiAgICAtLWJnOiAjMGMwYzBmO1xuICAgIC0tY2FyZDogIzEyMTIxNztcbiAgICAtLXBpbGw6ICMxYzFjMWU7XG4gICAgLS10ZXh0OiAjZmZmO1xuICAgIC0tdGV4dC1tdXRlZDogIzhlOGU5MztcbiAgICAtLWFjY2VudDogIzRkN2JmZjtcbiAgICAtLXJhZGl1czogMTZweDtcbiAgICAtLWJhY2tncm91bmQtY29sb3I6ICMwQzBDMEY7XG4gICAgLS10ZXh0LWNvbG9yOiAjRkZGRkZGO1xuICAgIC0tc2Vjb25kYXJ5LXRleHQ6ICM4RThFOTM7XG4gICAgLS1hY2NlbnQtY29sb3I6ICM0MTY5RTE7XG4gICAgLS1xdWVzdC1iZzogIzFDMUMxRTtcbiAgICAtLXF1ZXN0LWJvcmRlcjogIzJDMkMyRTtcbn1cblxuXG5ib2R5LmRhcmstdGhlbWUge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJhY2tncm91bmQtY29sb3IpO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcbiAgICBtaW4taGVpZ2h0OiAxMDB2aDtcbn1cbioge1xuICAgIG1hcmdpbjogMDtcbiAgICBwYWRkaW5nOiAwO1xuICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XG4gICAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgUm9ib3RvLCBPeHlnZW4sIFVidW50dSwgQ2FudGFyZWxsLCAnT3BlbiBTYW5zJywgJ0hlbHZldGljYSBOZXVlJywgc2Fucy1zZXJpZjtcbn1cblxuLmNvbnRhaW5lciB7XG4gICAgbWF4LXdpZHRoOiA0ODBweDtcbiAgICBtYXJnaW4tbGVmdDogYXV0bztcbiAgICBtYXJnaW4tcmlnaHQ6IGF1dG87XG4gICAgcGFkZGluZzogMjBweDtcbn1cbmgxIHtcbiAgICBmb250LXNpemU6IDIwcHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLnRvcC1iYXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cbmhlYWRlciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xufVxuXG4ubG9nbyB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogOHB4O1xufVxuXG4ubG9nbyBpbWcge1xuICAgIGhlaWdodDogMjRweDtcbn1cblxuLmxvZ28gc3BhbiB7XG4gICAgZm9udC1zaXplOiAyMHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG4uZ29hbC1jYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICBwYWRkaW5nOiAyMHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cyk7XG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICBib3gtc2hhZG93OiAwIDAgMCAxcHggIzFlMWUxZTtcbiAgICBtYXJnaW4tdG9wOiAxMHB4O1xufVxuXG4uZGVzY3JpcHRpb24tY29udGFpbmVyIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xufVxuXG4uZ29hbC1kZXNjIHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gICAgZmxleDogMTtcbn1cblxuLmVkaXQtYmlvLWJ0biB7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgZm9udC1zaXplOiAxMnB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBwYWRkaW5nOiAycHggNXB4O1xuICAgIG1hcmdpbi1sZWZ0OiAxMHB4O1xufVxuXG4uZGVzY3JpcHRpb24tZm9ybSB7XG4gICAgbWFyZ2luLWJvdHRvbTogMTVweDtcbn1cblxuLmRlc2NyaXB0aW9uLWZvcm0gdGV4dGFyZWEge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIHBhZGRpbmc6IDEwcHg7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tcGlsbCk7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICMyYzJjMmU7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzKTtcbiAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICAgIG1pbi1oZWlnaHQ6IDgwcHg7XG4gICAgcmVzaXplOiB2ZXJ0aWNhbDtcbn1cblxuLnNhdmUtYnRuLCAuY2FuY2VsLWJ0biB7XG4gICAgcGFkZGluZzogOHB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzKTtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xuICAgIG1hcmdpbi1yaWdodDogMTBweDtcbn1cblxuLnNhdmUtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1hY2NlbnQpO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBib3JkZXI6IG5vbmU7XG59XG5cbi5jYW5jZWwtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiAjMmMyYzJlO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBib3JkZXI6IG5vbmU7XG59XG5cbi5kZWxldGUtZ29hbC1jb250YWluZXIge1xuICAgIG1hcmdpbi10b3A6IDMwcHg7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDEwMHB4O1xufVxuXG4uZGVsZXRlLWdvYWwtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgNTksIDQ4LCAwLjIpO1xuICAgIGNvbG9yOiAjRkYzQjMwO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICNGRjNCMzA7XG4gICAgcGFkZGluZzogMTBweCAxNnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cyk7XG4gICAgZm9udC1zaXplOiAxNHB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbn1cblxuLnByb2dyZXNzLWNvbnRhaW5lciB7XG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLnByb2dyZXNzLWJhciB7XG4gICAgaGVpZ2h0OiAxMHB4O1xuICAgIGJhY2tncm91bmQ6ICMyYzJjMmU7XG4gICAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIG1hcmdpbi1ib3R0b206IDZweDtcbn1cblxuLnByb2dyZXNzLWZpbGwge1xuICAgIGhlaWdodDogMTAwJTtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1hY2NlbnQpO1xuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZS1pbi1vdXQ7XG59XG5cbi5wcm9ncmVzcy1pbmZvIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBmb250LXNpemU6IDE0cHg7XG59XG5cbi5wcm9ncmVzcy11cGRhdGUgaW5wdXQge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIHBhZGRpbmc6IDEwcHg7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tcGlsbCk7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICMyYzJjMmU7XG4gICAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzKTtcbiAgICBtYXJnaW4tdG9wOiAxMHB4O1xufVxuXG5idXR0b24ge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWFjY2VudCk7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIHBhZGRpbmc6IDEwcHggMTZweDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMpO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBtYXJnaW4tdG9wOiAxMHB4O1xufVxuXG4ubWljcm9nb2Fscy1zZWN0aW9uIHtcbiAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xufVxuXG4ubWljcm9nb2Fscy1saXN0IHtcbiAgICBsaXN0LXN0eWxlOiBub25lO1xuICAgIHBhZGRpbmctbGVmdDogMDtcbiAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xufVxuXG4ubWljcm9nb2FsLWZvcm0ge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDEwcHg7XG4gICAgbWFyZ2luLWJvdHRvbTogNnB4O1xufVxuXG4ubWljcm9nb2FsLWFkZCBpbnB1dCB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgcGFkZGluZzogMTBweDtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1waWxsKTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgYm9yZGVyOiAxcHggc29saWQgIzJjMmMyZTtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMpO1xuICAgIG1hcmdpbi1ib3R0b206IDhweDtcbn1cblxuLm1pY3JvZ29hbC1hZGQgYnV0dG9uIHtcbiAgICB3aWR0aDogMTAwJTtcbn1cblxuLmpvdXJuYWwtc2VjdGlvbiB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tcGlsbCk7XG4gICAgcGFkZGluZzogMTZweDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMpO1xufVxuXG4uam91cm5hbC1lbnRyeSB7XG4gICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMyYzJjMmU7XG4gICAgcGFkZGluZy10b3A6IDEwcHg7XG4gICAgbWFyZ2luLXRvcDogMTBweDtcbn1cblxuLmpvdXJuYWwtZW50cnk6Zmlyc3QtY2hpbGQge1xuICAgIGJvcmRlci10b3A6IG5vbmU7XG4gICAgcGFkZGluZy10b3A6IDA7XG4gICAgbWFyZ2luLXRvcDogMDtcbn1cblxuLm1pbGVzdG9uZSB7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBjb2xvcjogdmFyKC0tYWNjZW50KTtcbn1cblxuLmpvdXJuYWwtbW9kYWwge1xuICAgIG1hcmdpbi10b3A6IDIwcHg7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tY2FyZCk7XG4gICAgcGFkZGluZzogMTRweDtcbiAgICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMpO1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLmpvdXJuYWwtbGluayB7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgIG1hcmdpbi10b3A6IDEwcHg7XG4gICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG59XG46cm9vdCB7XG4gICAgLS1iZzogIzBjMGMwZjtcbiAgICAtLWNhcmQ6ICMxMjEyMTc7XG4gICAgLS1waWxsOiAjMWMxYzFlO1xuICAgIC0tdGV4dDogI2ZmZjtcbiAgICAtLXRleHQtbXV0ZWQ6ICM4ZThlOTM7XG4gICAgLS1hY2NlbnQ6ICM0ZDdiZmY7XG4gICAgLS1yYWRpdXM6IDE2cHg7XG59XG5cblxuLmNvbnRhaW5lciB7XG4gICAgd2lkdGg6IDQ4MHB4O1xuICAgIG1hcmdpbi1sZWZ0OiBhdXRvO1xuICAgIG1hcmdpbi1yaWdodDogYXV0bztcbiAgICBwYWRkaW5nOiAyMHB4O1xufVxuXG5cblxuXG4udG9wLWRhdGUge1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LW11dGVkKTtcbn1cblxuLnBhZ2UtdGl0bGUge1xuICAgIGZvbnQtc2l6ZTogMjJweDtcbiAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufVxuXG4uZ29hbC1jYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1jYXJkKTtcbiAgICBwYWRkaW5nOiAyMHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cyk7XG4gICAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgICBib3gtc2hhZG93OiAwIDAgMCAxcHggIzFlMWUxZTtcbn1cblxuLmdvYWwtaGVhZGVyIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG59XG5cbi5nb2FsLWhlYWRlciBoMiB7XG4gICAgZm9udC1zaXplOiAxOHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgbWFyZ2luOiAwO1xufVxuXG4uZ29hbC1wZXJjZW50IHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjb2xvcjogdmFyKC0tYWNjZW50KTtcbn1cblxuLnByb2dyZXNzLWJhciB7XG4gICAgaGVpZ2h0OiAxMHB4O1xuICAgIGJhY2tncm91bmQ6ICMyYzJjMmU7XG4gICAgYm9yZGVyLXJhZGl1czogMTBweDtcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xuICAgIG1hcmdpbi1ib3R0b206IDhweDtcbn1cblxuLnByb2dyZXNzLWZpbGwge1xuICAgIGhlaWdodDogMTAwJTtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1hY2NlbnQpO1xuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZS1pbi1vdXQ7XG59XG5cbi5nb2FsLXZhbHVlIHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuICAgIG1hcmdpbi1ib3R0b206IDEycHg7XG59XG5cbi5nb2FsLWxpbmsge1xuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbn1cblxuLmdvYWwtbGluazpob3ZlciB7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XG59XG5cbi5uby1nb2FscyB7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LW11dGVkKTtcbiAgICBtYXJnaW4tdG9wOiA0MHB4O1xufVxuLmRlbGV0ZS1idG4ge1xuICAgIGJhY2tncm91bmQ6IG5vbmU7XG4gICAgYm9yZGVyOiBub25lO1xuICAgIGNvbG9yOiAjZmY0ZDRkO1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBtYXJnaW4tbGVmdDogOHB4O1xuICAgIGZvbnQtc2l6ZTogMTZweDtcbiAgICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xufVxuXG4uZGVsZXRlLWJ0bjpob3ZlciB7XG4gICAgY29sb3I6IHJlZDtcbn1cbi5taWNyb2dvYWwtaXRlbSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1waWxsKTtcbiAgICBwYWRkaW5nOiAxMHB4IDE0cHg7XG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG59XG5cbi5taWNyb2dvYWwtZm9ybSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMTBweDtcbiAgICBmbGV4OiAxO1xufVxuXG4ubWljcm8tYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgZm9udC1zaXplOiAxOHB4O1xuICAgIGNvbG9yOiB2YXIoLS1hY2NlbnQpO1xufVxuXG4uY2hlY2ttYXJrIHtcbiAgICBmb250LXNpemU6IDIwcHg7XG4gICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xufVxuXG4uY2hlY2ttYXJrLmNoZWNrZWQge1xuICAgIGNvbG9yOiAjNGNkOTY0O1xufVxuXG4ubWljcm9nb2FsLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDE1cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBjb2xvcjogdmFyKC0tdGV4dCk7XG59XG5cbi5kZWxldGUtZm9ybSB7XG4gICAgbWFyZ2luLWxlZnQ6IGF1dG87XG59XG5cbi5kZWxldGUtYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiBub25lO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBmb250LXNpemU6IDE4cHg7XG4gICAgY29sb3I6ICNmZjRkNmQ7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xufVxuXG4uam91cm5hbC1tb2RhbC5mYW5jeSB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcGlsbCk7XG4gICAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgICBwYWRkaW5nOiAyMHB4O1xuICAgIG1hcmdpbi10b3A6IDI0cHg7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIGJveC1zaGFkb3c6IDAgMCAwIDFweCAjMWUxZTFlO1xufVxuXG4uam91cm5hbC1tb2RhbC5mYW5jeSBwIHtcbiAgICBmb250LXNpemU6IDE1cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgICBtYXJnaW4tYm90dG9tOiAxMnB4O1xufVxuXG4uam91cm5hbC1saW5rIHtcbiAgICBmb250LXdlaWdodDogNjAwO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICBjb2xvcjogdmFyKC0tYWNjZW50KTtcbn1cblxuLm1pbGVzdG9uZS1oaWdobGlnaHQge1xuICAgIGNvbG9yOiB2YXIoLS1hY2NlbnQpO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5taWNyby1idG4sIC5kZWxldGUtYnRuIHtcbiAgICBtYXJnaW46IDA7XG59XG4uaW5saW5lLWpvdXJuYWwtZm9ybSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMTBweDtcbiAgICBtYXJnaW4tdG9wOiAxMnB4O1xufVxuXG4uaW5saW5lLWpvdXJuYWwtZm9ybSB0ZXh0YXJlYSB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcGlsbCk7XG4gICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tcXVlc3QtYm9yZGVyKTtcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgIHBhZGRpbmc6IDEwcHg7XG4gICAgY29sb3I6IHZhcigtLXRleHQpO1xuICAgIHJlc2l6ZTogdmVydGljYWw7XG4gICAgZm9udC1zaXplOiAxNHB4O1xufVxuXG4uaW5saW5lLWpvdXJuYWwtZm9ybSBidXR0b24ge1xuICAgIGFsaWduLXNlbGY6IGZsZXgtc3RhcnQ7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYWNjZW50KTtcbiAgICBib3JkZXI6IG5vbmU7XG4gICAgcGFkZGluZzogMTBweCAxNnB4O1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuMnMgZWFzZTtcbn1cblxuLmlubGluZS1qb3VybmFsLWZvcm0gYnV0dG9uOmhvdmVyIHtcbiAgICBvcGFjaXR5OiAwLjk7XG59XG4uc2F2ZS1iYXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG59XG4uc2F2ZS1iYXIgYnV0dG9uIHtcbiAgICBib3JkZXItcmFkaXVzOiAwIDE2cHggMTZweCAwO1xufVxuLnNhdmUtYmFyIGlucHV0IHtcbiAgICBib3JkZXItcmFkaXVzOiAxNnB4IDAgMCAxNnB4O1xufVxuLmdvYWwtY2FyZC1saW5rIHtcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgY29sb3I6IGluaGVyaXQ7XG4gICAgZGlzcGxheTogYmxvY2s7XG59XG5cbi5nb2FsLWNhcmQtbGluazpob3ZlciAuZ29hbC1jYXJkIHtcbiAgICBib3gtc2hhZG93OiAwIDAgMCAxcHggdmFyKC0tYWNjZW50LWNvbG9yKTtcbiAgICB0cmFuc2l0aW9uOiBib3gtc2hhZG93IDAuMnM7XG59XG4uZ29hbC12YWx1ZXtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2Vlbjtcbn1cblxuLmxvZ28td3JhcCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgIGdhcDogNHB4O1xufVxuXG4uYmFjay1saW5rIHtcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgY29sb3I6IHZhcigtLXRleHQtbXV0ZWQpO1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICBtYXJnaW4tbGVmdDogMnB4O1xuICAgIHRyYW5zaXRpb246IGNvbG9yIDAuMnMgZWFzZTtcbiAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuXG59XG5cbi5iYWNrLWxpbms6aG92ZXIge1xuICAgIGNvbG9yOiB2YXIoLS1hY2NlbnQpO1xufVxuXG4uZ29hbC1oZWFkZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xufVxuXG4uZ29hbC10aXRsZSB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMTBweDtcbn1cblxuLmdvYWwtZW1vamkge1xuICAgIGZvbnQtc2l6ZTogMjBweDtcbiAgICBsaW5lLWhlaWdodDogMTtcbn1cblxuLmdvYWwtbmFtZSB7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICBmb250LXNpemU6IDE2cHg7XG59XG5cbi8qIEFkZCBHb2FsIEJ1dHRvbiAqL1xuLmFkZC1xdWVzdC1saW5rIHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgY29sb3I6IHZhcigtLWFjY2VudCk7XG4gICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIGZvbnQtc2l6ZTogMTRweDtcbiAgICBmb250LXdlaWdodDogNTAwO1xuICAgIHBhZGRpbmc6IDZweCAxMnB4O1xuICAgIGJvcmRlci1yYWRpdXM6IDZweDtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDc3LCAxMjMsIDI1NSwgMC4xKTtcbiAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuMnM7XG59XG5cbi5hZGQtcXVlc3QtbGluazpob3ZlciB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSg3NywgMTIzLCAyNTUsIDAuMik7XG59XG5cbi5hZGQtcXVlc3QtaWNvbiB7XG4gICAgbWFyZ2luLXJpZ2h0OiA0cHg7XG4gICAgZm9udC1zaXplOiAxNnB4O1xufVxuXG4vKiBNb2RhbCBTdHlsZXMgKi9cbi5tb2RhbCB7XG4gICAgZGlzcGxheTogbm9uZTtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgei1pbmRleDogMTAwMDtcbiAgICBsZWZ0OiAwO1xuICAgIHRvcDogMDtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgb3ZlcmZsb3c6IGF1dG87XG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjkpO1xufVxuXG4ubW9kYWwtY29udGVudCB7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgbWFyZ2luOiA1JSBhdXRvO1xuICAgIHBhZGRpbmc6IDIwcHg7XG4gICAgd2lkdGg6IDkwJTtcbiAgICBtYXgtd2lkdGg6IDUwMHB4O1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICBjb2xvcjogd2hpdGU7XG4gICAgbWF4LWhlaWdodDogODB2aDtcbiAgICBvdmVyZmxvdy15OiBhdXRvO1xufVxuXG4uY2xvc2UtbW9kYWwge1xuICAgIGNvbG9yOiAjNjY2O1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDVweDtcbiAgICByaWdodDogMTBweDtcbiAgICBmb250LXNpemU6IDIwcHg7XG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgY3Vyc29yOiBwb2ludGVyO1xufVxuXG4uY2xvc2UtbW9kYWw6aG92ZXIge1xuICAgIGNvbG9yOiB3aGl0ZTtcbn1cblxuLm1vZGFsIGgyIHtcbiAgICBtYXJnaW4tdG9wOiAwO1xuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgZm9udC1zaXplOiAyMHB4O1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgY29sb3I6IHdoaXRlO1xufVxuXG4uZm9ybS1ncm91cCB7XG4gICAgbWFyZ2luLWJvdHRvbTogMThweDtcbn1cblxuLmZvcm0tZ3JvdXAgbGFiZWwge1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIG1hcmdpbi1ib3R0b206IDhweDtcbiAgICBjb2xvcjogI0NDQztcbiAgICBmb250LXNpemU6IDE0cHg7XG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLmZvcm0tZ3JvdXAgaW5wdXRbdHlwZT1cInRleHRcIl0sXG4uZm9ybS1ncm91cCBpbnB1dFt0eXBlPVwibnVtYmVyXCJdLFxuLmZvcm0tZ3JvdXAgc2VsZWN0LFxuLmZvcm0tZ3JvdXAgdGV4dGFyZWEge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIHBhZGRpbmc6IDEwcHggMTJweDtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMUMxQzFFO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkICMzQzNDM0U7XG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgICBmb250LXNpemU6IDE0cHg7XG59XG5cbi5mb3JtLWdyb3VwIHRleHRhcmVhIHtcbiAgICBoZWlnaHQ6IDgwcHg7XG4gICAgd2lkdGg6IDk1JTtcbiAgICByZXNpemU6IHZlcnRpY2FsO1xufVxuXG4jZW1vamkge1xuICAgIHdpZHRoOiA2MHB4O1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMUMxQzFFO1xuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICBwYWRkaW5nOiAxMHB4O1xufVxuXG4uZ29hbC1pbnB1dHMge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZ2FwOiA4cHg7XG59XG5cbi5nb2FsLWlucHV0cyBpbnB1dCB7XG4gICAgd2lkdGg6IDgwcHg7XG59XG5cbi5nb2FsLWlucHV0cyBzZWxlY3Qge1xuICAgIGZsZXgtZ3JvdzogMTtcbn1cblxuLnN1Ym1pdC1idG4ge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWFjY2VudCk7XG4gICAgY29sb3I6IHdoaXRlO1xuICAgIGJvcmRlcjogbm9uZTtcbiAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gICAgYm9yZGVyLXJhZGl1czogOHB4O1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBmb250LXNpemU6IDE2cHg7XG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBtYXJnaW4tdG9wOiAyMHB4O1xuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4ycztcbn1cblxuLnN1Ym1pdC1idG46aG92ZXIge1xuICAgIGJhY2tncm91bmQtY29sb3I6ICMzQTU3QzI7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "GoalService", "Goal", "combineLatest", "map", "of", "switchMap", "take", "NavigationComponent", "SupabaseService", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "goal_r2", "completedMicrogoals", "totalMicrogoals", "ɵɵelement", "ɵɵtemplate", "GoalListPage_ng_container_15_a_1_span_15_Template", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "emoji", "name", "ɵɵtextInterpolate1", "progressPercent", "ɵɵstyleProp", "ɵɵtextInterpolate3", "current_value", "goal_value", "goal_unit", "ɵɵelementContainerStart", "GoalListPage_ng_container_15_a_1_Template", "ctx_r2", "goals", "GoalListPage", "constructor", "userId", "showAddGoalModal", "newGoal", "getEmptyGoal", "userSubscription", "supabaseService", "goalService", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "loadGoals", "ionViewWillEnter", "ngOnDestroy", "unsubscribe", "console", "error", "getGoals", "length", "goalObservables", "goal", "getMicroGoals", "microgoals", "filter", "m", "completed", "Math", "min", "round", "next", "goalsWithMicrogoals", "openAddGoalModal", "event", "preventDefault", "closeAddGoalModal", "createGoal", "goalToCreate", "description", "user_id", "start_date", "Date", "public", "then", "goalId", "catch", "selectors", "decls", "vars", "consts", "template", "GoalListPage_Template", "rf", "ctx", "ɵɵlistener", "GoalListPage_Template_a_click_11_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "GoalListPage_ng_container_15_Template", "GoalListPage_ng_template_16_Template", "ɵɵtemplateRefExtractor", "GoalListPage_Template_span_click_20_listener", "GoalListPage_Template_form_ngSubmit_24_listener", "ɵɵtwoWayListener", "GoalListPage_Template_input_ngModelChange_28_listener", "ɵɵtwoWayBindingSet", "GoalListPage_Template_input_ngModelChange_30_listener", "GoalListPage_Template_textarea_ngModelChange_34_listener", "GoalListPage_Template_input_ngModelChange_39_listener", "GoalListPage_Template_select_ngModelChange_40_listener", "noGoals_r4", "ɵɵtwoWayProperty", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "NumberValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-list\\goal-list.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-list\\goal-list.page.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal, GoalUnit } from '../../../models/goal.model';\nimport { Subscription, combineLatest, map, of, switchMap, take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\n\ninterface GoalDisplay extends Goal {\n  progressPercent: number;\n  totalMicrogoals: number;\n  completedMicrogoals: number;\n}\n\n@Component({\n  selector: 'app-goal-list',\n  templateUrl: './goal-list.page.html',\n  styleUrls: ['./goal-list.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]\n})\nexport class GoalListPage implements OnInit, OnDestroy {\n  // User data\n  userId: string | null = null;\n\n  // Goals\n  goals: GoalDisplay[] = [];\n\n  // Add Goal Modal\n  showAddGoalModal = false;\n  newGoal = this.getEmptyGoal();\n\n  // Subscriptions\n  private userSubscription: Subscription | null = null;\n\n  private supabaseService = inject(SupabaseService);\n  private goalService = inject(GoalService);\n\n  constructor() {\n  }\n\n  ngOnInit() {\n    // Get the current user\n    this.userSubscription = this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        this.loadGoals();\n      }\n    });\n  }\n\n  ionViewWillEnter() {\n    // Reload goals data when the page is about to be displayed\n    if (this.userId) {\n      this.loadGoals();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.userSubscription) {\n      this.userSubscription.unsubscribe();\n    }\n  }\n\n  loadGoals() {\n    if (!this.userId) {\n      console.error('Cannot load goals: No user ID available');\n      return;\n    }\n\n\n    // Clear existing goals to show loading state\n    this.goals = [];\n\n    this.goalService.getGoals(this.userId).pipe(\n      switchMap(goals => {\n\n        if (goals.length === 0) {\n          return of([]);\n        }\n\n        // Get microgoals for each goal\n        const goalObservables = goals.map(goal =>\n          this.goalService.getMicroGoals(goal.id!).pipe(\n            map(microgoals => {\n\n              const totalMicrogoals = microgoals.length;\n              const completedMicrogoals = microgoals.filter(m => m.completed).length;\n              const progressPercent = goal.goal_value > 0\n                ? Math.min(100, Math.round((goal.current_value / goal.goal_value) * 100))\n                : 0;\n\n              return {\n                ...goal,\n                progressPercent,\n                totalMicrogoals,\n                completedMicrogoals\n              } as GoalDisplay;\n            })\n          )\n        );\n\n        return combineLatest(goalObservables);\n      })\n    ).subscribe({\n      next: goalsWithMicrogoals => {\n        this.goals = goalsWithMicrogoals;\n      },\n      error: error => {\n        console.error('GoalListPage: Error loading goals:', error);\n        this.goals = [];\n      }\n    });\n  }\n\n  openAddGoalModal(event: Event) {\n    event.preventDefault();\n    this.showAddGoalModal = true;\n    this.newGoal = this.getEmptyGoal();\n  }\n\n  closeAddGoalModal() {\n    this.showAddGoalModal = false;\n  }\n\n  createGoal() {\n    if (!this.userId) {\n      console.error('Cannot create goal: No user ID available');\n      return;\n    }\n\n    if (!this.newGoal.name) {\n      console.error('Cannot create goal: Goal name is required');\n      return;\n    }\n\n    const goalToCreate: Goal = {\n      name: this.newGoal.name,\n      description: this.newGoal.description || '',\n      emoji: this.newGoal.emoji || '🎯',\n      goal_value: this.newGoal.goal_value || 0,\n      goal_unit: this.newGoal.goal_unit || 'count',\n      user_id: this.userId,\n      start_date: new Date(),\n      current_value: 0,\n      public: false\n    };\n\n\n    this.goalService.createGoal(goalToCreate)\n      .then((goalId) => {\n        this.closeAddGoalModal();\n        this.loadGoals();\n      })\n      .catch(error => {\n        console.error('Error creating goal:', error);\n      });\n  }\n\n  private getEmptyGoal(): Partial<Goal> {\n    return {\n      name: '',\n      description: '',\n      emoji: '🎯',\n      goal_value: 100,\n      goal_unit: 'count' as GoalUnit\n    };\n  }\n}\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo\">\r\n            <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n            <span>Upshift</span>\r\n        </div>\r\n        <h1>Goals</h1>\r\n    </header>\r\n\r\n    <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;\">\r\n        <h2>My Goals</h2>\r\n        <a href=\"#\" id=\"add-goal-btn\" class=\"add-quest-link\" (click)=\"openAddGoalModal($event)\">\r\n            <span class=\"add-quest-icon\">+</span> Add Goal\r\n        </a>\r\n    </div>\r\n\r\n    <ng-container *ngIf=\"goals.length > 0; else noGoals\">\r\n        <a *ngFor=\"let goal of goals\" [routerLink]=\"['/goals', goal.id]\" class=\"goal-card-link\">\r\n            <div class=\"goal-card\">\r\n                <div class=\"goal-header\">\r\n                    <div class=\"goal-title\">\r\n                        <span class=\"goal-emoji\">{{ goal.emoji }}</span>\r\n                        <span class=\"goal-name\">{{ goal.name }}</span>\r\n                    </div>\r\n                    <span class=\"goal-percent\">{{ goal.progressPercent }}%</span>\r\n                </div>\r\n\r\n                <div class=\"progress-bar\">\r\n                    <div class=\"progress-fill\" [style.width.%]=\"goal.progressPercent\"></div>\r\n                </div>\r\n\r\n                <p class=\"goal-value\">\r\n                    <span>{{ goal.current_value }}/{{ goal.goal_value }} {{ goal.goal_unit }} </span>\r\n                    <span *ngIf=\"goal.totalMicrogoals > 0\">Micro goals: {{ goal.completedMicrogoals }}/{{ goal.totalMicrogoals }} </span>\r\n                </p>\r\n            </div>\r\n        </a>\r\n    </ng-container>\r\n\r\n    <ng-template #noGoals>\r\n        <p class=\"no-goals\">You have no goals yet.</p>\r\n    </ng-template>\r\n</div>\r\n\r\n<!-- Add Goal Modal -->\r\n<div id=\"add-goal-modal\" class=\"modal\" [style.display]=\"showAddGoalModal ? 'block' : 'none'\">\r\n    <div class=\"modal-content\">\r\n        <span class=\"close-modal\" (click)=\"closeAddGoalModal()\">&times;</span>\r\n        <h2>Add New Goal</h2>\r\n        <form (ngSubmit)=\"createGoal()\" #goalForm=\"ngForm\">\r\n            <div style=\"display: flex;gap: 10px;\">\r\n                <div class=\"form-group\">\r\n                    <input type=\"text\" id=\"emoji\" name=\"emoji\" [(ngModel)]=\"newGoal.emoji\" value=\"🎯\" appEmojiInput>\r\n                </div>\r\n                <div class=\"form-group\">\r\n                    <input type=\"text\" id=\"name\" name=\"name\" [(ngModel)]=\"newGoal.name\" placeholder=\"Enter goal name\" required>\r\n                </div>\r\n            </div>\r\n            <div class=\"form-group\">\r\n                <label for=\"description\">Description</label>\r\n                <textarea id=\"description\" name=\"description\" [(ngModel)]=\"newGoal.description\" placeholder=\"Enter goal description\"></textarea>\r\n            </div>\r\n            <div class=\"form-group goal-settings\">\r\n                <label>Goal Target</label>\r\n                <div class=\"goal-inputs\">\r\n                    <input type=\"number\" id=\"goal_value\" name=\"goal_value\" [(ngModel)]=\"newGoal.goal_value\" value=\"100\" min=\"1\">\r\n                    <select id=\"goal_unit\" name=\"goal_unit\" [(ngModel)]=\"newGoal.goal_unit\">\r\n                        <option value=\"count\">count</option>\r\n                        <option value=\"steps\">steps</option>\r\n                        <option value=\"m\">meters</option>\r\n                        <option value=\"km\">kilometers</option>\r\n                        <option value=\"sec\">seconds</option>\r\n                        <option value=\"min\">minutes</option>\r\n                        <option value=\"hr\">hours</option>\r\n                        <option value=\"days\">days</option>\r\n                        <option value=\"weeks\">weeks</option>\r\n                        <option value=\"months\">months</option>\r\n                        <option value=\"years\">years</option>\r\n                        <option value=\"Cal\">calories</option>\r\n                        <option value=\"g\">grams</option>\r\n                        <option value=\"mg\">milligrams</option>\r\n                        <option value=\"pages\">pages</option>\r\n                        <option value=\"books\">books</option>\r\n                        <option value=\"%\">percent</option>\r\n                        <option value=\"€\">euros</option>\r\n                        <option value=\"$\">dollars</option>\r\n                        <option value=\"£\">pounds</option>\r\n                    </select>\r\n                </div>\r\n            </div>\r\n            <button type=\"submit\" class=\"submit-btn\">Create Goal</button>\r\n        </form>\r\n    </div>\r\n</div>\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";AAAA,SAAuCA,MAAM,QAAQ,eAAe;AACpE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,IAAI,QAAkB,4BAA4B;AAC3D,SAAuBC,aAAa,EAAEC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,IAAI,QAAQ,MAAM;AAC5E,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;ICwB3DC,EAAA,CAAAC,cAAA,WAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9EH,EAAA,CAAAI,SAAA,EAAuE;IAAvEJ,EAAA,CAAAK,kBAAA,kBAAAC,OAAA,CAAAC,mBAAA,OAAAD,OAAA,CAAAE,eAAA,MAAuE;;;;;IAZ1GR,EAJhB,CAAAC,cAAA,YAAwF,cAC7D,cACM,cACG,eACK;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAENH,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAAS,SAAA,eAAwE;IAC5ET,EAAA,CAAAG,YAAA,EAAM;IAGFH,EADJ,CAAAC,cAAA,aAAsB,YACZ;IAAAD,EAAA,CAAAE,MAAA,IAAoE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjFH,EAAA,CAAAU,UAAA,KAAAC,iDAAA,mBAAuC;IAGnDX,EAFQ,CAAAG,YAAA,EAAI,EACF,EACN;;;;IAnB0BH,EAAA,CAAAY,UAAA,eAAAZ,EAAA,CAAAa,eAAA,KAAAC,GAAA,EAAAR,OAAA,CAAAS,EAAA,EAAkC;IAIvBf,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAgB,iBAAA,CAAAV,OAAA,CAAAW,KAAA,CAAgB;IACjBjB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAgB,iBAAA,CAAAV,OAAA,CAAAY,IAAA,CAAe;IAEhBlB,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAmB,kBAAA,KAAAb,OAAA,CAAAc,eAAA,MAA2B;IAI3BpB,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAqB,WAAA,UAAAf,OAAA,CAAAc,eAAA,MAAsC;IAI3DpB,EAAA,CAAAI,SAAA,GAAoE;IAApEJ,EAAA,CAAAsB,kBAAA,KAAAhB,OAAA,CAAAiB,aAAA,OAAAjB,OAAA,CAAAkB,UAAA,OAAAlB,OAAA,CAAAmB,SAAA,MAAoE;IACnEzB,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAY,UAAA,SAAAN,OAAA,CAAAE,eAAA,KAA8B;;;;;IAjBrDR,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAU,UAAA,IAAAiB,yCAAA,kBAAwF;;;;;IAApE3B,EAAA,CAAAI,SAAA,EAAQ;IAARJ,EAAA,CAAAY,UAAA,YAAAgB,MAAA,CAAAC,KAAA,CAAQ;;;;;IAuB5B7B,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADhBtD,OAAM,MAAO2B,YAAY;EAiBvBC,YAAA;IAhBA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAH,KAAK,GAAkB,EAAE;IAEzB;IACA,KAAAI,gBAAgB,GAAG,KAAK;IACxB,KAAAC,OAAO,GAAG,IAAI,CAACC,YAAY,EAAE;IAE7B;IACQ,KAAAC,gBAAgB,GAAwB,IAAI;IAE5C,KAAAC,eAAe,GAAGpD,MAAM,CAACa,eAAe,CAAC;IACzC,KAAAwC,WAAW,GAAGrD,MAAM,CAACK,WAAW,CAAC;EAGzC;EAEAiD,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACC,eAAe,CAACG,YAAY,CAACC,IAAI,CAC5D7C,IAAI,CAAC,CAAC,CAAC,CACR,CAAC8C,SAAS,CAACC,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACX,MAAM,GAAGW,IAAI,CAAC5B,EAAE;QACrB,IAAI,CAAC6B,SAAS,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd;IACA,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACY,SAAS,EAAE;IAClB;EACF;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACV,gBAAgB,EAAE;MACzB,IAAI,CAACA,gBAAgB,CAACW,WAAW,EAAE;IACrC;EACF;EAEAH,SAASA,CAAA;IACP,IAAI,CAAC,IAAI,CAACZ,MAAM,EAAE;MAChBgB,OAAO,CAACC,KAAK,CAAC,yCAAyC,CAAC;MACxD;IACF;IAGA;IACA,IAAI,CAACpB,KAAK,GAAG,EAAE;IAEf,IAAI,CAACS,WAAW,CAACY,QAAQ,CAAC,IAAI,CAAClB,MAAM,CAAC,CAACS,IAAI,CACzC9C,SAAS,CAACkC,KAAK,IAAG;MAEhB,IAAIA,KAAK,CAACsB,MAAM,KAAK,CAAC,EAAE;QACtB,OAAOzD,EAAE,CAAC,EAAE,CAAC;MACf;MAEA;MACA,MAAM0D,eAAe,GAAGvB,KAAK,CAACpC,GAAG,CAAC4D,IAAI,IACpC,IAAI,CAACf,WAAW,CAACgB,aAAa,CAACD,IAAI,CAACtC,EAAG,CAAC,CAAC0B,IAAI,CAC3ChD,GAAG,CAAC8D,UAAU,IAAG;QAEf,MAAM/C,eAAe,GAAG+C,UAAU,CAACJ,MAAM;QACzC,MAAM5C,mBAAmB,GAAGgD,UAAU,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAACP,MAAM;QACtE,MAAM/B,eAAe,GAAGiC,IAAI,CAAC7B,UAAU,GAAG,CAAC,GACvCmC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAER,IAAI,CAAC9B,aAAa,GAAG8B,IAAI,CAAC7B,UAAU,GAAI,GAAG,CAAC,CAAC,GACvE,CAAC;QAEL,OAAO;UACL,GAAG6B,IAAI;UACPjC,eAAe;UACfZ,eAAe;UACfD;SACc;MAClB,CAAC,CAAC,CACH,CACF;MAED,OAAOf,aAAa,CAAC4D,eAAe,CAAC;IACvC,CAAC,CAAC,CACH,CAACV,SAAS,CAAC;MACVoB,IAAI,EAAEC,mBAAmB,IAAG;QAC1B,IAAI,CAAClC,KAAK,GAAGkC,mBAAmB;MAClC,CAAC;MACDd,KAAK,EAAEA,KAAK,IAAG;QACbD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACpB,KAAK,GAAG,EAAE;MACjB;KACD,CAAC;EACJ;EAEAmC,gBAAgBA,CAACC,KAAY;IAC3BA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,YAAY,EAAE;EACpC;EAEAgC,iBAAiBA,CAAA;IACf,IAAI,CAAClC,gBAAgB,GAAG,KAAK;EAC/B;EAEAmC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACpC,MAAM,EAAE;MAChBgB,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IAEA,IAAI,CAAC,IAAI,CAACf,OAAO,CAAChB,IAAI,EAAE;MACtB8B,OAAO,CAACC,KAAK,CAAC,2CAA2C,CAAC;MAC1D;IACF;IAEA,MAAMoB,YAAY,GAAS;MACzBnD,IAAI,EAAE,IAAI,CAACgB,OAAO,CAAChB,IAAI;MACvBoD,WAAW,EAAE,IAAI,CAACpC,OAAO,CAACoC,WAAW,IAAI,EAAE;MAC3CrD,KAAK,EAAE,IAAI,CAACiB,OAAO,CAACjB,KAAK,IAAI,IAAI;MACjCO,UAAU,EAAE,IAAI,CAACU,OAAO,CAACV,UAAU,IAAI,CAAC;MACxCC,SAAS,EAAE,IAAI,CAACS,OAAO,CAACT,SAAS,IAAI,OAAO;MAC5C8C,OAAO,EAAE,IAAI,CAACvC,MAAM;MACpBwC,UAAU,EAAE,IAAIC,IAAI,EAAE;MACtBlD,aAAa,EAAE,CAAC;MAChBmD,MAAM,EAAE;KACT;IAGD,IAAI,CAACpC,WAAW,CAAC8B,UAAU,CAACC,YAAY,CAAC,CACtCM,IAAI,CAAEC,MAAM,IAAI;MACf,IAAI,CAACT,iBAAiB,EAAE;MACxB,IAAI,CAACvB,SAAS,EAAE;IAClB,CAAC,CAAC,CACDiC,KAAK,CAAC5B,KAAK,IAAG;MACbD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,CAAC;EACN;EAEQd,YAAYA,CAAA;IAClB,OAAO;MACLjB,IAAI,EAAE,EAAE;MACRoD,WAAW,EAAE,EAAE;MACfrD,KAAK,EAAE,IAAI;MACXO,UAAU,EAAE,GAAG;MACfC,SAAS,EAAE;KACZ;EACH;;gBApJWK,YAAY;;mCAAZA,aAAY;AAAA;;QAAZA,aAAY;EAAAgD,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;MCtBjBpF,EAFR,CAAAC,cAAA,aAAuB,aACX,aACc;MACdD,EAAA,CAAAS,SAAA,aAA6D;MAC7DT,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,YAAK;MACbF,EADa,CAAAG,YAAA,EAAK,EACT;MAGLH,EADJ,CAAAC,cAAA,aAAsG,SAC9F;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACjBH,EAAA,CAAAC,cAAA,YAAwF;MAAnCD,EAAA,CAAAsF,UAAA,mBAAAC,0CAAAC,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA,OAAA1F,EAAA,CAAA2F,WAAA,CAASN,GAAA,CAAArB,gBAAA,CAAAwB,MAAA,CAAwB;MAAA,EAAC;MACnFxF,EAAA,CAAAC,cAAA,eAA6B;MAAAD,EAAA,CAAAE,MAAA,SAAC;MAAAF,EAAA,CAAAG,YAAA,EAAO;MAACH,EAAA,CAAAE,MAAA,kBAC1C;MACJF,EADI,CAAAG,YAAA,EAAI,EACF;MAyBNH,EAvBA,CAAAU,UAAA,KAAAkF,qCAAA,0BAAqD,KAAAC,oCAAA,gCAAA7F,EAAA,CAAA8F,sBAAA,CAuB/B;MAG1B9F,EAAA,CAAAG,YAAA,EAAM;MAKEH,EAFR,CAAAC,cAAA,cAA6F,eAC9D,gBACiC;MAA9BD,EAAA,CAAAsF,UAAA,mBAAAS,6CAAA;QAAA/F,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA,OAAA1F,EAAA,CAAA2F,WAAA,CAASN,GAAA,CAAAlB,iBAAA,EAAmB;MAAA,EAAC;MAACnE,EAAA,CAAAE,MAAA,cAAO;MAAAF,EAAA,CAAAG,YAAA,EAAO;MACtEH,EAAA,CAAAC,cAAA,UAAI;MAAAD,EAAA,CAAAE,MAAA,oBAAY;MAAAF,EAAA,CAAAG,YAAA,EAAK;MACrBH,EAAA,CAAAC,cAAA,mBAAmD;MAA7CD,EAAA,CAAAsF,UAAA,sBAAAU,gDAAA;QAAAhG,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA,OAAA1F,EAAA,CAAA2F,WAAA,CAAYN,GAAA,CAAAjB,UAAA,EAAY;MAAA,EAAC;MAGnBpE,EAFR,CAAAC,cAAA,eAAsC,eACV,iBAC4E;MAArDD,EAAA,CAAAiG,gBAAA,2BAAAC,sDAAAV,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA1F,EAAA,CAAAmG,kBAAA,CAAAd,GAAA,CAAAnD,OAAA,CAAAjB,KAAA,EAAAuE,MAAA,MAAAH,GAAA,CAAAnD,OAAA,CAAAjB,KAAA,GAAAuE,MAAA;QAAA,OAAAxF,EAAA,CAAA2F,WAAA,CAAAH,MAAA;MAAA,EAA2B;MAC1ExF,EADI,CAAAG,YAAA,EAAgG,EAC9F;MAEFH,EADJ,CAAAC,cAAA,eAAwB,iBACuF;MAAlED,EAAA,CAAAiG,gBAAA,2BAAAG,sDAAAZ,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA1F,EAAA,CAAAmG,kBAAA,CAAAd,GAAA,CAAAnD,OAAA,CAAAhB,IAAA,EAAAsE,MAAA,MAAAH,GAAA,CAAAnD,OAAA,CAAAhB,IAAA,GAAAsE,MAAA;QAAA,OAAAxF,EAAA,CAAA2F,WAAA,CAAAH,MAAA;MAAA,EAA0B;MAE3ExF,EAFQ,CAAAG,YAAA,EAA2G,EACzG,EACJ;MAEFH,EADJ,CAAAC,cAAA,eAAwB,iBACK;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAC5CH,EAAA,CAAAC,cAAA,oBAAqH;MAAvED,EAAA,CAAAiG,gBAAA,2BAAAI,yDAAAb,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA1F,EAAA,CAAAmG,kBAAA,CAAAd,GAAA,CAAAnD,OAAA,CAAAoC,WAAA,EAAAkB,MAAA,MAAAH,GAAA,CAAAnD,OAAA,CAAAoC,WAAA,GAAAkB,MAAA;QAAA,OAAAxF,EAAA,CAAA2F,WAAA,CAAAH,MAAA;MAAA,EAAiC;MACnFxF,EADyH,CAAAG,YAAA,EAAW,EAC9H;MAEFH,EADJ,CAAAC,cAAA,eAAsC,aAC3B;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAQ;MAEtBH,EADJ,CAAAC,cAAA,eAAyB,iBACuF;MAArDD,EAAA,CAAAiG,gBAAA,2BAAAK,sDAAAd,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA1F,EAAA,CAAAmG,kBAAA,CAAAd,GAAA,CAAAnD,OAAA,CAAAV,UAAA,EAAAgE,MAAA,MAAAH,GAAA,CAAAnD,OAAA,CAAAV,UAAA,GAAAgE,MAAA;QAAA,OAAAxF,EAAA,CAAA2F,WAAA,CAAAH,MAAA;MAAA,EAAgC;MAAvFxF,EAAA,CAAAG,YAAA,EAA4G;MAC5GH,EAAA,CAAAC,cAAA,kBAAwE;MAAhCD,EAAA,CAAAiG,gBAAA,2BAAAM,uDAAAf,MAAA;QAAAxF,EAAA,CAAAyF,aAAA,CAAAC,GAAA;QAAA1F,EAAA,CAAAmG,kBAAA,CAAAd,GAAA,CAAAnD,OAAA,CAAAT,SAAA,EAAA+D,MAAA,MAAAH,GAAA,CAAAnD,OAAA,CAAAT,SAAA,GAAA+D,MAAA;QAAA,OAAAxF,EAAA,CAAA2F,WAAA,CAAAH,MAAA;MAAA,EAA+B;MACnExF,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACjCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACjCH,EAAA,CAAAC,cAAA,kBAAqB;MAAAD,EAAA,CAAAE,MAAA,YAAI;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAuB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAoB;MAAAD,EAAA,CAAAE,MAAA,gBAAQ;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACrCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAChCH,EAAA,CAAAC,cAAA,kBAAmB;MAAAD,EAAA,CAAAE,MAAA,kBAAU;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACtCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAsB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACpCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAChCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAS;MAClCH,EAAA,CAAAC,cAAA,kBAAkB;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAGpCF,EAHoC,CAAAG,YAAA,EAAS,EAC5B,EACP,EACJ;MACNH,EAAA,CAAAC,cAAA,kBAAyC;MAAAD,EAAA,CAAAE,MAAA,mBAAW;MAGhEF,EAHgE,CAAAG,YAAA,EAAS,EAC1D,EACL,EACJ;MAGNH,EAAA,CAAAS,SAAA,sBAAiC;;;;MAhFdT,EAAA,CAAAI,SAAA,IAAwB;MAAAJ,EAAxB,CAAAY,UAAA,SAAAyE,GAAA,CAAAxD,KAAA,CAAAsB,MAAA,KAAwB,aAAAqD,UAAA,CAAY;MA6BhBxG,EAAA,CAAAI,SAAA,GAAqD;MAArDJ,EAAA,CAAAqB,WAAA,YAAAgE,GAAA,CAAApD,gBAAA,oBAAqD;MAO7BjC,EAAA,CAAAI,SAAA,IAA2B;MAA3BJ,EAAA,CAAAyG,gBAAA,YAAApB,GAAA,CAAAnD,OAAA,CAAAjB,KAAA,CAA2B;MAG7BjB,EAAA,CAAAI,SAAA,GAA0B;MAA1BJ,EAAA,CAAAyG,gBAAA,YAAApB,GAAA,CAAAnD,OAAA,CAAAhB,IAAA,CAA0B;MAKzBlB,EAAA,CAAAI,SAAA,GAAiC;MAAjCJ,EAAA,CAAAyG,gBAAA,YAAApB,GAAA,CAAAnD,OAAA,CAAAoC,WAAA,CAAiC;MAKpBtE,EAAA,CAAAI,SAAA,GAAgC;MAAhCJ,EAAA,CAAAyG,gBAAA,YAAApB,GAAA,CAAAnD,OAAA,CAAAV,UAAA,CAAgC;MAC/CxB,EAAA,CAAAI,SAAA,EAA+B;MAA/BJ,EAAA,CAAAyG,gBAAA,YAAApB,GAAA,CAAAnD,OAAA,CAAAT,SAAA,CAA+B;;;iBD5C/ErC,WAAW,EAAAsH,EAAA,CAAAC,0BAAA,EAAEzH,YAAY,EAAA0H,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3H,WAAW,EAAA4H,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,oBAAA,EAAAJ,EAAA,CAAAK,mBAAA,EAAAL,EAAA,CAAAM,0BAAA,EAAAN,EAAA,CAAAO,eAAA,EAAAP,EAAA,CAAAQ,oBAAA,EAAAR,EAAA,CAAAS,iBAAA,EAAAT,EAAA,CAAAU,YAAA,EAAAV,EAAA,CAAAW,OAAA,EAAAX,EAAA,CAAAY,MAAA,EAAEtI,YAAY,EAAAuI,EAAA,CAAAC,UAAA,EAAEhI,mBAAmB,EAAEE,mBAAmB;EAAA+H,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}