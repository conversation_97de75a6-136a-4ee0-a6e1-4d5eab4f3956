import { Injectable, inject } from '@angular/core';
import { Activity, ActivityType, DayTracking } from '../models/activity.model';
import { Observable, from, of, catchError, map, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackerUnifiedService {
  private supabaseService = inject(SupabaseService);

  /**
   * Get all activity types
   */
  getActivityTypes(): Observable<ActivityType[]> {

    return from(
      this.supabaseService.getClient()
        .from('activity_types')
        .select('*')
        .eq('is_active', true)
        .order('order', { ascending: true })
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('TimeTrackerService: Error getting activity types:', response.error);
          return [];
        }

        return response.data as ActivityType[];
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error getting activity types:', error);
        return of([]);
      })
    );
  }

  /**
   * Get or create a day tracking entry for a user and date
   */
  getDayTracking(userId: string, date: string): Observable<DayTracking> {

    if (!userId || !date) {
      console.error('TimeTrackerService: Missing userId or date');
      return of({ id: '', user_id: userId, date: date });
    }

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', date)
        .single()
    ).pipe(
      switchMap(response => {
        if (response.error && response.error.code === 'PGRST116') { // Not found error
          return from(
            this.supabaseService.getClient()
              .from('day_tracking')
              .insert({ user_id: userId, date: date })
              .select()
              .single()
          ).pipe(
            map(insertResponse => {
              if (insertResponse.error) {
                console.error('TimeTrackerService: Error creating day tracking:', insertResponse.error);
                return { id: '', user_id: userId, date: date };
              }

              return insertResponse.data as DayTracking;
            }),
            catchError(error => {
              console.error('TimeTrackerService: Error creating day tracking:', error);
              return of({ id: '', user_id: userId, date: date });
            })
          );
        }

        if (response.error) {
          console.error('TimeTrackerService: Error getting day tracking:', response.error);
          return of({ id: '', user_id: userId, date: date });
        }

        return of(response.data as DayTracking);
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error in getDayTracking:', error);
        return of({ id: '', user_id: userId, date: date });
      })
    );
  }

  /**
   * Get activities for a day tracking entry
   */
  getActivitiesForDayTracking(dayTrackingId: string): Observable<Activity[]> {

    if (!dayTrackingId) {
      console.error('TimeTrackerService: Missing dayTrackingId');
      return of([]);
    }

    return from(
      this.supabaseService.getClient()
        .from('activities')
        .select('*')
        .eq('day_tracking_id', dayTrackingId)
        .order('name')
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('TimeTrackerService: Error getting activities:', response.error);
          return [];
        }

        return response.data as Activity[];
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error getting activities:', error);
        return of([]);
      })
    );
  }

  /**
   * Get activities for a user and date
   */
  getActivities(userId: string, date: string): Observable<Activity[]> {

    if (!userId || !date) {
      console.error('TimeTrackerService: Missing userId or date');
      return of([]);
    }

    return this.getDayTracking(userId, date).pipe(
      switchMap(dayTracking => {
        if (!dayTracking.id) {
          return of([]);
        }

        return this.getActivitiesForDayTracking(dayTracking.id);
      })
    );
  }

  /**
   * Get activities for a user within a date range
   */
  getActivitiesForDateRange(userId: string, startDate: string, endDate: string): Observable<Activity[]> {
    if (!userId || !startDate || !endDate) {
      console.error('TimeTrackerService: Missing userId, startDate or endDate');
      return of([]);
    }

    return from(
      this.supabaseService.getClient()
        .from('day_tracking')
        .select(`
          id,
          date,
          activities (
            id,
            day_tracking_id,
            name,
            emoji,
            hours,
            minutes,
            is_custom
          )
        `)
        .eq('user_id', userId)
        .gte('date', startDate)
        .lte('date', endDate)
    ).pipe(
      map(response => {
        if (response.error) {
          console.error('TimeTrackerService: Error getting activities for date range:', response.error);
          return [];
        }

        // Flatten activities from all days and add date info
        const allActivities: Activity[] = [];
        response.data.forEach((dayTracking: any) => {
          if (dayTracking.activities) {
            dayTracking.activities.forEach((activity: any) => {
              allActivities.push({
                ...activity,
                date: dayTracking.date // Add date info for better grouping
              });
            });
          }
        });

        return allActivities;
      }),
      catchError(error => {
        console.error('TimeTrackerService: Error getting activities for date range:', error);
        return of([]);
      })
    );
  }

  /**
   * Create a new activity
   */
  async createActivity(
    userId: string,
    date: string,
    name: string,
    emoji: string,
    hours: number,
    minutes: number,
    isCustom: boolean
  ): Promise<{ id: string, total_hours: string, remaining_hours: string }> {

    try {
      // First, get or create day tracking
      const { data: dayTracking, error: dayTrackingError } = await this.supabaseService.getClient()
        .from('day_tracking')
        .select('id')
        .eq('user_id', userId)
        .eq('date', date)
        .single();

      let dayTrackingId: string;

      if (dayTrackingError && dayTrackingError.code === 'PGRST116') { // Not found error
        // Create new day tracking
        const { data: newDayTracking, error: newDayTrackingError } = await this.supabaseService.getClient()
          .from('day_tracking')
          .insert({ user_id: userId, date: date })
          .select('id')
          .single();

        if (newDayTrackingError) {
          console.error('TimeTrackerService: Error creating day tracking:', newDayTrackingError);
          return Promise.reject(newDayTrackingError);
        }

        dayTrackingId = newDayTracking.id;
      } else if (dayTrackingError) {
        console.error('TimeTrackerService: Error getting day tracking:', dayTrackingError);
        return Promise.reject(dayTrackingError);
      } else {
        dayTrackingId = dayTracking.id;
      }

      // Now create the activity
      const { data: newActivity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .insert({
          day_tracking_id: dayTrackingId,
          name: name,
          emoji: emoji,
          hours: hours,
          minutes: minutes,
          is_custom: isCustom
        })
        .select('id')
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error creating activity:', activityError);
        return Promise.reject(activityError);
      }


      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        id: newActivity.id,
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error creating activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Update an activity
   */
  async updateActivity(activityId: string, hours: number, minutes: number): Promise<{ total_hours: string, remaining_hours: string }> {

    try {
      // First get the activity to get the day_tracking_id
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error getting activity:', activityError);
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      // Update the activity
      const { error: updateError } = await this.supabaseService.getClient()
        .from('activities')
        .update({ hours, minutes })
        .eq('id', activityId);

      if (updateError) {
        console.error('TimeTrackerService: Error updating activity:', updateError);
        return Promise.reject(updateError);
      }


      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error updating activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Delete an activity
   */
  async deleteActivity(activityId: string): Promise<{ total_hours: string, remaining_hours: string }> {

    try {
      // First get the activity to get the day_tracking_id
      const { data: activity, error: activityError } = await this.supabaseService.getClient()
        .from('activities')
        .select('day_tracking_id')
        .eq('id', activityId)
        .single();

      if (activityError) {
        console.error('TimeTrackerService: Error getting activity:', activityError);
        return Promise.reject(activityError);
      }

      const dayTrackingId = activity.day_tracking_id;

      // Delete the activity
      const { error: deleteError } = await this.supabaseService.getClient()
        .from('activities')
        .delete()
        .eq('id', activityId);

      if (deleteError) {
        console.error('TimeTrackerService: Error deleting activity:', deleteError);
        return Promise.reject(deleteError);
      }


      // Calculate total time
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      // We don't need to update day_tracking with totals anymore
      // Totals are calculated on the fly from activities

      return {
        total_hours: totalHours.toFixed(1),
        remaining_hours: remainingHours.toFixed(1)
      };
    } catch (error) {
      console.error('TimeTrackerService: Error deleting activity:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Get total time for a day tracking entry
   */
  async getTotalTime(dayTrackingId: string): Promise<{ total_hours: number, remaining_hours: number }> {

    try {
      const { data: activities, error: activitiesError } = await this.supabaseService.getClient()
        .from('activities')
        .select('hours, minutes')
        .eq('day_tracking_id', dayTrackingId);

      if (activitiesError) {
        console.error('TimeTrackerService: Error getting activities for total calculation:', activitiesError);
        return Promise.reject(activitiesError);
      }

      const totalMinutes = activities.reduce((total, act) => {
        return total + (act.hours * 60) + act.minutes;
      }, 0);

      const totalHours = totalMinutes / 60;
      const remainingHours = Math.max(0, 24 - totalHours);

      return {
        total_hours: totalHours,
        remaining_hours: remainingHours
      };
    } catch (error) {
      console.error('TimeTrackerService: Error getting total time:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Create a new activity type
   */
  async createActivityType(activityType: Omit<ActivityType, 'id'>): Promise<string> {

    try {
      const { data, error } = await this.supabaseService.getClient()
        .from('activity_types')
        .insert(activityType)
        .select('id')
        .single();

      if (error) {
        console.error('TimeTrackerService: Error creating activity type:', error);
        return Promise.reject(error);
      }

      return data.id;
    } catch (error) {
      console.error('TimeTrackerService: Error creating activity type:', error);
      return Promise.reject(error);
    }
  }
}
