{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\n// @ts-ignore\nimport { Chart, registerables } from 'chart.js';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\nChart.register(...registerables);\nlet TimeTrackerPage = class TimeTrackerPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // View mode\n    this.viewMode = 'day';\n    // Calendar data - starting with Monday\n    this.dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n    this.weekDates = [];\n    this.weekOffset = 0;\n    this.selectedDate = new Date();\n    // Activity data\n    this.activityTypes = [];\n    this.activities = [];\n    this.weekActivities = [];\n    this.monthActivities = [];\n    // Form inputs\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    this.customEmoji = '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    // UI state\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    // Time summary\n    this.totalTrackedHours = '0.0';\n    this.remainingHours = '24.0';\n    // Chart\n    this.timeChart = null;\n    this.supabaseService = inject(SupabaseService);\n    this.timeTrackerService = inject(TimeTrackerUnifiedService);\n  }\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadActivityTypes();\n        this.generateWeekDates();\n        this.loadActivities();\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeChart();\n    }, 500);\n  }\n  loadActivityTypes() {\n    this.timeTrackerService.getActivityTypes().subscribe(types => {\n      this.activityTypes = types;\n    });\n  }\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n    // Calculate the start of the week (Monday)\n    // For Monday as first day: 1 = Monday, 0 = Sunday (which should be treated as day 7)\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + 7 * this.weekOffset);\n    this.weekDates = [];\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n      const dateStr = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n      this.weekDates.push({\n        date: dateStr,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture\n      });\n    }\n  }\n  formatDate(date) {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  changeWeek(offset) {\n    this.weekOffset += offset;\n    this.generateWeekDates();\n  }\n  selectDate(dateStr) {\n    this.selectedDate = new Date(dateStr);\n    this.generateWeekDates();\n    this.loadActivities();\n  }\n  // View mode methods\n  setViewMode(mode) {\n    this.viewMode = mode;\n    this.loadDataForCurrentView();\n  }\n  loadDataForCurrentView() {\n    if (!this.userId) return;\n    switch (this.viewMode) {\n      case 'day':\n        this.loadActivities();\n        break;\n      case 'week':\n        this.loadWeekActivities();\n        break;\n      case 'month':\n        this.loadMonthActivities();\n        break;\n    }\n  }\n  loadWeekActivities() {\n    if (!this.userId) return;\n    const startOfWeek = this.getStartOfWeek(this.selectedDate);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n    this.timeTrackerService.getActivitiesForDateRange(this.userId, this.formatDate(startOfWeek), this.formatDate(endOfWeek)).subscribe(activities => {\n      this.weekActivities = this.calculateAverageActivities(activities, 7);\n      this.activities = this.weekActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n  loadMonthActivities() {\n    if (!this.userId) return;\n    const startOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);\n    const endOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);\n    this.timeTrackerService.getActivitiesForDateRange(this.userId, this.formatDate(startOfMonth), this.formatDate(endOfMonth)).subscribe(activities => {\n      const daysInMonth = endOfMonth.getDate();\n      this.monthActivities = this.calculateAverageActivities(activities, daysInMonth);\n      this.activities = this.monthActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n  getStartOfWeek(date) {\n    const currentDay = date.getDay(); // 0 = Sunday, 6 = Saturday\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(date);\n    startOfWeek.setDate(date.getDate() - currentDay + mondayOffset + 7 * this.weekOffset);\n    return startOfWeek;\n  }\n  calculateAverageActivities(activities, totalDays) {\n    // Group activities by name and emoji\n    const activityGroups = {};\n    activities.forEach(activity => {\n      const key = `${activity.name}_${activity.emoji}`;\n      if (!activityGroups[key]) {\n        activityGroups[key] = [];\n      }\n      activityGroups[key].push(activity);\n    });\n    // Calculate averages\n    const averageActivities = [];\n    Object.keys(activityGroups).forEach(key => {\n      const group = activityGroups[key];\n      const totalMinutes = group.reduce((sum, activity) => {\n        return sum + activity.hours * 60 + activity.minutes;\n      }, 0);\n      const averageMinutes = totalMinutes / totalDays;\n      const averageHours = Math.floor(averageMinutes / 60);\n      const remainingMinutes = Math.round(averageMinutes % 60);\n      if (averageHours > 0 || remainingMinutes > 0) {\n        averageActivities.push({\n          id: group[0].id,\n          day_tracking_id: group[0].day_tracking_id,\n          name: group[0].name,\n          emoji: group[0].emoji,\n          hours: averageHours,\n          minutes: remainingMinutes,\n          is_custom: group[0].is_custom\n        });\n      }\n    });\n    return averageActivities;\n  }\n  loadActivities() {\n    if (!this.userId) return;\n    const dateStr = this.formatDate(this.selectedDate);\n    const userId = this.userId; // Store in local variable to ensure it's not null\n    // First get the day tracking to get the totals\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\n      // Then get the activities\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe(activities => {\n        this.activities = activities;\n        // Calculate totals manually from activities\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      });\n    });\n  }\n  // Calculate totals manually from activities\n  calculateTotals() {\n    // Calculate total time in hours (including minutes as fraction of hour)\n    const totalHours = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    // Format to 1 decimal place\n    this.totalTrackedHours = totalHours.toFixed(1);\n    // Calculate remaining hours (max 0)\n    const remainingHours = Math.max(0, 24 - totalHours);\n    this.remainingHours = remainingHours.toFixed(1);\n  }\n  handleActivitySelection(event) {\n    const select = event.target;\n    const value = select.value;\n    this.selectedActivityId = value;\n    this.showStandardInput = value !== '' && value !== 'custom';\n    this.showCustomForm = value === 'custom';\n  }\n  addActivity() {\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    // Find the selected activity type\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\n    if (!activityType) return;\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.hoursInput;\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, activityType.name, activityType.emoji, this.hoursInput, this.minutesInput, false).then(result => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          // This will be set on the server\n          name: activityType.name,\n          emoji: activityType.emoji,\n          hours: this.hoursInput,\n          minutes: this.minutesInput,\n          is_custom: false\n        });\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  addCustomActivity() {\n    if (!this.userId || !this.customActivityName.trim()) {\n      alert('Please enter an activity name');\n      return;\n    }\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase());\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.customHoursInput;\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n      this.timeTrackerService.createActivity(userId, dateStr, this.customActivityName.trim(), this.customEmoji, this.customHoursInput, this.customMinutesInput, true).then(result => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '',\n          // This will be set on the server\n          name: this.customActivityName.trim(),\n          emoji: this.customEmoji,\n          hours: this.customHoursInput,\n          minutes: this.customMinutesInput,\n          is_custom: true\n        });\n        // Reset inputs\n        this.resetInputs();\n        // Calculate totals manually\n        this.calculateTotals();\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n  updateActivity(activity) {\n    if (!activity.id) return;\n    // Validate time inputs\n    if (activity.hours < 0) activity.hours = 0;\n    if (activity.minutes < 0) activity.minutes = 0;\n    if (activity.hours > 23) activity.hours = 23;\n    if (activity.minutes > 59) activity.minutes = 59;\n    // Check if total time would exceed 24 hours\n    const currentHours = activity.hours;\n    const currentMinutes = activity.minutes;\n    // Calculate total time for all activities except this one\n    const otherActivitiesTime = this.activities.filter(a => a.id !== activity.id).reduce((total, a) => total + a.hours + a.minutes / 60, 0);\n    // Add this activity's time\n    const totalTime = otherActivitiesTime + currentHours + currentMinutes / 60;\n    if (totalTime > 24) {\n      alert('Total time cannot exceed 24 hours');\n      // Reset to previous values\n      this.loadActivities();\n      return;\n    }\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\n      // Calculate totals manually\n      this.calculateTotals();\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error updating activity:', error);\n      alert('Error updating activity. Please try again.');\n      this.loadActivities(); // Reload to get the correct state\n    });\n  }\n  deleteActivity(activityId) {\n    if (!activityId) return;\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\n      // Remove from local activities\n      this.activities = this.activities.filter(a => a.id !== activityId);\n      // Calculate totals manually\n      this.calculateTotals();\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error deleting activity:', error);\n      alert('Error deleting activity. Please try again.');\n    });\n  }\n  resetInputs() {\n    // Save the current emoji before resetting\n    const currentEmoji = this.customEmoji;\n    // Reset form values\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    // Keep the last used emoji instead of resetting to default\n    // Only set default emoji if current is empty\n    this.customEmoji = currentEmoji || '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n    // Reset the select element to \"Select Activity\"\n    setTimeout(() => {\n      const selectElement = document.getElementById('activitySelect');\n      if (selectElement) {\n        selectElement.value = '';\n        // Directly update the UI state instead of using the event handler\n        this.selectedActivityId = '';\n        this.showStandardInput = false;\n        this.showCustomForm = false;\n      }\n    }, 0);\n  }\n  // We no longer need this method as we get the totals from the server\n  // Keeping it for backward compatibility\n  updateTotals() {\n    // This is now handled by the server\n  }\n  validateTotalTime(hours, minutes) {\n    // Calculate total time for all existing activities\n    const existingTime = this.activities.reduce((total, activity) => {\n      return total + activity.hours + activity.minutes / 60;\n    }, 0);\n    // Add new time\n    const totalTime = existingTime + hours + minutes / 60;\n    // Check if total time exceeds 24 hours\n    return totalTime <= 24;\n  }\n  initializeChart() {\n    const canvas = document.getElementById('timeChart');\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n    // Destroy existing chart if it exists\n    if (this.timeChart) {\n      this.timeChart.destroy();\n      this.timeChart = null;\n    }\n    // Register center text plugin\n    Chart.register({\n      id: 'centerText',\n      beforeDraw: chart => {\n        const ctx = chart.ctx;\n        const width = chart.width;\n        const height = chart.height;\n        ctx.restore();\n        ctx.font = 'bold 20px Inter';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n        // Draw \"24h\" text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillText('24h', width / 2, height / 2);\n        ctx.save();\n      }\n    });\n    try {\n      this.timeChart = new Chart(ctx, {\n        type: 'doughnut',\n        data: {\n          labels: [],\n          datasets: [{\n            data: [],\n            backgroundColor: [],\n            borderWidth: 0,\n            borderRadius: 5,\n            spacing: 2\n          }]\n        },\n        options: {\n          cutout: '75%',\n          radius: '90%',\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              enabled: true,\n              callbacks: {\n                label: function (context) {\n                  const value = context.raw;\n                  const hours = Math.floor(value);\n                  const minutes = Math.round((value - hours) * 60);\n                  return `${hours}h ${minutes}m`;\n                }\n              }\n            }\n          },\n          animation: {\n            animateRotate: true,\n            animateScale: true\n          }\n        }\n      });\n      this.updateChart();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error creating chart:', error);\n    }\n  }\n  updateChart() {\n    if (!this.timeChart) {\n      this.initializeChart();\n      return;\n    }\n    try {\n      const data = this.processActivitiesForChart();\n      this.timeChart.data.labels = data.labels;\n      this.timeChart.data.datasets[0].data = data.values;\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\n      this.timeChart.update();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error updating chart:', error);\n      // If there's an error, try to recreate the chart\n      this.timeChart = null;\n      setTimeout(() => {\n        this.initializeChart();\n      }, 100);\n    }\n  }\n  processActivitiesForChart() {\n    const labels = [];\n    const values = [];\n    this.activities.forEach(activity => {\n      // Include emoji in the label for better visualization\n      labels.push(`${activity.emoji} ${activity.name}`);\n      values.push(activity.hours + activity.minutes / 60);\n    });\n    // Add remaining time if total is less than 24 hours\n    const totalHours = values.reduce((a, b) => a + b, 0);\n    if (totalHours < 24) {\n      labels.push('Remaining');\n      values.push(24 - totalHours);\n    }\n    return {\n      labels,\n      values\n    };\n  }\n  generateColors(count) {\n    const colors = [];\n    // Use colors from activity_types in Supabase\n    this.activities.forEach(activity => {\n      // Find the matching activity type to get its color\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\n      // If we found a matching activity type with a color, use it; otherwise use a default color\n      if (activityType && activityType.color) {\n        colors.push(activityType.color);\n      } else if (activity.is_custom) {\n        // For custom activities, use a specific color\n        colors.push('#2C3E50'); // Royal Blue for custom activities\n      } else {\n        // Default color if no matching activity type or no color defined\n        colors.push('#2C3E50');\n      }\n    });\n    // Add dark color for remaining time\n    if (count > colors.length) {\n      colors.push('#1C1C1E');\n    }\n    return colors;\n  }\n};\n__decorate([ViewChild('timeChart')], TimeTrackerPage.prototype, \"chartCanvas\", void 0);\nTimeTrackerPage = __decorate([Component({\n  selector: 'app-time-tracker',\n  templateUrl: './time-tracker.page.html',\n  styleUrls: ['./time-tracker.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]\n})], TimeTrackerPage);\nexport { TimeTrackerPage };", "map": {"version": 3, "names": ["Component", "ViewChild", "inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "TimeTrackerUnifiedService", "take", "NavigationComponent", "Chart", "registerables", "SupabaseService", "EmojiInputDirective", "register", "TimeTrackerPage", "constructor", "userId", "viewMode", "dayNames", "weekDates", "weekOffset", "selectedDate", "Date", "activityTypes", "activities", "weekActivities", "monthActivities", "selectedActivityId", "hoursInput", "minutesInput", "customActivityName", "customEmoji", "customHoursInput", "customMinutesInput", "showStandardInput", "showCustomForm", "totalTrackedHours", "remainingHours", "timeChart", "supabaseService", "timeTrackerService", "ngOnInit", "currentUser$", "pipe", "subscribe", "authUser", "id", "loadActivityTypes", "generateWeekDates", "loadActivities", "ngAfterViewInit", "setTimeout", "initializeChart", "getActivityTypes", "types", "today", "currentDay", "getDay", "mondayOffset", "startOfWeek", "setDate", "getDate", "i", "date", "dateStr", "formatDate", "isToday", "isSameDay", "isSelected", "isFuture", "push", "day", "is_today", "is_selected", "is_future", "year", "getFullYear", "month", "String", "getMonth", "padStart", "date1", "date2", "changeWeek", "offset", "selectDate", "setViewMode", "mode", "loadDataForCurrentView", "loadWeekActivities", "loadMonthActivities", "getStartOfWeek", "endOfWeek", "getActivitiesForDateRange", "calculateAverageActivities", "calculateTotals", "updateChart", "startOfMonth", "endOfMonth", "daysInMonth", "totalDays", "activityGroups", "for<PERSON>ach", "activity", "key", "name", "emoji", "averageActivities", "Object", "keys", "group", "totalMinutes", "reduce", "sum", "hours", "minutes", "averageMinutes", "averageHours", "Math", "floor", "remainingMinutes", "round", "day_tracking_id", "is_custom", "getDayTracking", "getActivities", "totalHours", "total", "toFixed", "max", "handleActivitySelection", "event", "select", "target", "value", "addActivity", "alert", "validateTotalTime", "activityType", "find", "type", "existingActivity", "updatedHours", "updatedMinutes", "finalHours", "finalMinutes", "updateActivity", "then", "resetInputs", "catch", "error", "console", "createActivity", "result", "addCustomActivity", "trim", "toLowerCase", "currentHours", "currentMinutes", "otherActivitiesTime", "filter", "a", "totalTime", "deleteActivity", "activityId", "<PERSON><PERSON><PERSON><PERSON>", "selectElement", "document", "getElementById", "updateTotals", "existingTime", "canvas", "ctx", "getContext", "destroy", "beforeDraw", "chart", "width", "height", "restore", "font", "textAlign", "textBaseline", "fillStyle", "fillText", "save", "data", "labels", "datasets", "backgroundColor", "borderWidth", "borderRadius", "spacing", "options", "cutout", "radius", "plugins", "legend", "display", "tooltip", "enabled", "callbacks", "label", "context", "raw", "animation", "animateRotate", "animateScale", "processActivitiesForChart", "values", "generateColors", "length", "update", "b", "count", "colors", "color", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\time-tracker\\time-tracker.page.ts"], "sourcesContent": ["import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { Activity, ActivityType } from '../../models/activity.model';\nimport { TimeTrackerUnifiedService } from '../../services/time-tracker-unified.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../components/navigation/navigation.component';\n// @ts-ignore\nimport { Chart, registerables } from 'chart.js';\nimport { SupabaseService } from '../../services/supabase.service';\nimport { EmojiInputDirective } from '../../directives/emoji-input.directive';\n\nChart.register(...registerables);\n\ninterface DateDisplay {\n  date: string;\n  day: number;\n  is_today: boolean;\n  is_selected: boolean;\n  is_future: boolean;\n}\n\n@Component({\n  selector: 'app-time-tracker',\n  templateUrl: './time-tracker.page.html',\n  styleUrls: ['./time-tracker.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]\n})\nexport class TimeTrackerPage implements OnInit, AfterViewInit {\n  // User data\n  userId: string | null = null;\n\n  // View mode\n  viewMode: 'day' | 'week' | 'month' = 'day';\n\n  // Calendar data - starting with Monday\n  dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];\n  weekDates: DateDisplay[] = [];\n  weekOffset = 0;\n  selectedDate: Date = new Date();\n\n  // Activity data\n  activityTypes: ActivityType[] = [];\n  activities: Activity[] = [];\n  weekActivities: Activity[] = [];\n  monthActivities: Activity[] = [];\n\n  // Form inputs\n  selectedActivityId = '';\n  hoursInput = 0;\n  minutesInput = 0;\n  customActivityName = '';\n  customEmoji = '⚡';\n  customHoursInput = 0;\n  customMinutesInput = 0;\n\n  // UI state\n  showStandardInput = false;\n  showCustomForm = false;\n\n  // Time summary\n  totalTrackedHours = '0.0';\n  remainingHours = '24.0';\n\n  // Chart\n  timeChart: Chart | null = null;\n\n  @ViewChild('timeChart') chartCanvas: ElementRef | undefined;\n\n  private supabaseService = inject(SupabaseService);\n  private timeTrackerService = inject(TimeTrackerUnifiedService);\n\n  constructor() {}\n\n  ngOnInit() {\n    this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(authUser => {\n      if (authUser) {\n        this.userId = authUser.id;\n        this.loadActivityTypes();\n        this.generateWeekDates();\n        this.loadActivities();\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeChart();\n    }, 500);\n  }\n\n  loadActivityTypes() {\n    this.timeTrackerService.getActivityTypes().subscribe((types: ActivityType[]) => {\n      this.activityTypes = types;\n    });\n  }\n\n  generateWeekDates() {\n    const today = new Date();\n    const currentDay = today.getDay(); // 0 = Sunday, 6 = Saturday\n\n    // Calculate the start of the week (Monday)\n    // For Monday as first day: 1 = Monday, 0 = Sunday (which should be treated as day 7)\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(today);\n    startOfWeek.setDate(today.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));\n\n    this.weekDates = [];\n\n    for (let i = 0; i < 7; i++) {\n      const date = new Date(startOfWeek);\n      date.setDate(startOfWeek.getDate() + i);\n\n      const dateStr = this.formatDate(date);\n      const isToday = this.isSameDay(date, today);\n      const isSelected = this.isSameDay(date, this.selectedDate);\n      const isFuture = date > today;\n\n      this.weekDates.push({\n        date: dateStr,\n        day: date.getDate(),\n        is_today: isToday,\n        is_selected: isSelected,\n        is_future: isFuture\n      });\n    }\n  }\n\n  formatDate(date: Date): string {\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n\n  isSameDay(date1: Date, date2: Date): boolean {\n    return date1.getFullYear() === date2.getFullYear() &&\n           date1.getMonth() === date2.getMonth() &&\n           date1.getDate() === date2.getDate();\n  }\n\n  changeWeek(offset: number) {\n    this.weekOffset += offset;\n    this.generateWeekDates();\n  }\n\n  selectDate(dateStr: string) {\n    this.selectedDate = new Date(dateStr);\n    this.generateWeekDates();\n    this.loadActivities();\n  }\n\n  // View mode methods\n  setViewMode(mode: 'day' | 'week' | 'month') {\n    this.viewMode = mode;\n    this.loadDataForCurrentView();\n  }\n\n  loadDataForCurrentView() {\n    if (!this.userId) return;\n\n    switch (this.viewMode) {\n      case 'day':\n        this.loadActivities();\n        break;\n      case 'week':\n        this.loadWeekActivities();\n        break;\n      case 'month':\n        this.loadMonthActivities();\n        break;\n    }\n  }\n\n  loadWeekActivities() {\n    if (!this.userId) return;\n\n    const startOfWeek = this.getStartOfWeek(this.selectedDate);\n    const endOfWeek = new Date(startOfWeek);\n    endOfWeek.setDate(startOfWeek.getDate() + 6);\n\n    this.timeTrackerService.getActivitiesForDateRange(\n      this.userId,\n      this.formatDate(startOfWeek),\n      this.formatDate(endOfWeek)\n    ).subscribe((activities: Activity[]) => {\n      this.weekActivities = this.calculateAverageActivities(activities, 7);\n      this.activities = this.weekActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n\n  loadMonthActivities() {\n    if (!this.userId) return;\n\n    const startOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth(), 1);\n    const endOfMonth = new Date(this.selectedDate.getFullYear(), this.selectedDate.getMonth() + 1, 0);\n\n    this.timeTrackerService.getActivitiesForDateRange(\n      this.userId,\n      this.formatDate(startOfMonth),\n      this.formatDate(endOfMonth)\n    ).subscribe((activities: Activity[]) => {\n      const daysInMonth = endOfMonth.getDate();\n      this.monthActivities = this.calculateAverageActivities(activities, daysInMonth);\n      this.activities = this.monthActivities; // Use for chart\n      this.calculateTotals();\n      this.updateChart();\n    });\n  }\n\n  getStartOfWeek(date: Date): Date {\n    const currentDay = date.getDay(); // 0 = Sunday, 6 = Saturday\n    const mondayOffset = currentDay === 0 ? -6 : 1; // If Sunday, go back 6 days to previous Monday\n    const startOfWeek = new Date(date);\n    startOfWeek.setDate(date.getDate() - currentDay + mondayOffset + (7 * this.weekOffset));\n    return startOfWeek;\n  }\n\n  calculateAverageActivities(activities: Activity[], totalDays: number): Activity[] {\n    // Group activities by name and emoji\n    const activityGroups: { [key: string]: Activity[] } = {};\n\n    activities.forEach(activity => {\n      const key = `${activity.name}_${activity.emoji}`;\n      if (!activityGroups[key]) {\n        activityGroups[key] = [];\n      }\n      activityGroups[key].push(activity);\n    });\n\n    // Calculate averages\n    const averageActivities: Activity[] = [];\n\n    Object.keys(activityGroups).forEach(key => {\n      const group = activityGroups[key];\n      const totalMinutes = group.reduce((sum, activity) => {\n        return sum + (activity.hours * 60) + activity.minutes;\n      }, 0);\n\n      const averageMinutes = totalMinutes / totalDays;\n      const averageHours = Math.floor(averageMinutes / 60);\n      const remainingMinutes = Math.round(averageMinutes % 60);\n\n      if (averageHours > 0 || remainingMinutes > 0) {\n        averageActivities.push({\n          id: group[0].id,\n          day_tracking_id: group[0].day_tracking_id,\n          name: group[0].name,\n          emoji: group[0].emoji,\n          hours: averageHours,\n          minutes: remainingMinutes,\n          is_custom: group[0].is_custom\n        });\n      }\n    });\n\n    return averageActivities;\n  }\n\n  loadActivities() {\n    if (!this.userId) return;\n\n    const dateStr = this.formatDate(this.selectedDate);\n    const userId = this.userId; // Store in local variable to ensure it's not null\n\n    // First get the day tracking to get the totals\n    this.timeTrackerService.getDayTracking(userId, dateStr).subscribe(() => {\n      // Then get the activities\n      this.timeTrackerService.getActivities(userId, dateStr).subscribe((activities: Activity[]) => {\n        this.activities = activities;\n\n        // Calculate totals manually from activities\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      });\n    });\n  }\n\n  // Calculate totals manually from activities\n  calculateTotals() {\n    // Calculate total time in hours (including minutes as fraction of hour)\n    const totalHours = this.activities.reduce((total, activity) => {\n      return total + activity.hours + (activity.minutes / 60);\n    }, 0);\n\n    // Format to 1 decimal place\n    this.totalTrackedHours = totalHours.toFixed(1);\n\n    // Calculate remaining hours (max 0)\n    const remainingHours = Math.max(0, 24 - totalHours);\n    this.remainingHours = remainingHours.toFixed(1);\n\n  }\n\n  handleActivitySelection(event: Event) {\n    const select = event.target as HTMLSelectElement;\n    const value = select.value;\n\n    this.selectedActivityId = value;\n    this.showStandardInput = value !== '' && value !== 'custom';\n    this.showCustomForm = value === 'custom';\n  }\n\n  addActivity() {\n    if (!this.userId || !this.selectedActivityId || this.selectedActivityId === 'custom') return;\n    if (this.hoursInput === 0 && this.minutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.hoursInput, this.minutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n\n    // Find the selected activity type\n    const activityType = this.activityTypes.find(type => type.id === this.selectedActivityId);\n    if (!activityType) return;\n\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity => activity.name === activityType.name);\n\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.hoursInput;\n      const updatedMinutes = existingActivity.minutes + this.minutesInput;\n\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId as string; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n\n      this.timeTrackerService.createActivity(\n        userId,\n        dateStr,\n        activityType.name,\n        activityType.emoji,\n        this.hoursInput,\n        this.minutesInput,\n        false\n      ).then((result) => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '', // This will be set on the server\n          name: activityType.name,\n          emoji: activityType.emoji,\n          hours: this.hoursInput,\n          minutes: this.minutesInput,\n          is_custom: false\n        });\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n\n  addCustomActivity() {\n    if (!this.userId || !this.customActivityName.trim()) {\n      alert('Please enter an activity name');\n      return;\n    }\n\n    if (this.customHoursInput === 0 && this.customMinutesInput === 0) {\n      alert('Please enter a time greater than 0');\n      return;\n    }\n\n    // Check if total time would exceed 24 hours\n    if (!this.validateTotalTime(this.customHoursInput, this.customMinutesInput)) {\n      alert('⏱️ Total time cannot exceed 24 hours');\n      return;\n    }\n\n    // Check if activity already exists for this day\n    const existingActivity = this.activities.find(activity =>\n      activity.name.toLowerCase() === this.customActivityName.trim().toLowerCase()\n    );\n\n    if (existingActivity) {\n      // Update existing activity\n      const updatedHours = existingActivity.hours + this.customHoursInput;\n      const updatedMinutes = existingActivity.minutes + this.customMinutesInput;\n\n      // Convert excess minutes to hours\n      let finalHours = updatedHours + Math.floor(updatedMinutes / 60);\n      let finalMinutes = updatedMinutes % 60;\n\n      // Cap at 24 hours\n      if (finalHours > 23) {\n        finalHours = 23;\n        finalMinutes = 59;\n      }\n\n      this.timeTrackerService.updateActivity(existingActivity.id, finalHours, finalMinutes).then(() => {\n        // Update local activity\n        existingActivity.hours = finalHours;\n        existingActivity.minutes = finalMinutes;\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error updating activity:', error);\n        alert('Error updating activity. Please try again.');\n      });\n    } else {\n      // Create new activity\n      const userId = this.userId as string; // Cast to string to satisfy TypeScript\n      const dateStr = this.formatDate(this.selectedDate);\n\n      this.timeTrackerService.createActivity(\n        userId,\n        dateStr,\n        this.customActivityName.trim(),\n        this.customEmoji,\n        this.customHoursInput,\n        this.customMinutesInput,\n        true\n      ).then((result) => {\n        // We need the ID from the result for the new activity\n        // Add to local activities\n        this.activities.push({\n          id: result.id,\n          day_tracking_id: '', // This will be set on the server\n          name: this.customActivityName.trim(),\n          emoji: this.customEmoji,\n          hours: this.customHoursInput,\n          minutes: this.customMinutesInput,\n          is_custom: true\n        });\n\n        // Reset inputs\n        this.resetInputs();\n\n        // Calculate totals manually\n        this.calculateTotals();\n\n        // Update chart\n        this.updateChart();\n      }).catch(error => {\n        console.error('Error creating activity:', error);\n        alert('Error creating activity. Please try again.');\n      });\n    }\n  }\n\n  updateActivity(activity: Activity) {\n    if (!activity.id) return;\n\n    // Validate time inputs\n    if (activity.hours < 0) activity.hours = 0;\n    if (activity.minutes < 0) activity.minutes = 0;\n    if (activity.hours > 23) activity.hours = 23;\n    if (activity.minutes > 59) activity.minutes = 59;\n\n    // Check if total time would exceed 24 hours\n    const currentHours = activity.hours;\n    const currentMinutes = activity.minutes;\n\n    // Calculate total time for all activities except this one\n    const otherActivitiesTime = this.activities\n      .filter(a => a.id !== activity.id)\n      .reduce((total, a) => total + a.hours + (a.minutes / 60), 0);\n\n    // Add this activity's time\n    const totalTime = otherActivitiesTime + currentHours + (currentMinutes / 60);\n\n    if (totalTime > 24) {\n      alert('Total time cannot exceed 24 hours');\n      // Reset to previous values\n      this.loadActivities();\n      return;\n    }\n\n    this.timeTrackerService.updateActivity(activity.id, activity.hours, activity.minutes).then(() => {\n      // Calculate totals manually\n      this.calculateTotals();\n\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error updating activity:', error);\n      alert('Error updating activity. Please try again.');\n      this.loadActivities(); // Reload to get the correct state\n    });\n  }\n\n  deleteActivity(activityId: string) {\n    if (!activityId) return;\n\n    this.timeTrackerService.deleteActivity(activityId).then(() => {\n      // Remove from local activities\n      this.activities = this.activities.filter(a => a.id !== activityId);\n\n      // Calculate totals manually\n      this.calculateTotals();\n\n      // Update chart\n      this.updateChart();\n    }).catch(error => {\n      console.error('Error deleting activity:', error);\n      alert('Error deleting activity. Please try again.');\n    });\n  }\n\n  resetInputs() {\n    // Save the current emoji before resetting\n    const currentEmoji = this.customEmoji;\n\n    // Reset form values\n    this.selectedActivityId = '';\n    this.hoursInput = 0;\n    this.minutesInput = 0;\n    this.customActivityName = '';\n    // Keep the last used emoji instead of resetting to default\n    // Only set default emoji if current is empty\n    this.customEmoji = currentEmoji || '⚡';\n    this.customHoursInput = 0;\n    this.customMinutesInput = 0;\n    this.showStandardInput = false;\n    this.showCustomForm = false;\n\n    // Reset the select element to \"Select Activity\"\n    setTimeout(() => {\n      const selectElement = document.getElementById('activitySelect') as HTMLSelectElement;\n      if (selectElement) {\n        selectElement.value = '';\n\n        // Directly update the UI state instead of using the event handler\n        this.selectedActivityId = '';\n        this.showStandardInput = false;\n        this.showCustomForm = false;\n      }\n    }, 0);\n  }\n\n  // We no longer need this method as we get the totals from the server\n  // Keeping it for backward compatibility\n  updateTotals() {\n    // This is now handled by the server\n  }\n\n  validateTotalTime(hours: number, minutes: number): boolean {\n    // Calculate total time for all existing activities\n    const existingTime = this.activities.reduce((total, activity) => {\n      return total + activity.hours + (activity.minutes / 60);\n    }, 0);\n\n    // Add new time\n    const totalTime = existingTime + hours + (minutes / 60);\n\n    // Check if total time exceeds 24 hours\n    return totalTime <= 24;\n  }\n\n  initializeChart() {\n    const canvas = document.getElementById('timeChart') as HTMLCanvasElement;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Destroy existing chart if it exists\n    if (this.timeChart) {\n      this.timeChart.destroy();\n      this.timeChart = null;\n    }\n\n    // Register center text plugin\n    Chart.register({\n      id: 'centerText',\n      beforeDraw: (chart: any) => {\n        const ctx = chart.ctx;\n        const width = chart.width;\n        const height = chart.height;\n\n        ctx.restore();\n        ctx.font = 'bold 20px Inter';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n\n        // Draw \"24h\" text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillText('24h', width / 2, height / 2);\n        ctx.save();\n      }\n    });\n\n    try {\n      this.timeChart = new Chart(ctx, {\n        type: 'doughnut',\n        data: {\n          labels: [],\n          datasets: [{\n            data: [],\n            backgroundColor: [],\n            borderWidth: 0,\n            borderRadius: 5,\n            spacing: 2\n          }]\n        },\n        options: {\n          cutout: '75%',\n          radius: '90%',\n          plugins: {\n            legend: {\n              display: false\n            },\n            tooltip: {\n              enabled: true,\n              callbacks: {\n                label: function(context: any) {\n                  const value = context.raw as number;\n                  const hours = Math.floor(value);\n                  const minutes = Math.round((value - hours) * 60);\n                  return `${hours}h ${minutes}m`;\n                }\n              }\n            }\n          },\n          animation: {\n            animateRotate: true,\n            animateScale: true\n          }\n        }\n      });\n\n      this.updateChart();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error creating chart:', error);\n    }\n  }\n\n  updateChart() {\n    if (!this.timeChart) {\n      this.initializeChart();\n      return;\n    }\n\n    try {\n      const data = this.processActivitiesForChart();\n\n      this.timeChart.data.labels = data.labels;\n      this.timeChart.data.datasets[0].data = data.values;\n      this.timeChart.data.datasets[0].backgroundColor = this.generateColors(data.labels.length);\n\n      this.timeChart.update();\n    } catch (error) {\n      console.error('TimeTrackerPage: Error updating chart:', error);\n      // If there's an error, try to recreate the chart\n      this.timeChart = null;\n      setTimeout(() => {\n        this.initializeChart();\n      }, 100);\n    }\n  }\n\n  processActivitiesForChart() {\n    const labels: string[] = [];\n    const values: number[] = [];\n\n    this.activities.forEach(activity => {\n      // Include emoji in the label for better visualization\n      labels.push(`${activity.emoji} ${activity.name}`);\n      values.push(activity.hours + (activity.minutes / 60));\n    });\n\n    // Add remaining time if total is less than 24 hours\n    const totalHours = values.reduce((a, b) => a + b, 0);\n    if (totalHours < 24) {\n      labels.push('Remaining');\n      values.push(24 - totalHours);\n    }\n\n    return { labels, values };\n  }\n\n  generateColors(count: number) {\n    const colors: string[] = [];\n\n    // Use colors from activity_types in Supabase\n    this.activities.forEach(activity => {\n      // Find the matching activity type to get its color\n      const activityType = this.activityTypes.find(type => type.name === activity.name);\n\n      // If we found a matching activity type with a color, use it; otherwise use a default color\n      if (activityType && activityType.color) {\n        colors.push(activityType.color);\n      } else if (activity.is_custom) {\n        // For custom activities, use a specific color\n        colors.push('#2C3E50'); // Royal Blue for custom activities\n      } else {\n        // Default color if no matching activity type or no color defined\n        colors.push('#2C3E50');\n      }\n    });\n\n    // Add dark color for remaining time\n    if (count > colors.length) {\n      colors.push('#1C1C1E');\n    }\n\n    return colors;\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAyBC,SAAS,EAAcC,MAAM,QAAQ,eAAe;AAC/F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,yBAAyB,QAAQ,6CAA6C;AACvF,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF;AACA,SAASC,KAAK,EAAEC,aAAa,QAAQ,UAAU;AAC/C,SAASC,eAAe,QAAQ,iCAAiC;AACjE,SAASC,mBAAmB,QAAQ,wCAAwC;AAE5EH,KAAK,CAACI,QAAQ,CAAC,GAAGH,aAAa,CAAC;AAiBzB,IAAMI,eAAe,GAArB,MAAMA,eAAe;EA4C1BC,YAAA;IA3CA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAC,QAAQ,GAA6B,KAAK;IAE1C;IACA,KAAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5D,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAS,IAAIC,IAAI,EAAE;IAE/B;IACA,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,UAAU,GAAe,EAAE;IAC3B,KAAAC,cAAc,GAAe,EAAE;IAC/B,KAAAC,eAAe,GAAe,EAAE;IAEhC;IACA,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,kBAAkB,GAAG,EAAE;IACvB,KAAAC,WAAW,GAAG,GAAG;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,kBAAkB,GAAG,CAAC;IAEtB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,KAAK;IAEtB;IACA,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,MAAM;IAEvB;IACA,KAAAC,SAAS,GAAiB,IAAI;IAItB,KAAAC,eAAe,GAAGtC,MAAM,CAACU,eAAe,CAAC;IACzC,KAAA6B,kBAAkB,GAAGvC,MAAM,CAACK,yBAAyB,CAAC;EAE/C;EAEfmC,QAAQA,CAAA;IACN,IAAI,CAACF,eAAe,CAACG,YAAY,CAACC,IAAI,CACpCpC,IAAI,CAAC,CAAC,CAAC,CACR,CAACqC,SAAS,CAACC,QAAQ,IAAG;MACrB,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC7B,MAAM,GAAG6B,QAAQ,CAACC,EAAE;QACzB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;QACxB,IAAI,CAACC,cAAc,EAAE;MACvB;IACF,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,eAAe,EAAE;IACxB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAL,iBAAiBA,CAAA;IACf,IAAI,CAACP,kBAAkB,CAACa,gBAAgB,EAAE,CAACT,SAAS,CAAEU,KAAqB,IAAI;MAC7E,IAAI,CAAC/B,aAAa,GAAG+B,KAAK;IAC5B,CAAC,CAAC;EACJ;EAEAN,iBAAiBA,CAAA;IACf,MAAMO,KAAK,GAAG,IAAIjC,IAAI,EAAE;IACxB,MAAMkC,UAAU,GAAGD,KAAK,CAACE,MAAM,EAAE,CAAC,CAAC;IAEnC;IACA;IACA,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,MAAMG,WAAW,GAAG,IAAIrC,IAAI,CAACiC,KAAK,CAAC;IACnCI,WAAW,CAACC,OAAO,CAACL,KAAK,CAACM,OAAO,EAAE,GAAGL,UAAU,GAAGE,YAAY,GAAI,CAAC,GAAG,IAAI,CAACtC,UAAW,CAAC;IAExF,IAAI,CAACD,SAAS,GAAG,EAAE;IAEnB,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAIzC,IAAI,CAACqC,WAAW,CAAC;MAClCI,IAAI,CAACH,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAGC,CAAC,CAAC;MAEvC,MAAME,OAAO,GAAG,IAAI,CAACC,UAAU,CAACF,IAAI,CAAC;MACrC,MAAMG,OAAO,GAAG,IAAI,CAACC,SAAS,CAACJ,IAAI,EAAER,KAAK,CAAC;MAC3C,MAAMa,UAAU,GAAG,IAAI,CAACD,SAAS,CAACJ,IAAI,EAAE,IAAI,CAAC1C,YAAY,CAAC;MAC1D,MAAMgD,QAAQ,GAAGN,IAAI,GAAGR,KAAK;MAE7B,IAAI,CAACpC,SAAS,CAACmD,IAAI,CAAC;QAClBP,IAAI,EAAEC,OAAO;QACbO,GAAG,EAAER,IAAI,CAACF,OAAO,EAAE;QACnBW,QAAQ,EAAEN,OAAO;QACjBO,WAAW,EAAEL,UAAU;QACvBM,SAAS,EAAEL;OACZ,CAAC;IACJ;EACF;EAEAJ,UAAUA,CAACF,IAAU;IACnB,MAAMY,IAAI,GAAGZ,IAAI,CAACa,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACf,IAAI,CAACgB,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMT,GAAG,GAAGO,MAAM,CAACf,IAAI,CAACF,OAAO,EAAE,CAAC,CAACmB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAIN,GAAG,EAAE;EAClC;EAEAJ,SAASA,CAACc,KAAW,EAAEC,KAAW;IAChC,OAAOD,KAAK,CAACL,WAAW,EAAE,KAAKM,KAAK,CAACN,WAAW,EAAE,IAC3CK,KAAK,CAACF,QAAQ,EAAE,KAAKG,KAAK,CAACH,QAAQ,EAAE,IACrCE,KAAK,CAACpB,OAAO,EAAE,KAAKqB,KAAK,CAACrB,OAAO,EAAE;EAC5C;EAEAsB,UAAUA,CAACC,MAAc;IACvB,IAAI,CAAChE,UAAU,IAAIgE,MAAM;IACzB,IAAI,CAACpC,iBAAiB,EAAE;EAC1B;EAEAqC,UAAUA,CAACrB,OAAe;IACxB,IAAI,CAAC3C,YAAY,GAAG,IAAIC,IAAI,CAAC0C,OAAO,CAAC;IACrC,IAAI,CAAChB,iBAAiB,EAAE;IACxB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;EACAqC,WAAWA,CAACC,IAA8B;IACxC,IAAI,CAACtE,QAAQ,GAAGsE,IAAI;IACpB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACxE,MAAM,EAAE;IAElB,QAAQ,IAAI,CAACC,QAAQ;MACnB,KAAK,KAAK;QACR,IAAI,CAACgC,cAAc,EAAE;QACrB;MACF,KAAK,MAAM;QACT,IAAI,CAACwC,kBAAkB,EAAE;QACzB;MACF,KAAK,OAAO;QACV,IAAI,CAACC,mBAAmB,EAAE;QAC1B;IACJ;EACF;EAEAD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACzE,MAAM,EAAE;IAElB,MAAM2C,WAAW,GAAG,IAAI,CAACgC,cAAc,CAAC,IAAI,CAACtE,YAAY,CAAC;IAC1D,MAAMuE,SAAS,GAAG,IAAItE,IAAI,CAACqC,WAAW,CAAC;IACvCiC,SAAS,CAAChC,OAAO,CAACD,WAAW,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE5C,IAAI,CAACrB,kBAAkB,CAACqD,yBAAyB,CAC/C,IAAI,CAAC7E,MAAM,EACX,IAAI,CAACiD,UAAU,CAACN,WAAW,CAAC,EAC5B,IAAI,CAACM,UAAU,CAAC2B,SAAS,CAAC,CAC3B,CAAChD,SAAS,CAAEpB,UAAsB,IAAI;MACrC,IAAI,CAACC,cAAc,GAAG,IAAI,CAACqE,0BAA0B,CAACtE,UAAU,EAAE,CAAC,CAAC;MACpE,IAAI,CAACA,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACvC,IAAI,CAACsE,eAAe,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEAN,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAAC1E,MAAM,EAAE;IAElB,MAAMiF,YAAY,GAAG,IAAI3E,IAAI,CAAC,IAAI,CAACD,YAAY,CAACuD,WAAW,EAAE,EAAE,IAAI,CAACvD,YAAY,CAAC0D,QAAQ,EAAE,EAAE,CAAC,CAAC;IAC/F,MAAMmB,UAAU,GAAG,IAAI5E,IAAI,CAAC,IAAI,CAACD,YAAY,CAACuD,WAAW,EAAE,EAAE,IAAI,CAACvD,YAAY,CAAC0D,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IAEjG,IAAI,CAACvC,kBAAkB,CAACqD,yBAAyB,CAC/C,IAAI,CAAC7E,MAAM,EACX,IAAI,CAACiD,UAAU,CAACgC,YAAY,CAAC,EAC7B,IAAI,CAAChC,UAAU,CAACiC,UAAU,CAAC,CAC5B,CAACtD,SAAS,CAAEpB,UAAsB,IAAI;MACrC,MAAM2E,WAAW,GAAGD,UAAU,CAACrC,OAAO,EAAE;MACxC,IAAI,CAACnC,eAAe,GAAG,IAAI,CAACoE,0BAA0B,CAACtE,UAAU,EAAE2E,WAAW,CAAC;MAC/E,IAAI,CAAC3E,UAAU,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC;MACxC,IAAI,CAACqE,eAAe,EAAE;MACtB,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC;EACJ;EAEAL,cAAcA,CAAC5B,IAAU;IACvB,MAAMP,UAAU,GAAGO,IAAI,CAACN,MAAM,EAAE,CAAC,CAAC;IAClC,MAAMC,YAAY,GAAGF,UAAU,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD,MAAMG,WAAW,GAAG,IAAIrC,IAAI,CAACyC,IAAI,CAAC;IAClCJ,WAAW,CAACC,OAAO,CAACG,IAAI,CAACF,OAAO,EAAE,GAAGL,UAAU,GAAGE,YAAY,GAAI,CAAC,GAAG,IAAI,CAACtC,UAAW,CAAC;IACvF,OAAOuC,WAAW;EACpB;EAEAmC,0BAA0BA,CAACtE,UAAsB,EAAE4E,SAAiB;IAClE;IACA,MAAMC,cAAc,GAAkC,EAAE;IAExD7E,UAAU,CAAC8E,OAAO,CAACC,QAAQ,IAAG;MAC5B,MAAMC,GAAG,GAAG,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACG,KAAK,EAAE;MAChD,IAAI,CAACL,cAAc,CAACG,GAAG,CAAC,EAAE;QACxBH,cAAc,CAACG,GAAG,CAAC,GAAG,EAAE;MAC1B;MACAH,cAAc,CAACG,GAAG,CAAC,CAAClC,IAAI,CAACiC,QAAQ,CAAC;IACpC,CAAC,CAAC;IAEF;IACA,MAAMI,iBAAiB,GAAe,EAAE;IAExCC,MAAM,CAACC,IAAI,CAACR,cAAc,CAAC,CAACC,OAAO,CAACE,GAAG,IAAG;MACxC,MAAMM,KAAK,GAAGT,cAAc,CAACG,GAAG,CAAC;MACjC,MAAMO,YAAY,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEV,QAAQ,KAAI;QAClD,OAAOU,GAAG,GAAIV,QAAQ,CAACW,KAAK,GAAG,EAAG,GAAGX,QAAQ,CAACY,OAAO;MACvD,CAAC,EAAE,CAAC,CAAC;MAEL,MAAMC,cAAc,GAAGL,YAAY,GAAGX,SAAS;MAC/C,MAAMiB,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,cAAc,GAAG,EAAE,CAAC;MACpD,MAAMI,gBAAgB,GAAGF,IAAI,CAACG,KAAK,CAACL,cAAc,GAAG,EAAE,CAAC;MAExD,IAAIC,YAAY,GAAG,CAAC,IAAIG,gBAAgB,GAAG,CAAC,EAAE;QAC5Cb,iBAAiB,CAACrC,IAAI,CAAC;UACrBxB,EAAE,EAAEgE,KAAK,CAAC,CAAC,CAAC,CAAChE,EAAE;UACf4E,eAAe,EAAEZ,KAAK,CAAC,CAAC,CAAC,CAACY,eAAe;UACzCjB,IAAI,EAAEK,KAAK,CAAC,CAAC,CAAC,CAACL,IAAI;UACnBC,KAAK,EAAEI,KAAK,CAAC,CAAC,CAAC,CAACJ,KAAK;UACrBQ,KAAK,EAAEG,YAAY;UACnBF,OAAO,EAAEK,gBAAgB;UACzBG,SAAS,EAAEb,KAAK,CAAC,CAAC,CAAC,CAACa;SACrB,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOhB,iBAAiB;EAC1B;EAEA1D,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjC,MAAM,EAAE;IAElB,MAAMgD,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC5C,YAAY,CAAC;IAClD,MAAML,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;IAE5B;IACA,IAAI,CAACwB,kBAAkB,CAACoF,cAAc,CAAC5G,MAAM,EAAEgD,OAAO,CAAC,CAACpB,SAAS,CAAC,MAAK;MACrE;MACA,IAAI,CAACJ,kBAAkB,CAACqF,aAAa,CAAC7G,MAAM,EAAEgD,OAAO,CAAC,CAACpB,SAAS,CAAEpB,UAAsB,IAAI;QAC1F,IAAI,CAACA,UAAU,GAAGA,UAAU;QAE5B;QACA,IAAI,CAACuE,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAD,eAAeA,CAAA;IACb;IACA,MAAM+B,UAAU,GAAG,IAAI,CAACtG,UAAU,CAACwF,MAAM,CAAC,CAACe,KAAK,EAAExB,QAAQ,KAAI;MAC5D,OAAOwB,KAAK,GAAGxB,QAAQ,CAACW,KAAK,GAAIX,QAAQ,CAACY,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,IAAI,CAAC/E,iBAAiB,GAAG0F,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC;IAE9C;IACA,MAAM3F,cAAc,GAAGiF,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC;IACnD,IAAI,CAACzF,cAAc,GAAGA,cAAc,CAAC2F,OAAO,CAAC,CAAC,CAAC;EAEjD;EAEAE,uBAAuBA,CAACC,KAAY;IAClC,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAA2B;IAChD,MAAMC,KAAK,GAAGF,MAAM,CAACE,KAAK;IAE1B,IAAI,CAAC3G,kBAAkB,GAAG2G,KAAK;IAC/B,IAAI,CAACpG,iBAAiB,GAAGoG,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAK,QAAQ;IAC3D,IAAI,CAACnG,cAAc,GAAGmG,KAAK,KAAK,QAAQ;EAC1C;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACvH,MAAM,IAAI,CAAC,IAAI,CAACW,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,KAAK,QAAQ,EAAE;IACtF,IAAI,IAAI,CAACC,UAAU,KAAK,CAAC,IAAI,IAAI,CAACC,YAAY,KAAK,CAAC,EAAE;MACpD2G,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAAC7G,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE;MAC/D2G,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA;IACA,MAAME,YAAY,GAAG,IAAI,CAACnH,aAAa,CAACoH,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC9F,EAAE,KAAK,IAAI,CAACnB,kBAAkB,CAAC;IACzF,IAAI,CAAC+G,YAAY,EAAE;IAEnB;IACA,MAAMG,gBAAgB,GAAG,IAAI,CAACrH,UAAU,CAACmH,IAAI,CAACpC,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAKiC,YAAY,CAACjC,IAAI,CAAC;IAE9F,IAAIoC,gBAAgB,EAAE;MACpB;MACA,MAAMC,YAAY,GAAGD,gBAAgB,CAAC3B,KAAK,GAAG,IAAI,CAACtF,UAAU;MAC7D,MAAMmH,cAAc,GAAGF,gBAAgB,CAAC1B,OAAO,GAAG,IAAI,CAACtF,YAAY;MAEnE;MACA,IAAImH,UAAU,GAAGF,YAAY,GAAGxB,IAAI,CAACC,KAAK,CAACwB,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIE,YAAY,GAAGF,cAAc,GAAG,EAAE;MAEtC;MACA,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfC,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAACzG,kBAAkB,CAAC0G,cAAc,CAACL,gBAAgB,CAAC/F,EAAE,EAAEkG,UAAU,EAAEC,YAAY,CAAC,CAACE,IAAI,CAAC,MAAK;QAC9F;QACAN,gBAAgB,CAAC3B,KAAK,GAAG8B,UAAU;QACnCH,gBAAgB,CAAC1B,OAAO,GAAG8B,YAAY;QAEvC;QACA,IAAI,CAACG,WAAW,EAAE;QAElB;QACA,IAAI,CAACrD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMxH,MAAM,GAAG,IAAI,CAACA,MAAgB,CAAC,CAAC;MACtC,MAAMgD,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC5C,YAAY,CAAC;MAElD,IAAI,CAACmB,kBAAkB,CAACgH,cAAc,CACpCxI,MAAM,EACNgD,OAAO,EACP0E,YAAY,CAACjC,IAAI,EACjBiC,YAAY,CAAChC,KAAK,EAClB,IAAI,CAAC9E,UAAU,EACf,IAAI,CAACC,YAAY,EACjB,KAAK,CACN,CAACsH,IAAI,CAAEM,MAAM,IAAI;QAChB;QACA;QACA,IAAI,CAACjI,UAAU,CAAC8C,IAAI,CAAC;UACnBxB,EAAE,EAAE2G,MAAM,CAAC3G,EAAE;UACb4E,eAAe,EAAE,EAAE;UAAE;UACrBjB,IAAI,EAAEiC,YAAY,CAACjC,IAAI;UACvBC,KAAK,EAAEgC,YAAY,CAAChC,KAAK;UACzBQ,KAAK,EAAE,IAAI,CAACtF,UAAU;UACtBuF,OAAO,EAAE,IAAI,CAACtF,YAAY;UAC1B8F,SAAS,EAAE;SACZ,CAAC;QAEF;QACA,IAAI,CAACyB,WAAW,EAAE;QAElB;QACA,IAAI,CAACrD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAkB,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC1I,MAAM,IAAI,CAAC,IAAI,CAACc,kBAAkB,CAAC6H,IAAI,EAAE,EAAE;MACnDnB,KAAK,CAAC,+BAA+B,CAAC;MACtC;IACF;IAEA,IAAI,IAAI,CAACxG,gBAAgB,KAAK,CAAC,IAAI,IAAI,CAACC,kBAAkB,KAAK,CAAC,EAAE;MAChEuG,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACF;IAEA;IACA,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACzG,gBAAgB,EAAE,IAAI,CAACC,kBAAkB,CAAC,EAAE;MAC3EuG,KAAK,CAAC,sCAAsC,CAAC;MAC7C;IACF;IAEA;IACA,MAAMK,gBAAgB,GAAG,IAAI,CAACrH,UAAU,CAACmH,IAAI,CAACpC,QAAQ,IACpDA,QAAQ,CAACE,IAAI,CAACmD,WAAW,EAAE,KAAK,IAAI,CAAC9H,kBAAkB,CAAC6H,IAAI,EAAE,CAACC,WAAW,EAAE,CAC7E;IAED,IAAIf,gBAAgB,EAAE;MACpB;MACA,MAAMC,YAAY,GAAGD,gBAAgB,CAAC3B,KAAK,GAAG,IAAI,CAAClF,gBAAgB;MACnE,MAAM+G,cAAc,GAAGF,gBAAgB,CAAC1B,OAAO,GAAG,IAAI,CAAClF,kBAAkB;MAEzE;MACA,IAAI+G,UAAU,GAAGF,YAAY,GAAGxB,IAAI,CAACC,KAAK,CAACwB,cAAc,GAAG,EAAE,CAAC;MAC/D,IAAIE,YAAY,GAAGF,cAAc,GAAG,EAAE;MAEtC;MACA,IAAIC,UAAU,GAAG,EAAE,EAAE;QACnBA,UAAU,GAAG,EAAE;QACfC,YAAY,GAAG,EAAE;MACnB;MAEA,IAAI,CAACzG,kBAAkB,CAAC0G,cAAc,CAACL,gBAAgB,CAAC/F,EAAE,EAAEkG,UAAU,EAAEC,YAAY,CAAC,CAACE,IAAI,CAAC,MAAK;QAC9F;QACAN,gBAAgB,CAAC3B,KAAK,GAAG8B,UAAU;QACnCH,gBAAgB,CAAC1B,OAAO,GAAG8B,YAAY;QAEvC;QACA,IAAI,CAACG,WAAW,EAAE;QAElB;QACA,IAAI,CAACrD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMxH,MAAM,GAAG,IAAI,CAACA,MAAgB,CAAC,CAAC;MACtC,MAAMgD,OAAO,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC5C,YAAY,CAAC;MAElD,IAAI,CAACmB,kBAAkB,CAACgH,cAAc,CACpCxI,MAAM,EACNgD,OAAO,EACP,IAAI,CAAClC,kBAAkB,CAAC6H,IAAI,EAAE,EAC9B,IAAI,CAAC5H,WAAW,EAChB,IAAI,CAACC,gBAAgB,EACrB,IAAI,CAACC,kBAAkB,EACvB,IAAI,CACL,CAACkH,IAAI,CAAEM,MAAM,IAAI;QAChB;QACA;QACA,IAAI,CAACjI,UAAU,CAAC8C,IAAI,CAAC;UACnBxB,EAAE,EAAE2G,MAAM,CAAC3G,EAAE;UACb4E,eAAe,EAAE,EAAE;UAAE;UACrBjB,IAAI,EAAE,IAAI,CAAC3E,kBAAkB,CAAC6H,IAAI,EAAE;UACpCjD,KAAK,EAAE,IAAI,CAAC3E,WAAW;UACvBmF,KAAK,EAAE,IAAI,CAAClF,gBAAgB;UAC5BmF,OAAO,EAAE,IAAI,CAAClF,kBAAkB;UAChC0F,SAAS,EAAE;SACZ,CAAC;QAEF;QACA,IAAI,CAACyB,WAAW,EAAE;QAElB;QACA,IAAI,CAACrD,eAAe,EAAE;QAEtB;QACA,IAAI,CAACC,WAAW,EAAE;MACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDd,KAAK,CAAC,4CAA4C,CAAC;MACrD,CAAC,CAAC;IACJ;EACF;EAEAU,cAAcA,CAAC3C,QAAkB;IAC/B,IAAI,CAACA,QAAQ,CAACzD,EAAE,EAAE;IAElB;IACA,IAAIyD,QAAQ,CAACW,KAAK,GAAG,CAAC,EAAEX,QAAQ,CAACW,KAAK,GAAG,CAAC;IAC1C,IAAIX,QAAQ,CAACY,OAAO,GAAG,CAAC,EAAEZ,QAAQ,CAACY,OAAO,GAAG,CAAC;IAC9C,IAAIZ,QAAQ,CAACW,KAAK,GAAG,EAAE,EAAEX,QAAQ,CAACW,KAAK,GAAG,EAAE;IAC5C,IAAIX,QAAQ,CAACY,OAAO,GAAG,EAAE,EAAEZ,QAAQ,CAACY,OAAO,GAAG,EAAE;IAEhD;IACA,MAAM0C,YAAY,GAAGtD,QAAQ,CAACW,KAAK;IACnC,MAAM4C,cAAc,GAAGvD,QAAQ,CAACY,OAAO;IAEvC;IACA,MAAM4C,mBAAmB,GAAG,IAAI,CAACvI,UAAU,CACxCwI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnH,EAAE,KAAKyD,QAAQ,CAACzD,EAAE,CAAC,CACjCkE,MAAM,CAAC,CAACe,KAAK,EAAEkC,CAAC,KAAKlC,KAAK,GAAGkC,CAAC,CAAC/C,KAAK,GAAI+C,CAAC,CAAC9C,OAAO,GAAG,EAAG,EAAE,CAAC,CAAC;IAE9D;IACA,MAAM+C,SAAS,GAAGH,mBAAmB,GAAGF,YAAY,GAAIC,cAAc,GAAG,EAAG;IAE5E,IAAII,SAAS,GAAG,EAAE,EAAE;MAClB1B,KAAK,CAAC,mCAAmC,CAAC;MAC1C;MACA,IAAI,CAACvF,cAAc,EAAE;MACrB;IACF;IAEA,IAAI,CAACT,kBAAkB,CAAC0G,cAAc,CAAC3C,QAAQ,CAACzD,EAAE,EAAEyD,QAAQ,CAACW,KAAK,EAAEX,QAAQ,CAACY,OAAO,CAAC,CAACgC,IAAI,CAAC,MAAK;MAC9F;MACA,IAAI,CAACpD,eAAe,EAAE;MAEtB;MACA,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;MACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDd,KAAK,CAAC,4CAA4C,CAAC;MACnD,IAAI,CAACvF,cAAc,EAAE,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEAkH,cAAcA,CAACC,UAAkB;IAC/B,IAAI,CAACA,UAAU,EAAE;IAEjB,IAAI,CAAC5H,kBAAkB,CAAC2H,cAAc,CAACC,UAAU,CAAC,CAACjB,IAAI,CAAC,MAAK;MAC3D;MACA,IAAI,CAAC3H,UAAU,GAAG,IAAI,CAACA,UAAU,CAACwI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnH,EAAE,KAAKsH,UAAU,CAAC;MAElE;MACA,IAAI,CAACrE,eAAe,EAAE;MAEtB;MACA,IAAI,CAACC,WAAW,EAAE;IACpB,CAAC,CAAC,CAACqD,KAAK,CAACC,KAAK,IAAG;MACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDd,KAAK,CAAC,4CAA4C,CAAC;IACrD,CAAC,CAAC;EACJ;EAEAY,WAAWA,CAAA;IACT;IACA,MAAMiB,YAAY,GAAG,IAAI,CAACtI,WAAW;IAErC;IACA,IAAI,CAACJ,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B;IACA;IACA,IAAI,CAACC,WAAW,GAAGsI,YAAY,IAAI,GAAG;IACtC,IAAI,CAACrI,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,cAAc,GAAG,KAAK;IAE3B;IACAgB,UAAU,CAAC,MAAK;MACd,MAAMmH,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAsB;MACpF,IAAIF,aAAa,EAAE;QACjBA,aAAa,CAAChC,KAAK,GAAG,EAAE;QAExB;QACA,IAAI,CAAC3G,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACO,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACC,cAAc,GAAG,KAAK;MAC7B;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACA;EACAsI,YAAYA,CAAA;IACV;EAAA;EAGFhC,iBAAiBA,CAACvB,KAAa,EAAEC,OAAe;IAC9C;IACA,MAAMuD,YAAY,GAAG,IAAI,CAAClJ,UAAU,CAACwF,MAAM,CAAC,CAACe,KAAK,EAAExB,QAAQ,KAAI;MAC9D,OAAOwB,KAAK,GAAGxB,QAAQ,CAACW,KAAK,GAAIX,QAAQ,CAACY,OAAO,GAAG,EAAG;IACzD,CAAC,EAAE,CAAC,CAAC;IAEL;IACA,MAAM+C,SAAS,GAAGQ,YAAY,GAAGxD,KAAK,GAAIC,OAAO,GAAG,EAAG;IAEvD;IACA,OAAO+C,SAAS,IAAI,EAAE;EACxB;EAEA9G,eAAeA,CAAA;IACb,MAAMuH,MAAM,GAAGJ,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAsB;IACxE,IAAI,CAACG,MAAM,EAAE;IAEb,MAAMC,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IACnC,IAAI,CAACD,GAAG,EAAE;IAEV;IACA,IAAI,IAAI,CAACtI,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACwI,OAAO,EAAE;MACxB,IAAI,CAACxI,SAAS,GAAG,IAAI;IACvB;IAEA;IACA7B,KAAK,CAACI,QAAQ,CAAC;MACbiC,EAAE,EAAE,YAAY;MAChBiI,UAAU,EAAGC,KAAU,IAAI;QACzB,MAAMJ,GAAG,GAAGI,KAAK,CAACJ,GAAG;QACrB,MAAMK,KAAK,GAAGD,KAAK,CAACC,KAAK;QACzB,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAM;QAE3BN,GAAG,CAACO,OAAO,EAAE;QACbP,GAAG,CAACQ,IAAI,GAAG,iBAAiB;QAC5BR,GAAG,CAACS,SAAS,GAAG,QAAQ;QACxBT,GAAG,CAACU,YAAY,GAAG,QAAQ;QAE3B;QACAV,GAAG,CAACW,SAAS,GAAG,SAAS;QACzBX,GAAG,CAACY,QAAQ,CAAC,KAAK,EAAEP,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC;QAC1CN,GAAG,CAACa,IAAI,EAAE;MACZ;KACD,CAAC;IAEF,IAAI;MACF,IAAI,CAACnJ,SAAS,GAAG,IAAI7B,KAAK,CAACmK,GAAG,EAAE;QAC9BhC,IAAI,EAAE,UAAU;QAChB8C,IAAI,EAAE;UACJC,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,CAAC;YACTF,IAAI,EAAE,EAAE;YACRG,eAAe,EAAE,EAAE;YACnBC,WAAW,EAAE,CAAC;YACdC,YAAY,EAAE,CAAC;YACfC,OAAO,EAAE;WACV;SACF;QACDC,OAAO,EAAE;UACPC,MAAM,EAAE,KAAK;UACbC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACPC,MAAM,EAAE;cACNC,OAAO,EAAE;aACV;YACDC,OAAO,EAAE;cACPC,OAAO,EAAE,IAAI;cACbC,SAAS,EAAE;gBACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;kBAC1B,MAAMrE,KAAK,GAAGqE,OAAO,CAACC,GAAa;kBACnC,MAAM1F,KAAK,GAAGI,IAAI,CAACC,KAAK,CAACe,KAAK,CAAC;kBAC/B,MAAMnB,OAAO,GAAGG,IAAI,CAACG,KAAK,CAAC,CAACa,KAAK,GAAGpB,KAAK,IAAI,EAAE,CAAC;kBAChD,OAAO,GAAGA,KAAK,KAAKC,OAAO,GAAG;gBAChC;;;WAGL;UACD0F,SAAS,EAAE;YACTC,aAAa,EAAE,IAAI;YACnBC,YAAY,EAAE;;;OAGnB,CAAC;MAEF,IAAI,CAAC/G,WAAW,EAAE;IACpB,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAChE;EACF;EAEAtD,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC1D,SAAS,EAAE;MACnB,IAAI,CAACc,eAAe,EAAE;MACtB;IACF;IAEA,IAAI;MACF,MAAMsI,IAAI,GAAG,IAAI,CAACsB,yBAAyB,EAAE;MAE7C,IAAI,CAAC1K,SAAS,CAACoJ,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxC,IAAI,CAACrJ,SAAS,CAACoJ,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACF,IAAI,GAAGA,IAAI,CAACuB,MAAM;MAClD,IAAI,CAAC3K,SAAS,CAACoJ,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACC,eAAe,GAAG,IAAI,CAACqB,cAAc,CAACxB,IAAI,CAACC,MAAM,CAACwB,MAAM,CAAC;MAEzF,IAAI,CAAC7K,SAAS,CAAC8K,MAAM,EAAE;IACzB,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D;MACA,IAAI,CAAChH,SAAS,GAAG,IAAI;MACrBa,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,eAAe,EAAE;MACxB,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEA4J,yBAAyBA,CAAA;IACvB,MAAMrB,MAAM,GAAa,EAAE;IAC3B,MAAMsB,MAAM,GAAa,EAAE;IAE3B,IAAI,CAACzL,UAAU,CAAC8E,OAAO,CAACC,QAAQ,IAAG;MACjC;MACAoF,MAAM,CAACrH,IAAI,CAAC,GAAGiC,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACE,IAAI,EAAE,CAAC;MACjDwG,MAAM,CAAC3I,IAAI,CAACiC,QAAQ,CAACW,KAAK,GAAIX,QAAQ,CAACY,OAAO,GAAG,EAAG,CAAC;IACvD,CAAC,CAAC;IAEF;IACA,MAAMW,UAAU,GAAGmF,MAAM,CAACjG,MAAM,CAAC,CAACiD,CAAC,EAAEoD,CAAC,KAAKpD,CAAC,GAAGoD,CAAC,EAAE,CAAC,CAAC;IACpD,IAAIvF,UAAU,GAAG,EAAE,EAAE;MACnB6D,MAAM,CAACrH,IAAI,CAAC,WAAW,CAAC;MACxB2I,MAAM,CAAC3I,IAAI,CAAC,EAAE,GAAGwD,UAAU,CAAC;IAC9B;IAEA,OAAO;MAAE6D,MAAM;MAAEsB;IAAM,CAAE;EAC3B;EAEAC,cAAcA,CAACI,KAAa;IAC1B,MAAMC,MAAM,GAAa,EAAE;IAE3B;IACA,IAAI,CAAC/L,UAAU,CAAC8E,OAAO,CAACC,QAAQ,IAAG;MACjC;MACA,MAAMmC,YAAY,GAAG,IAAI,CAACnH,aAAa,CAACoH,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACnC,IAAI,KAAKF,QAAQ,CAACE,IAAI,CAAC;MAEjF;MACA,IAAIiC,YAAY,IAAIA,YAAY,CAAC8E,KAAK,EAAE;QACtCD,MAAM,CAACjJ,IAAI,CAACoE,YAAY,CAAC8E,KAAK,CAAC;MACjC,CAAC,MAAM,IAAIjH,QAAQ,CAACoB,SAAS,EAAE;QAC7B;QACA4F,MAAM,CAACjJ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL;QACAiJ,MAAM,CAACjJ,IAAI,CAAC,SAAS,CAAC;MACxB;IACF,CAAC,CAAC;IAEF;IACA,IAAIgJ,KAAK,GAAGC,MAAM,CAACJ,MAAM,EAAE;MACzBI,MAAM,CAACjJ,IAAI,CAAC,SAAS,CAAC;IACxB;IAEA,OAAOiJ,MAAM;EACf;CACD;AAhrByBE,UAAA,EAAvBzN,SAAS,CAAC,WAAW,CAAC,C,mDAAqC;AAvCjDc,eAAe,GAAA2M,UAAA,EAP3B1N,SAAS,CAAC;EACT2N,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,0BAA0B;EACvCC,SAAS,EAAE,CAAC,0BAA0B,CAAC;EACvCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1N,WAAW,EAAEF,YAAY,EAAEC,WAAW,EAAEE,YAAY,EAAEG,mBAAmB,EAAEI,mBAAmB;CACzG,CAAC,C,EACWE,eAAe,CAutB3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}