{"ast": null, "code": "// Supabase database models\nexport {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\models\\supabase.models.ts"], "sourcesContent": ["// Supabase database models\n\n// User profile model\nexport interface UserProfile {\n  id: string;                      // PK, FK to auth.users.id\n  username: string;                // unique\n  profile_picture?: string;        // nullable\n  active: boolean;                 // default false\n  strength_xp: number;             // default 0\n  money_xp: number;                // default 0\n  health_xp: number;               // default 0\n  knowledge_xp: number;            // default 0\n  level: number;                   // default 0\n  title: string;                   // default '🥚 Beginner'\n  bio: string;                     // default ''\n  timezone: string;                // default 'UTC'\n  friend_code?: string;            // unique, nullable\n  friend_code_expiry?: Date;       // nullable\n  plan?: string;                   // nullable\n  start_of_current_plan?: Date;    // nullable\n  end_of_current_plan?: Date;      // nullable\n  auto_renew: boolean;             // default true\n  start_of_sick_days?: Date;       // nullable\n  end_of_sick_days?: Date;         // nullable\n  sidequests_switch: boolean;      // default true\n  show_celebration: boolean;       // default true\n  celebration_name: string;        // default 'Another Day, Another W'\n  celebration_description: string; // default \"You've completed all your quests for today. Keep up the great work!\"\n  celebration_emoji?: string;      // nullable\n  subscription_status: string;     // default 'email marketing'\n}\n\n// User badges model\nexport interface UserBadge {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id, unique\n  badge_newbie?: boolean;          // default false\n  badge_warrior?: boolean;         // default false\n  badge_monk?: boolean;            // default false\n  badge_nonchalant?: boolean;      // default false\n  badge_hardcore?: boolean;        // default false\n  badge_disciplined_machine?: boolean; // default false\n  badge_high_performer?: boolean;  // default false\n  badge_master_of_consistency?: boolean; // default false\n  badge_peak_performer?: boolean;  // default false\n  badge_elite_operator?: boolean;  // default false\n  badge_indestructible?: boolean;  // default false\n  badge_ultra_human?: boolean;     // default false\n  badge_professional?: boolean;    // default false\n  created_at: Date;                // default now()\n  updated_at: Date;                // default now()\n}\n\n// Subscription history model\nexport interface SubscriptionHistory {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id\n  plan: string;                    // ('monthly', 'yearly')\n  start_date_of_subscription: Date;\n  end_date_of_subscription?: Date; // nullable\n  created: Date;                   // default now()\n  updated: Date;                   // default now()\n}\n\n// Quest model\nexport interface Quest {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id nullable\n  name: string;\n  description?: string;            // nullable\n  active: boolean;                 // default true\n  quest_type: string;              // ('build', 'quit')\n  streak: number;                  // default 0\n  goal_value: number;              // default 1\n  goal_unit: string;               // (steps, minutes, etc.)\n  goal_period: string;             // ('day', 'week', 'month')\n  priority: string;                // ('basic', 'high')\n  category: string;                // ('money', 'health', 'strength', 'knowledge')\n  task_days_of_week?: string;      // nullable\n  task_days_of_month?: string;     // nullable\n  custom_reminder_times?: string;  // nullable\n  created_at: Date;                // default now()\n  emoji: string;                   // default '🏆'\n}\n\n// Quest progress model\nexport interface QuestProgress {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id\n  quest_id: string;                // FK to quests.id\n  date: Date;\n  completed: boolean;              // default false\n  value_achieved: number;          // default 0\n  historical_streak?: number;      // Not stored in DB, calculated on the fly\n}\n\n// Daily sidequest pool model\nexport interface DailySideQuestPool {\n  id: string;                      // PK\n  name: string;\n  description?: string;            // nullable\n  goal_value: number;              // default 1\n  category: string;                // ('money', 'health', 'strength', 'knowledge')\n  goal_unit: string;               // ('steps', 'minutes', etc.)\n  active: boolean;                 // default true\n  emoji: string;                   // default '🎯'\n}\n\n// User daily sidequests model\nexport interface UserDailySideQuest {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id\n  current_quest_id: string;        // FK to daily_sidequest_pool.id\n  streak: number;                  // default 0\n  last_completed_date?: string | null;    // YYYY-MM-DD format, nullable\n  date_assigned: string;           // YYYY-MM-DD format\n  completed: boolean;              // default false\n  value_achieved: number;          // default 0\n  category: string;                // ('money', 'health', 'strength', 'knowledge')\n  emoji: string;                   // default '🎯'\n}\n\n// Day tracking model\nexport interface DayTracking {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id\n  date: Date;\n}\n\n// Activities model\nexport interface Activity {\n  id: string;                      // PK\n  day_tracking_id: string;         // FK to day_tracking.id\n  name: string;\n  emoji: string;                   // default '⚡'\n  hours: number;                   // default 0\n  minutes: number;                 // default 0\n  is_custom: boolean;              // default false\n}\n\n// Activity types model\nexport interface ActivityType {\n  id: string;                      // PK\n  name: string;                    // unique\n  emoji: string;\n  is_active: boolean;              // default true\n  order: number;                   // default 0\n}\n\n// Goals model\nexport interface Goal {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id nullable\n  name: string;\n  description?: string;            // nullable\n  emoji: string;                   // default '🎯'\n  start_date: Date;                // default now()\n  end_date?: Date;                 // nullable\n  goal_value: number;              // default 1\n  current_value: number;           // default 0\n  goal_unit: string;\n  before_photo?: string;           // nullable\n  after_photo?: string;            // nullable\n  public: boolean;                 // default false\n}\n\n// Microgoals model\nexport interface MicroGoal {\n  id: string;                      // PK\n  goal_id: string;                 // FK to goals.id\n  title: string;\n  completed: boolean;              // default false\n  completed_at?: Date;             // nullable\n}\n\n// Goal journal entries model\nexport interface GoalJournalEntry {\n  id: string;                      // PK\n  goal_id: string;                 // FK to goals.id\n  milestone_percentage: number;    // e.g., 20, 40, 60...\n  content: string;\n  created_at: Date;                // default now()\n}\n\n// Friends model\nexport interface Friend {\n  id: string;                      // PK\n  user_id: string;                 // FK to profiles.id\n  friend_id: string;               // FK to profiles.id\n  created: Date;                   // default now()\n}\n\n// Groups model\nexport interface Group {\n  id: string;                      // PK\n  name: string;\n  emoji: string;                   // default '👥'\n  admin_id: string;                // FK to profiles.id\n  enable_sidequests: boolean;      // default true\n  created: Date;                   // default now()\n  timezone: string;                // default 'UTC'\n  level: number;                   // default 0\n  strength_xp: number;             // default 0\n  money_xp: number;                // default 0\n  health_xp: number;               // default 0\n  knowledge_xp: number;            // default 0\n  invitation_code?: string;        // nullable\n  code_expiry?: Date;              // nullable\n}\n\n// Group members model\nexport interface GroupMember {\n  id: string;                      // PK\n  group_id: string;                // FK to groups.id\n  user_id: string;                 // FK to profiles.id\n  nickname: string;\n  is_admin: boolean;               // default false\n  joined_date: Date;               // default now()\n}\n\n// Group quests model\nexport interface GroupQuest {\n  id: string;                      // PK\n  group_id: string;                // FK to groups.id\n  name: string;\n  description?: string;            // nullable\n  emoji: string;                   // default '🎯'\n  category: string;\n  priority: string;                // ('basic', 'high')\n  quest_type: string;              // ('build', 'quit')\n  goal_value: number;              // default 1\n  goal_unit: string;\n  goal_period: string;\n  task_days_of_week?: string;      // nullable\n  task_days_of_month?: string;     // nullable\n  streak: number;                  // default 0\n  created: Date;                   // default now()\n}\n\n// Group quest progress model\nexport interface GroupQuestProgress {\n  id: string;                      // PK\n  quest_id: string;                // FK to group_quests.id\n  user_id: string;                 // FK to profiles.id\n  date: Date;\n  value_achieved: number;          // default 0\n  completed: boolean;              // default false\n}\n\n// Group join requests model\nexport interface GroupJoinRequest {\n  id: string;                      // PK\n  group_id: string;                // FK to groups.id\n  username_invited: string;        // Username of the invited user\n  invited_by: string;              // FK to profiles.id (the user who sent the invitation)\n  created: Date;                   // When the request was made\n}\n"], "mappings": "AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}