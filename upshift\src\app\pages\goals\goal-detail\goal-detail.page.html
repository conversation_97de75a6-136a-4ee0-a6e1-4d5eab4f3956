<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo-wrap">
            <div class="logo">
                <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
                <span>Upshift</span>
            </div>
        </div>
        <h1>{{ goal?.name }}</h1>
    </header>
    <a [routerLink]="['/goals']" class="back-link">&larr; Back to goals</a>

    <section class="goal-card" *ngIf="goal">
        <!-- Goal Title Section -->
        <div class="goal-title-container" [style.display]="showTitleForm ? 'none' : 'flex'">
            <h2>{{goal.emoji}} {{ goal.name }}</h2>
            <button class="edit-bio-btn" (click)="showTitleForm = true">Edit</button>
        </div>

        <div class="title-form" [style.display]="showTitleForm ? 'block' : 'none'">
            <form (ngSubmit)="updateTitle()">
                <div class="title-inputs">
                    <input type="text" name="emoji" maxlength="2" placeholder="🎯" [(ngModel)]="editedEmoji" class="emoji-input">
                    <input type="text" name="name" maxlength="100" placeholder="Goal name" [(ngModel)]="editedName" class="name-input">
                </div>
                <div class="form-buttons">
                    <button type="submit" class="save-btn">Save</button>
                    <button type="button" class="cancel-btn" (click)="cancelEditTitle()">Cancel</button>
                </div>
            </form>
        </div>

        <!-- Description Section -->
        <div class="description-container" [style.display]="showDescriptionForm ? 'none' : 'flex'">
            <p class="goal-desc">{{ goal.description }}</p>
            <button id="edit-description-btn" class="edit-bio-btn" (click)="showDescriptionForm = true">Edit</button>
        </div>

        <div id="description-form" class="description-form" [style.display]="showDescriptionForm ? 'block' : 'none'">
            <form (ngSubmit)="updateDescription()">
                <textarea name="description" maxlength="500" placeholder="Enter goal description" [(ngModel)]="editedDescription"></textarea>
                <button type="submit" name="edit_description" class="save-btn">Save</button>
                <button type="button" id="cancel-edit-btn" class="cancel-btn" (click)="cancelEditDescription()">Cancel</button>
            </form>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="progressPercent"></div>
            </div>
            <div class="progress-info">
                <span><strong>{{ goal.current_value }}</strong> / {{ goal.goal_value }} {{ goal.goal_unit }}</span>
                <span class="percent">{{ progressPercent }}%</span>
            </div>
        </div>

        <form (ngSubmit)="updateProgress()" class="progress-update">
            <label for="current_value">Update value:</label>
            <div class="save-bar">
                <input type="number" name="current_value" step="any" [(ngModel)]="currentValue">
                <button name="update_progress" type="submit">Save</button>
            </div>
        </form>

        <div class="goal-settings">
            <div class="public-toggle">
                <label>
                    <input type="checkbox" [checked]="goal.public" (change)="togglePublic()">
                    Show on profile (public)
                </label>
            </div>
        </div>
    </section>

    <section class="microgoals-section" *ngIf="goal">
        <h3>Microgoals</h3>
        <ul class="microgoals-list">
            <li class="microgoal-item" *ngFor="let microgoal of microgoals">
                <div class="microgoal-form">
                    <button class="micro-btn" type="button" (click)="toggleMicrogoal(microgoal)">
                        <span class="checkmark" [class.checked]="microgoal.completed">
                            {{ microgoal.completed ? '✔' : '☐' }}
                        </span>
                    </button>
                    <span class="microgoal-title">{{ microgoal.title }}</span>
                </div>
                <div class="delete-form">
                    <button class="delete-btn" type="button" (click)="deleteMicrogoal(microgoal)">✖</button>
                </div>
            </li>
        </ul>
        <form (ngSubmit)="addMicrogoal()" class="microgoal-add">
            <input name="microgoal_title" type="text" placeholder="New microgoal..." [(ngModel)]="newMicrogoalTitle">
            <button name="add_microgoal" type="submit">➕ Add</button>
        </form>
    </section>

    <section class="journal-section" *ngIf="journalEntries.length > 0">
        <h3>📝 Goal Journal:</h3>
        <div class="journal-entry" *ngFor="let entry of journalEntries">
            <div class="milestone">🎯 {{ entry.milestone_percentage }}%</div>
            <p>{{ entry.content }}</p>
        </div>
    </section>

    <div class="journal-modal fancy" *ngIf="showJournalModal && nextMilestone">
        <p>🎉 <strong>Congrats!</strong> You've reached <span class="milestone-highlight">{{ nextMilestone }}%</span> of your goal.</p>

        <form (ngSubmit)="addJournalEntry()" class="inline-journal-form">
            <textarea name="journal_content" rows="4" placeholder="Write how you're progressing..." [(ngModel)]="journalContent"></textarea>
            <button type="submit" name="add_journal">✍️ Add journal entry</button>
        </form>
    </div>

    <div class="delete-goal-container">
        <button type="button" class="delete-goal-btn" (click)="confirmDeleteGoal()">Remove Goal</button>
    </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
