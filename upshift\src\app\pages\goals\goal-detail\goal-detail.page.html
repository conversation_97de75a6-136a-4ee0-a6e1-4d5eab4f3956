<!-- Exact HTML from Django template with Angular syntax -->
<div class="container">
    <header>
        <div class="logo-wrap">
            <div class="logo">
                <img src="assets/images/upshift_icon_mini.svg" alt="Upshift">
                <span>Upshift</span>
            </div>
        </div>
        <h1>{{ goal?.name }}</h1>
    </header>
    <a [routerLink]="['/goals']" class="back-link">&larr; Back to goals</a>

    <section class="goal-card" *ngIf="goal">
        <!-- Goal Info Section -->
        <div class="goal-info-container" [style.display]="showEditForm ? 'none' : 'block'">
            <h2>{{goal.emoji}} {{ goal.name }}</h2>
            <div class="description-container">
                <p class="goal-desc">{{ goal.description }}</p>
                <button class="edit-bio-btn" (click)="showEditForm = true">Edit</button>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="edit-form" [style.display]="showEditForm ? 'block' : 'none'">
            <form (ngSubmit)="updateGoalInfo()">
                <div class="title-inputs">
                    <input type="text" name="emoji" placeholder="🎯" [(ngModel)]="editedEmoji" class="emoji-input" appEmojiInput>
                    <input type="text" name="name" maxlength="100" placeholder="Goal name" [(ngModel)]="editedName" class="name-input">
                </div>
                <textarea name="description" maxlength="500" placeholder="Enter goal description" [(ngModel)]="editedDescription" class="description-textarea"></textarea>
                <div class="form-buttons">
                    <button type="submit" class="save-btn">Save</button>
                    <button type="button" class="cancel-btn" (click)="cancelEdit()">Cancel</button>
                </div>
            </form>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" [style.width.%]="progressPercent"></div>
            </div>
            <div class="progress-info">
                <span><strong>{{ goal.current_value }}</strong> / {{ goal.goal_value }} {{ goal.goal_unit }}</span>
                <span class="percent">{{ progressPercent }}%</span>
            </div>
        </div>

        <form (ngSubmit)="updateProgress()" class="progress-update">
            <label for="current_value">Update value:</label>
            <div class="save-bar">
                <input type="number" name="current_value" step="any" [(ngModel)]="currentValue">
                <button name="update_progress" type="submit">Save</button>
            </div>
        </form>

        <div class="goal-settings">
            <div class="public-toggle">
                <label>
                    <input type="checkbox" [checked]="goal.public" (change)="togglePublic()">
                    Show on profile (public)
                </label>
            </div>
        </div>
    </section>

    <section class="microgoals-section" *ngIf="goal">
        <h3>Microgoals</h3>
        <ul class="microgoals-list">
            <li class="microgoal-item" *ngFor="let microgoal of microgoals">
                <div class="microgoal-form">
                    <button class="micro-btn" type="button" (click)="toggleMicrogoal(microgoal)">
                        <span class="checkmark" [class.checked]="microgoal.completed">
                            {{ microgoal.completed ? '✔' : '☐' }}
                        </span>
                    </button>
                    <span class="microgoal-title">{{ microgoal.title }}</span>
                </div>
                <div class="delete-form">
                    <button class="delete-btn" type="button" (click)="deleteMicrogoal(microgoal)">✖</button>
                </div>
            </li>
        </ul>
        <form (ngSubmit)="addMicrogoal()" class="microgoal-add">
            <input name="microgoal_title" type="text" placeholder="New microgoal..." [(ngModel)]="newMicrogoalTitle">
            <button name="add_microgoal" type="submit">➕ Add</button>
        </form>
    </section>

    <section class="journal-section" *ngIf="journalEntries.length > 0">
        <h3>📝 Goal Journal:</h3>
        <div class="journal-entry" *ngFor="let entry of journalEntries" (click)="openJournalEntry(entry)">
            <div class="journal-entry-content">
                <div class="journal-entry-header">
                    <div class="milestone">🎯 {{ entry.milestone_percentage }}%</div>
                </div>
                <p class="journal-preview">{{ getJournalPreview(entry.content) }}</p>
            </div>
            <div class="journal-arrow">></div>
        </div>
    </section>

    <div class="journal-modal fancy" *ngIf="showJournalModal && nextMilestone">
        <p>🎉 <strong>Congrats!</strong> You've reached <span class="milestone-highlight">{{ nextMilestone }}%</span> of your goal.</p>

        <form (ngSubmit)="addJournalEntry()" class="inline-journal-form">
            <!-- Rich Text Toolbar -->
            <div class="toolbar">
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('bold')" title="Bold">
                        <strong>B</strong>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('italic')" title="Italic">
                        <em>I</em>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('underline')" title="Underline">
                        <u>U</u>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="formatText('strikeThrough')" title="Strikethrough">
                        <s>S</s>
                    </button>
                </div>

                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" (click)="formatText('formatBlock', 'h3')" title="Heading">
                        <strong>H</strong>
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertList(false)" title="Bullet List">
                        •
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertList(true)" title="Numbered List">
                        1.
                    </button>
                    <button type="button" class="toolbar-btn" (click)="insertQuote()" title="Quote">
                        <ion-icon name="chatbox-outline"></ion-icon>
                    </button>
                </div>
            </div>

            <!-- Rich Text Editor -->
            <div
                class="rich-editor-small"
                contenteditable="true"
                (input)="onJournalContentInput($event)"
                (blur)="onJournalContentBlur($event)"
                #journalEditor
                placeholder="Write how you're progressing...">
            </div>

            <button type="submit" name="add_journal">✍️ Add journal entry</button>
        </form>
    </div>

    <!-- Remove Goal Button -->
    <div class="delete-goal-container">
        <button type="button" class="delete-goal-btn" (click)="confirmDeleteGoal()">Remove Goal</button>
    </div>
</div>

<!-- Navigation -->
<app-navigation></app-navigation>
