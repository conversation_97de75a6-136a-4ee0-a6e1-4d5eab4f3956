{"ast": null, "code": "var _GoalDetailPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = () => [\"/goals\"];\nfunction GoalDetailPage_section_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 11)(1, \"div\", 12)(2, \"div\", 13)(3, \"h2\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showEditForm = true);\n    });\n    i0.ɵɵtext(6, \"Edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"form\", 17);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateGoalInfo());\n    });\n    i0.ɵɵelementStart(11, \"div\", 18)(12, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedEmoji, $event) || (ctx_r1.editedEmoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedName, $event) || (ctx_r1.editedName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"textarea\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_textarea_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedDescription, $event) || (ctx_r1.editedDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 22)(16, \"button\", 23);\n    i0.ɵɵtext(17, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelEdit());\n    });\n    i0.ɵɵtext(19, \"Cancel\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 25)(21, \"div\", 26);\n    i0.ɵɵelement(22, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 28)(24, \"span\")(25, \"strong\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 29);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"form\", 30);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateProgress());\n    });\n    i0.ɵɵelementStart(31, \"label\", 31);\n    i0.ɵɵtext(32, \"Update value:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"input\", 33);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.currentValue, $event) || (ctx_r1.currentValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 34);\n    i0.ɵɵtext(36, \"Save\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 35)(38, \"div\", 36)(39, \"label\")(40, \"input\", 37);\n    i0.ɵɵlistener(\"change\", function GoalDetailPage_section_11_Template_input_change_40_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePublic());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Show on profile (public) \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showEditForm ? \"none\" : \"block\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.goal.emoji, \" \", ctx_r1.goal.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.description);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showEditForm ? \"block\" : \"none\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedEmoji);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedName);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedDescription);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.current_value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" / \", ctx_r1.goal.goal_value, \" \", ctx_r1.goal.goal_unit, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.currentValue);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"checked\", ctx_r1.goal.public);\n  }\n}\nfunction GoalDetailPage_section_12_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 44)(1, \"div\", 45)(2, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_2_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵelementStart(3, \"span\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 49)(8, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_8_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵtext(9, \"\\u2716\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const microgoal_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"checked\", microgoal_r5.completed);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", microgoal_r5.completed ? \"\\u2714\" : \"\\u2610\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(microgoal_r5.title);\n  }\n}\nfunction GoalDetailPage_section_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 38)(1, \"h3\");\n    i0.ɵɵtext(2, \"Microgoals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 39);\n    i0.ɵɵtemplate(4, GoalDetailPage_section_12_li_4_Template, 10, 4, \"li\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 41);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_12_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addMicrogoal());\n    });\n    i0.ɵɵelementStart(6, \"input\", 42);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_12_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newMicrogoalTitle, $event) || (ctx_r1.newMicrogoalTitle = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 43);\n    i0.ɵɵtext(8, \"\\u2795 Add\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.microgoals);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newMicrogoalTitle);\n  }\n}\nfunction GoalDetailPage_section_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"div\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const entry_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\uD83C\\uDFAF \", entry_r6.milestone_percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(entry_r6.content);\n  }\n}\nfunction GoalDetailPage_section_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 51)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCDD Goal Journal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GoalDetailPage_section_13_div_3_Template, 5, 2, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.journalEntries);\n  }\n}\nfunction GoalDetailPage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83C\\uDF89 \");\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Congrats!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" You've reached \");\n    i0.ɵɵelementStart(6, \"span\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" of your goal.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"form\", 57);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_div_14_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addJournalEntry());\n    });\n    i0.ɵɵelementStart(10, \"textarea\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_div_14_Template_textarea_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.journalContent, $event) || (ctx_r1.journalContent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 59);\n    i0.ɵɵtext(12, \"\\u270D\\uFE0F Add journal entry\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.nextMilestone, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.journalContent);\n  }\n}\nexport class GoalDetailPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // Goal data\n    this.goalId = null;\n    this.goal = null;\n    this.microgoals = [];\n    this.journalEntries = [];\n    // UI state\n    this.showEditForm = false;\n    this.editedDescription = '';\n    this.editedName = '';\n    this.editedEmoji = '';\n    this.currentValue = 0;\n    this.progressPercent = 0;\n    this.showJournalModal = false;\n    this.nextMilestone = null;\n    this.journalContent = '';\n    // New microgoal\n    this.newMicrogoalTitle = '';\n    this.supabaseService = inject(SupabaseService);\n    this.goalService = inject(GoalService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n  }\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(take(1)).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n  loadGoal() {\n    if (!this.goalId) return;\n    this.goalService.getGoal(this.goalId).pipe(take(1)).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.editedName = goal.name;\n        this.editedEmoji = goal.emoji;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n  loadMicrogoals() {\n    if (!this.goalId) return;\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n  loadJournalEntries() {\n    if (!this.goalId) return;\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n  calculateProgress() {\n    if (!this.goal) return;\n    this.progressPercent = this.goal.goal_value > 0 ? Math.min(100, Math.round(this.goal.current_value / this.goal.goal_value * 100)) : 0;\n  }\n  checkForMilestone() {\n    if (!this.goal) return;\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone => currentPercent >= milestone && !existingMilestones.includes(milestone));\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n  updateGoalInfo() {\n    if (!this.goalId || !this.goal) return;\n    // Validate emoji (keep only the last valid emoji)\n    const emojiRegex = /[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/gu;\n    const emojis = this.editedEmoji.match(emojiRegex);\n    const finalEmoji = emojis ? emojis[emojis.length - 1] : '🎯';\n    const trimmedName = this.editedName.trim();\n    if (!trimmedName) {\n      return; // Don't save if name is empty\n    }\n    this.goalService.updateGoal(this.goalId, {\n      name: trimmedName,\n      emoji: finalEmoji,\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.name = trimmedName;\n        this.goal.emoji = finalEmoji;\n        this.goal.description = this.editedDescription;\n      }\n      this.showEditForm = false;\n    }).catch(error => {\n      console.error('Error updating goal info:', error);\n    });\n  }\n  cancelEdit() {\n    var _this$goal, _this$goal2, _this$goal3;\n    this.showEditForm = false;\n    this.editedName = ((_this$goal = this.goal) === null || _this$goal === void 0 ? void 0 : _this$goal.name) || '';\n    this.editedEmoji = ((_this$goal2 = this.goal) === null || _this$goal2 === void 0 ? void 0 : _this$goal2.emoji) || '';\n    this.editedDescription = ((_this$goal3 = this.goal) === null || _this$goal3 === void 0 ? void 0 : _this$goal3.description) || '';\n  }\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n  toggleMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n  deleteMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n    const newMicrogoal = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n    const newEntry = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n    this.goalService.createJournalEntry(newEntry).then(id => {\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n    const newPublicValue = !this.goal.public;\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n}\n_GoalDetailPage = GoalDetailPage;\n_GoalDetailPage.ɵfac = function GoalDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GoalDetailPage)();\n};\n_GoalDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GoalDetailPage,\n  selectors: [[\"app-goal-detail\"]],\n  decls: 19,\n  vars: 7,\n  consts: [[1, \"container\"], [1, \"logo-wrap\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"back-link\", 3, \"routerLink\"], [\"class\", \"goal-card\", 4, \"ngIf\"], [\"class\", \"microgoals-section\", 4, \"ngIf\"], [\"class\", \"journal-section\", 4, \"ngIf\"], [\"class\", \"journal-modal fancy\", 4, \"ngIf\"], [1, \"delete-goal-container\"], [\"type\", \"button\", 1, \"delete-goal-btn\", 3, \"click\"], [1, \"goal-card\"], [1, \"goal-info-container\"], [1, \"goal-header\"], [1, \"edit-bio-btn\", 3, \"click\"], [1, \"goal-desc\"], [1, \"edit-form\"], [3, \"ngSubmit\"], [1, \"title-inputs\"], [\"type\", \"text\", \"name\", \"emoji\", \"maxlength\", \"2\", \"placeholder\", \"\\uD83C\\uDFAF\", 1, \"emoji-input\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"name\", \"maxlength\", \"100\", \"placeholder\", \"Goal name\", 1, \"name-input\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"description\", \"maxlength\", \"500\", \"placeholder\", \"Enter goal description\", 1, \"description-textarea\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-buttons\"], [\"type\", \"submit\", 1, \"save-btn\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-info\"], [1, \"percent\"], [1, \"progress-update\", 3, \"ngSubmit\"], [\"for\", \"current_value\"], [1, \"save-bar\"], [\"type\", \"number\", \"name\", \"current_value\", \"step\", \"any\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"update_progress\", \"type\", \"submit\"], [1, \"goal-settings\"], [1, \"public-toggle\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"microgoals-section\"], [1, \"microgoals-list\"], [\"class\", \"microgoal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"microgoal-add\", 3, \"ngSubmit\"], [\"name\", \"microgoal_title\", \"type\", \"text\", \"placeholder\", \"New microgoal...\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"add_microgoal\", \"type\", \"submit\"], [1, \"microgoal-item\"], [1, \"microgoal-form\"], [\"type\", \"button\", 1, \"micro-btn\", 3, \"click\"], [1, \"checkmark\"], [1, \"microgoal-title\"], [1, \"delete-form\"], [\"type\", \"button\", 1, \"delete-btn\", 3, \"click\"], [1, \"journal-section\"], [\"class\", \"journal-entry\", 4, \"ngFor\", \"ngForOf\"], [1, \"journal-entry\"], [1, \"milestone\"], [1, \"journal-modal\", \"fancy\"], [1, \"milestone-highlight\"], [1, \"inline-journal-form\", 3, \"ngSubmit\"], [\"name\", \"journal_content\", \"rows\", \"4\", \"placeholder\", \"Write how you're progressing...\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"submit\", \"name\", \"add_journal\"]],\n  template: function GoalDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"div\", 1)(3, \"div\", 2);\n      i0.ɵɵelement(4, \"img\", 3);\n      i0.ɵɵelementStart(5, \"span\");\n      i0.ɵɵtext(6, \"Upshift\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(7, \"h1\");\n      i0.ɵɵtext(8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"a\", 4);\n      i0.ɵɵtext(10, \"\\u2190 Back to goals\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, GoalDetailPage_section_11_Template, 42, 18, \"section\", 5)(12, GoalDetailPage_section_12_Template, 9, 2, \"section\", 6)(13, GoalDetailPage_section_13_Template, 4, 1, \"section\", 7)(14, GoalDetailPage_div_14_Template, 13, 2, \"div\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"div\", 9)(16, \"button\", 10);\n      i0.ɵɵlistener(\"click\", function GoalDetailPage_Template_button_click_16_listener() {\n        return ctx.confirmDeleteGoal();\n      });\n      i0.ɵɵtext(17, \"Remove Goal\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelement(18, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵtextInterpolate(ctx.goal == null ? null : ctx.goal.name);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c0));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.journalEntries.length > 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showJournalModal && ctx.nextMilestone);\n    }\n  },\n  dependencies: [IonicModule, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent],\n  styles: [\"[_nghost-%COMP%] {\\n  --bg: #0c0c0f;\\n  --card: #121217;\\n  --pill: #1c1c1e;\\n  --text: #fff;\\n  --text-muted: #8e8e93;\\n  --accent: #4d7bff;\\n  --radius: 16px;\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\nh1[_ngcontent-%COMP%], .page-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nconti[_ngcontent-%COMP%]   body.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  padding: 20px;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n  margin-top: 10px;\\n}\\n\\n.description-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-desc[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 10px;\\n  flex: 1;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--accent);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 2px 5px;\\n  margin-left: 10px;\\n}\\n\\n.description-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.description-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n}\\n\\n.save-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-right: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #2c2c2e;\\n  color: white;\\n  border: none;\\n}\\n\\n.delete-goal-container[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  text-align: center;\\n}\\n\\n.delete-goal-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 59, 48, 0.2);\\n  color: #FF3B30;\\n  border: 1px solid #FF3B30;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n}\\n\\n.progress-update[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-top: 10px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  font-weight: 600;\\n  border: none;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  cursor: pointer;\\n  margin-top: 10px;\\n}\\n\\n.microgoals-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.microgoals-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding-left: 0;\\n  margin-bottom: 12px;\\n  margin-top: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 6px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 8px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.journal-section[_ngcontent-%COMP%] {\\n  background: var(--pill);\\n  padding: 16px;\\n  border-radius: var(--radius);\\n}\\n\\n.journal-entry[_ngcontent-%COMP%] {\\n  border-top: 1px solid #2c2c2e;\\n  padding-top: 10px;\\n  margin-top: 10px;\\n}\\n\\n.journal-entry[_ngcontent-%COMP%]:first-child {\\n  border-top: none;\\n  padding-top: 0;\\n  margin-top: 0;\\n}\\n\\n.milestone[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--accent);\\n}\\n\\n.journal-modal[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: var(--card);\\n  padding: 14px;\\n  border-radius: var(--radius);\\n  text-align: center;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-top: 10px;\\n  color: var(--accent);\\n  text-decoration: underline;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n  padding: 20px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.top-date[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-muted);\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.goal-percent[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--accent);\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 12px;\\n}\\n\\n.goal-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--accent);\\n  text-decoration: none;\\n  display: inline-block;\\n}\\n\\n.goal-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.no-goals[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-muted);\\n  margin-top: 40px;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #ff4d4d;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  font-size: 16px;\\n  vertical-align: middle;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  color: red;\\n}\\n\\n.microgoal-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: var(--pill);\\n  padding: 10px 14px;\\n  margin-bottom: 8px;\\n  border-radius: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  flex: 1;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 18px;\\n  color: var(--accent);\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text-muted);\\n}\\n\\n.checkmark.checked[_ngcontent-%COMP%] {\\n  color: #4cd964;\\n}\\n\\n.microgoal-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: var(--text);\\n}\\n\\n.delete-form[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  color: #ff4d6d;\\n  cursor: pointer;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border-radius: 16px;\\n  padding: 20px;\\n  margin-top: 24px;\\n  text-align: center;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin-bottom: 12px;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  text-decoration: underline;\\n  color: var(--accent);\\n}\\n\\n.milestone-highlight[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 600;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 10px;\\n  padding: 10px;\\n  color: var(--text);\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background-color: var(--accent);\\n  border: none;\\n  padding: 10px 16px;\\n  color: white;\\n  font-weight: bold;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  transition: opacity 0.2s ease;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n\\n.save-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 0 16px 16px 0;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-radius: 16px 0 0 16px;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: inherit;\\n  display: block;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%]:hover   .goal-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 1px var(--accent-color);\\n  transition: box-shadow 0.2s;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.logo-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n}\\n\\n.back-link[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  text-decoration: none;\\n  margin-left: 2px;\\n  transition: color 0.2s ease;\\n  margin-bottom: 10px;\\n}\\n\\n.back-link[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n\\n.goal-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 1;\\n}\\n\\n.goal-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  width: 95%;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n\\n\\n\\n.goal-settings[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px solid var(--quest-border);\\n}\\n\\n.public-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.public-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: var(--text);\\n  cursor: pointer;\\n}\\n\\n.public-toggle[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  accent-color: var(--accent);\\n  cursor: pointer;\\n}\\n\\n\\n\\n.goal-info-container[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.edit-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.title-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.emoji-input[_ngcontent-%COMP%] {\\n  width: 60px;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  text-align: center;\\n  font-size: 16px;\\n}\\n\\n.name-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n}\\n\\n.description-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "ActivatedRoute", "Router", "GoalService", "take", "NavigationComponent", "SupabaseService", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GoalDetailPage_section_11_Template_button_click_5_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showEditForm", "GoalDetailPage_section_11_Template_form_ngSubmit_10_listener", "updateGoalInfo", "ɵɵtwoWayListener", "GoalDetailPage_section_11_Template_input_ngModelChange_12_listener", "$event", "ɵɵtwoWayBindingSet", "edited<PERSON><PERSON><PERSON>", "GoalDetailPage_section_11_Template_input_ngModelChange_13_listener", "editedName", "GoalDetailPage_section_11_Template_textarea_ngModelChange_14_listener", "editedDescription", "GoalDetailPage_section_11_Template_button_click_18_listener", "cancelEdit", "ɵɵelement", "GoalDetailPage_section_11_Template_form_ngSubmit_30_listener", "updateProgress", "GoalDetailPage_section_11_Template_input_ngModelChange_34_listener", "currentValue", "GoalDetailPage_section_11_Template_input_change_40_listener", "togglePublic", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtextInterpolate2", "goal", "emoji", "name", "ɵɵtextInterpolate", "description", "ɵɵtwoWayProperty", "progressPercent", "current_value", "goal_value", "goal_unit", "ɵɵtextInterpolate1", "ɵɵproperty", "public", "GoalDetailPage_section_12_li_4_Template_button_click_2_listener", "microgoal_r5", "_r4", "$implicit", "toggleMicrogoal", "GoalDetailPage_section_12_li_4_Template_button_click_8_listener", "deleteMicrogoal", "ɵɵclassProp", "completed", "title", "ɵɵtemplate", "GoalDetailPage_section_12_li_4_Template", "GoalDetailPage_section_12_Template_form_ngSubmit_5_listener", "_r3", "addMicrogoal", "GoalDetailPage_section_12_Template_input_ngModelChange_6_listener", "newMicrogoalTitle", "microgoals", "entry_r6", "milestone_percentage", "content", "GoalDetailPage_section_13_div_3_Template", "journalEntries", "GoalDetailPage_div_14_Template_form_ngSubmit_9_listener", "_r7", "addJournalEntry", "GoalDetailPage_div_14_Template_textarea_ngModelChange_10_listener", "journalContent", "nextMilestone", "GoalDetailPage", "constructor", "userId", "goalId", "showJournalModal", "supabaseService", "goalService", "route", "router", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "id", "paramMap", "params", "get", "loadGoal", "loadMicrogoals", "loadJournalEntries", "getGoal", "calculateProgress", "checkForMilestone", "console", "error", "getMicroGoals", "getJournalEntries", "entries", "Math", "min", "round", "currentPercent", "milestones", "existingMilestones", "map", "entry", "reachedMilestones", "filter", "milestone", "includes", "length", "emojiRegex", "emojis", "match", "<PERSON><PERSON><PERSON><PERSON>", "trimmedName", "trim", "updateGoal", "then", "catch", "_this$goal", "_this$goal2", "_this$goal3", "microgoal", "toggleMicroGoalCompletion", "completed_at", "Date", "undefined", "deleteMicroGoal", "m", "newMicrogoal", "goal_id", "createMicroGoal", "push", "newEntry", "createJournalEntry", "newPublicValue", "confirmDeleteGoal", "confirm", "deleteGoal", "navigate", "selectors", "decls", "vars", "consts", "template", "GoalDetailPage_Template", "rf", "ctx", "GoalDetailPage_section_11_Template", "GoalDetailPage_section_12_Template", "GoalDetailPage_section_13_Template", "GoalDetailPage_div_14_Template", "GoalDetailPage_Template_button_click_16_listener", "ɵɵpureFunction0", "_c0", "i1", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.html"], "sourcesContent": ["import { Component, OnInit, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal, GoalJournalEntry, MicroGoal } from '../../../models/goal.model';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\n\n@Component({\n  selector: 'app-goal-detail',\n  templateUrl: './goal-detail.page.html',\n  styleUrls: ['./goal-detail.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent]\n})\nexport class GoalDetailPage implements OnInit {\n  // User data\n  userId: string | null = null;\n\n  // Goal data\n  goalId: string | null = null;\n  goal: Goal | null = null;\n  microgoals: MicroGoal[] = [];\n  journalEntries: GoalJournalEntry[] = [];\n\n  // UI state\n  showEditForm = false;\n  editedDescription = '';\n  editedName = '';\n  editedEmoji = '';\n  currentValue = 0;\n  progressPercent = 0;\n  showJournalModal = false;\n  nextMilestone: number | null = null;\n  journalContent = '';\n\n  // New microgoal\n  newMicrogoalTitle = '';\n\n  private supabaseService = inject(SupabaseService);\n  private goalService = inject(GoalService);\n  private route = inject(ActivatedRoute);\n  private router = inject(Router);\n\n  constructor() {}\n\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(\n          take(1)\n        ).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n\n  loadGoal() {\n    if (!this.goalId) return;\n\n    this.goalService.getGoal(this.goalId).pipe(\n      take(1)\n    ).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.editedName = goal.name;\n        this.editedEmoji = goal.emoji;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n\n  loadMicrogoals() {\n    if (!this.goalId) return;\n\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n\n  loadJournalEntries() {\n    if (!this.goalId) return;\n\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n\n  calculateProgress() {\n    if (!this.goal) return;\n\n    this.progressPercent = this.goal.goal_value > 0\n      ? Math.min(100, Math.round((this.goal.current_value / this.goal.goal_value) * 100))\n      : 0;\n  }\n\n  checkForMilestone() {\n    if (!this.goal) return;\n\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone =>\n      currentPercent >= milestone &&\n      !existingMilestones.includes(milestone)\n    );\n\n\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n\n  updateGoalInfo() {\n    if (!this.goalId || !this.goal) return;\n\n    // Validate emoji (keep only the last valid emoji)\n    const emojiRegex = /[\\u{1F600}-\\u{1F64F}]|[\\u{1F300}-\\u{1F5FF}]|[\\u{1F680}-\\u{1F6FF}]|[\\u{1F1E0}-\\u{1F1FF}]|[\\u{2600}-\\u{26FF}]|[\\u{2700}-\\u{27BF}]/gu;\n    const emojis = this.editedEmoji.match(emojiRegex);\n    const finalEmoji = emojis ? emojis[emojis.length - 1] : '🎯';\n\n    const trimmedName = this.editedName.trim();\n    if (!trimmedName) {\n      return; // Don't save if name is empty\n    }\n\n    this.goalService.updateGoal(this.goalId, {\n      name: trimmedName,\n      emoji: finalEmoji,\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.name = trimmedName;\n        this.goal.emoji = finalEmoji;\n        this.goal.description = this.editedDescription;\n      }\n      this.showEditForm = false;\n    }).catch(error => {\n      console.error('Error updating goal info:', error);\n    });\n  }\n\n  cancelEdit() {\n    this.showEditForm = false;\n    this.editedName = this.goal?.name || '';\n    this.editedEmoji = this.goal?.emoji || '';\n    this.editedDescription = this.goal?.description || '';\n  }\n\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n\n\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n\n  toggleMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n\n  deleteMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n\n    const newMicrogoal: Omit<MicroGoal, 'id'> = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n\n    const newEntry: Omit<GoalJournalEntry, 'id' | 'created_at'> = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n\n    this.goalService.createJournalEntry(newEntry).then(id => {\n\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n\n    const newPublicValue = !this.goal.public;\n\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n\n\n}\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo-wrap\">\r\n            <div class=\"logo\">\r\n                <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n                <span>Upshift</span>\r\n            </div>\r\n        </div>\r\n        <h1>{{ goal?.name }}</h1>\r\n    </header>\r\n    <a [routerLink]=\"['/goals']\" class=\"back-link\">&larr; Back to goals</a>\r\n\r\n    <section class=\"goal-card\" *ngIf=\"goal\">\r\n        <!-- Goal Info Section -->\r\n        <div class=\"goal-info-container\" [style.display]=\"showEditForm ? 'none' : 'block'\">\r\n            <div class=\"goal-header\">\r\n                <h2>{{goal.emoji}} {{ goal.name }}</h2>\r\n                <button class=\"edit-bio-btn\" (click)=\"showEditForm = true\">Edit</button>\r\n            </div>\r\n            <p class=\"goal-desc\">{{ goal.description }}</p>\r\n        </div>\r\n\r\n        <!-- Edit Form -->\r\n        <div class=\"edit-form\" [style.display]=\"showEditForm ? 'block' : 'none'\">\r\n            <form (ngSubmit)=\"updateGoalInfo()\">\r\n                <div class=\"title-inputs\">\r\n                    <input type=\"text\" name=\"emoji\" maxlength=\"2\" placeholder=\"🎯\" [(ngModel)]=\"editedEmoji\" class=\"emoji-input\">\r\n                    <input type=\"text\" name=\"name\" maxlength=\"100\" placeholder=\"Goal name\" [(ngModel)]=\"editedName\" class=\"name-input\">\r\n                </div>\r\n                <textarea name=\"description\" maxlength=\"500\" placeholder=\"Enter goal description\" [(ngModel)]=\"editedDescription\" class=\"description-textarea\"></textarea>\r\n                <div class=\"form-buttons\">\r\n                    <button type=\"submit\" class=\"save-btn\">Save</button>\r\n                    <button type=\"button\" class=\"cancel-btn\" (click)=\"cancelEdit()\">Cancel</button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n\r\n        <div class=\"progress-container\">\r\n            <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" [style.width.%]=\"progressPercent\"></div>\r\n            </div>\r\n            <div class=\"progress-info\">\r\n                <span><strong>{{ goal.current_value }}</strong> / {{ goal.goal_value }} {{ goal.goal_unit }}</span>\r\n                <span class=\"percent\">{{ progressPercent }}%</span>\r\n            </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"updateProgress()\" class=\"progress-update\">\r\n            <label for=\"current_value\">Update value:</label>\r\n            <div class=\"save-bar\">\r\n                <input type=\"number\" name=\"current_value\" step=\"any\" [(ngModel)]=\"currentValue\">\r\n                <button name=\"update_progress\" type=\"submit\">Save</button>\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"goal-settings\">\r\n            <div class=\"public-toggle\">\r\n                <label>\r\n                    <input type=\"checkbox\" [checked]=\"goal.public\" (change)=\"togglePublic()\">\r\n                    Show on profile (public)\r\n                </label>\r\n            </div>\r\n        </div>\r\n    </section>\r\n\r\n    <section class=\"microgoals-section\" *ngIf=\"goal\">\r\n        <h3>Microgoals</h3>\r\n        <ul class=\"microgoals-list\">\r\n            <li class=\"microgoal-item\" *ngFor=\"let microgoal of microgoals\">\r\n                <div class=\"microgoal-form\">\r\n                    <button class=\"micro-btn\" type=\"button\" (click)=\"toggleMicrogoal(microgoal)\">\r\n                        <span class=\"checkmark\" [class.checked]=\"microgoal.completed\">\r\n                            {{ microgoal.completed ? '✔' : '☐' }}\r\n                        </span>\r\n                    </button>\r\n                    <span class=\"microgoal-title\">{{ microgoal.title }}</span>\r\n                </div>\r\n                <div class=\"delete-form\">\r\n                    <button class=\"delete-btn\" type=\"button\" (click)=\"deleteMicrogoal(microgoal)\">✖</button>\r\n                </div>\r\n            </li>\r\n        </ul>\r\n        <form (ngSubmit)=\"addMicrogoal()\" class=\"microgoal-add\">\r\n            <input name=\"microgoal_title\" type=\"text\" placeholder=\"New microgoal...\" [(ngModel)]=\"newMicrogoalTitle\">\r\n            <button name=\"add_microgoal\" type=\"submit\">➕ Add</button>\r\n        </form>\r\n    </section>\r\n\r\n    <section class=\"journal-section\" *ngIf=\"journalEntries.length > 0\">\r\n        <h3>📝 Goal Journal:</h3>\r\n        <div class=\"journal-entry\" *ngFor=\"let entry of journalEntries\">\r\n            <div class=\"milestone\">🎯 {{ entry.milestone_percentage }}%</div>\r\n            <p>{{ entry.content }}</p>\r\n        </div>\r\n    </section>\r\n\r\n    <div class=\"journal-modal fancy\" *ngIf=\"showJournalModal && nextMilestone\">\r\n        <p>🎉 <strong>Congrats!</strong> You've reached <span class=\"milestone-highlight\">{{ nextMilestone }}%</span> of your goal.</p>\r\n\r\n        <form (ngSubmit)=\"addJournalEntry()\" class=\"inline-journal-form\">\r\n            <textarea name=\"journal_content\" rows=\"4\" placeholder=\"Write how you're progressing...\" [(ngModel)]=\"journalContent\"></textarea>\r\n            <button type=\"submit\" name=\"add_journal\">✍️ Add journal entry</button>\r\n        </form>\r\n    </div>\r\n\r\n</div>\r\n\r\n<!-- Remove Goal Button -->\r\n<div class=\"delete-goal-container\">\r\n    <button type=\"button\" class=\"delete-goal-btn\" (click)=\"confirmDeleteGoal()\">Remove Goal</button>\r\n</div>\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";AAAA,SAA4BA,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACtE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,eAAe,QAAQ,oCAAoC;;;;;;;;;;ICQpDC,EAJZ,CAAAC,cAAA,kBAAwC,cAE+C,cACtD,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,iBAA2D;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,2DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,YAAA,GAAwB,IAAI;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,WAAI;IACnEF,EADmE,CAAAG,YAAA,EAAS,EACtE;IACNH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC7C;IAIFH,EADJ,CAAAC,cAAA,cAAyE,gBACjC;IAA9BD,EAAA,CAAAI,UAAA,sBAAAQ,6DAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC;IAE3Bb,EADJ,CAAAC,cAAA,eAA0B,iBACuF;IAA9CD,EAAA,CAAAc,gBAAA,2BAAAC,mEAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAU,WAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,WAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAAyB;IAAxFhB,EAAA,CAAAG,YAAA,EAA6G;IAC7GH,EAAA,CAAAC,cAAA,iBAAmH;IAA5CD,EAAA,CAAAc,gBAAA,2BAAAK,mEAAAH,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAY,UAAA,EAAAJ,MAAA,MAAAR,MAAA,CAAAY,UAAA,GAAAJ,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAAwB;IACnGhB,EADI,CAAAG,YAAA,EAAmH,EACjH;IACNH,EAAA,CAAAC,cAAA,oBAA+I;IAA7DD,EAAA,CAAAc,gBAAA,2BAAAO,sEAAAL,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAc,iBAAA,EAAAN,MAAA,MAAAR,MAAA,CAAAc,iBAAA,GAAAN,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAA8BhB,EAAA,CAAAG,YAAA,EAAW;IAEtJH,EADJ,CAAAC,cAAA,eAA0B,kBACiB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAgE;IAAvBD,EAAA,CAAAI,UAAA,mBAAAmB,4DAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,UAAA,EAAY;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,cAAM;IAGlFF,EAHkF,CAAAG,YAAA,EAAS,EAC7E,EACH,EACL;IAGFH,EADJ,CAAAC,cAAA,eAAgC,eACF;IACtBD,EAAA,CAAAyB,SAAA,eAAmE;IACvEzB,EAAA,CAAAG,YAAA,EAAM;IAEIH,EADV,CAAAC,cAAA,eAA2B,YACjB,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnGH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAEpDF,EAFoD,CAAAG,YAAA,EAAO,EACjD,EACJ;IAENH,EAAA,CAAAC,cAAA,gBAA4D;IAAtDD,EAAA,CAAAI,UAAA,sBAAAsB,6DAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAmB,cAAA,EAAgB;IAAA,EAAC;IAC/B3B,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE5CH,EADJ,CAAAC,cAAA,eAAsB,iBAC8D;IAA3BD,EAAA,CAAAc,gBAAA,2BAAAc,mEAAAZ,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAqB,YAAA,EAAAb,MAAA,MAAAR,MAAA,CAAAqB,YAAA,GAAAb,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA0B;IAA/EhB,EAAA,CAAAG,YAAA,EAAgF;IAChFH,EAAA,CAAAC,cAAA,kBAA6C;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAEzDF,EAFyD,CAAAG,YAAA,EAAS,EACxD,EACH;IAKKH,EAHZ,CAAAC,cAAA,eAA2B,eACI,aAChB,iBACsE;IAA1BD,EAAA,CAAAI,UAAA,oBAAA0B,4DAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAuB,YAAA,EAAc;IAAA,EAAC;IAAxE/B,EAAA,CAAAG,YAAA,EAAyE;IACzEH,EAAA,CAAAE,MAAA,kCACJ;IAGZF,EAHY,CAAAG,YAAA,EAAQ,EACN,EACJ,EACA;;;;IAjD2BH,EAAA,CAAAgC,SAAA,EAAiD;IAAjDhC,EAAA,CAAAiC,WAAA,YAAAzB,MAAA,CAAAG,YAAA,oBAAiD;IAEtEX,EAAA,CAAAgC,SAAA,GAA8B;IAA9BhC,EAAA,CAAAkC,kBAAA,KAAA1B,MAAA,CAAA2B,IAAA,CAAAC,KAAA,OAAA5B,MAAA,CAAA2B,IAAA,CAAAE,IAAA,KAA8B;IAGjBrC,EAAA,CAAAgC,SAAA,GAAsB;IAAtBhC,EAAA,CAAAsC,iBAAA,CAAA9B,MAAA,CAAA2B,IAAA,CAAAI,WAAA,CAAsB;IAIxBvC,EAAA,CAAAgC,SAAA,EAAiD;IAAjDhC,EAAA,CAAAiC,WAAA,YAAAzB,MAAA,CAAAG,YAAA,oBAAiD;IAGGX,EAAA,CAAAgC,SAAA,GAAyB;IAAzBhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAU,WAAA,CAAyB;IACjBlB,EAAA,CAAAgC,SAAA,EAAwB;IAAxBhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAY,UAAA,CAAwB;IAEjBpB,EAAA,CAAAgC,SAAA,EAA+B;IAA/BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAc,iBAAA,CAA+B;IAUtFtB,EAAA,CAAAgC,SAAA,GAAiC;IAAjChC,EAAA,CAAAiC,WAAA,UAAAzB,MAAA,CAAAiC,eAAA,MAAiC;IAG9CzC,EAAA,CAAAgC,SAAA,GAAwB;IAAxBhC,EAAA,CAAAsC,iBAAA,CAAA9B,MAAA,CAAA2B,IAAA,CAAAO,aAAA,CAAwB;IAAU1C,EAAA,CAAAgC,SAAA,EAA4C;IAA5ChC,EAAA,CAAAkC,kBAAA,QAAA1B,MAAA,CAAA2B,IAAA,CAAAQ,UAAA,OAAAnC,MAAA,CAAA2B,IAAA,CAAAS,SAAA,KAA4C;IACtE5C,EAAA,CAAAgC,SAAA,GAAsB;IAAtBhC,EAAA,CAAA6C,kBAAA,KAAArC,MAAA,CAAAiC,eAAA,MAAsB;IAOSzC,EAAA,CAAAgC,SAAA,GAA0B;IAA1BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAqB,YAAA,CAA0B;IAQpD7B,EAAA,CAAAgC,SAAA,GAAuB;IAAvBhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAA2B,IAAA,CAAAY,MAAA,CAAuB;;;;;;IAY9C/C,EAFR,CAAAC,cAAA,aAAgE,cAChC,iBACqD;IAArCD,EAAA,CAAAI,UAAA,mBAAA4C,gEAAA;MAAA,MAAAC,YAAA,GAAAjD,EAAA,CAAAM,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4C,eAAA,CAAAH,YAAA,CAA0B;IAAA,EAAC;IACxEjD,EAAA,CAAAC,cAAA,eAA8D;IAC1DD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACF;IACTH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;IAEFH,EADJ,CAAAC,cAAA,cAAyB,iBACyD;IAArCD,EAAA,CAAAI,UAAA,mBAAAiD,gEAAA;MAAA,MAAAJ,YAAA,GAAAjD,EAAA,CAAAM,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8C,eAAA,CAAAL,YAAA,CAA0B;IAAA,EAAC;IAACjD,EAAA,CAAAE,MAAA,aAAC;IAEvFF,EAFuF,CAAAG,YAAA,EAAS,EACtF,EACL;;;;IAT+BH,EAAA,CAAAgC,SAAA,GAAqC;IAArChC,EAAA,CAAAuD,WAAA,YAAAN,YAAA,CAAAO,SAAA,CAAqC;IACzDxD,EAAA,CAAAgC,SAAA,EACJ;IADIhC,EAAA,CAAA6C,kBAAA,MAAAI,YAAA,CAAAO,SAAA,4BACJ;IAE0BxD,EAAA,CAAAgC,SAAA,GAAqB;IAArBhC,EAAA,CAAAsC,iBAAA,CAAAW,YAAA,CAAAQ,KAAA,CAAqB;;;;;;IAT/DzD,EADJ,CAAAC,cAAA,kBAAiD,SACzC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,aAA4B;IACxBD,EAAA,CAAA0D,UAAA,IAAAC,uCAAA,kBAAgE;IAapE3D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAwD;IAAlDD,EAAA,CAAAI,UAAA,sBAAAwD,4DAAA;MAAA5D,EAAA,CAAAM,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAsD,YAAA,EAAc;IAAA,EAAC;IAC7B9D,EAAA,CAAAC,cAAA,gBAAyG;IAAhCD,EAAA,CAAAc,gBAAA,2BAAAiD,kEAAA/C,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAwD,iBAAA,EAAAhD,MAAA,MAAAR,MAAA,CAAAwD,iBAAA,GAAAhD,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAAxGhB,EAAA,CAAAG,YAAA,EAAyG;IACzGH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAExDF,EAFwD,CAAAG,YAAA,EAAS,EACtD,EACD;;;;IAlB+CH,EAAA,CAAAgC,SAAA,GAAa;IAAbhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAAyD,UAAA,CAAa;IAeWjE,EAAA,CAAAgC,SAAA,GAA+B;IAA/BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAwD,iBAAA,CAA+B;;;;;IAQxGhE,EADJ,CAAAC,cAAA,cAAgE,cACrC;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC1BF,EAD0B,CAAAG,YAAA,EAAI,EACxB;;;;IAFqBH,EAAA,CAAAgC,SAAA,GAAoC;IAApChC,EAAA,CAAA6C,kBAAA,kBAAAqB,QAAA,CAAAC,oBAAA,MAAoC;IACxDnE,EAAA,CAAAgC,SAAA,GAAmB;IAAnBhC,EAAA,CAAAsC,iBAAA,CAAA4B,QAAA,CAAAE,OAAA,CAAmB;;;;;IAH1BpE,EADJ,CAAAC,cAAA,kBAAmE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,iCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAA0D,UAAA,IAAAW,wCAAA,kBAAgE;IAIpErE,EAAA,CAAAG,YAAA,EAAU;;;;IAJuCH,EAAA,CAAAgC,SAAA,GAAiB;IAAjBhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAA8D,cAAA,CAAiB;;;;;;IAO9DtE,EADJ,CAAAC,cAAA,cAA2E,QACpE;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/HH,EAAA,CAAAC,cAAA,eAAiE;IAA3DD,EAAA,CAAAI,UAAA,sBAAAmE,wDAAA;MAAAvE,EAAA,CAAAM,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAiE,eAAA,EAAiB;IAAA,EAAC;IAChCzE,EAAA,CAAAC,cAAA,oBAAqH;IAA7BD,EAAA,CAAAc,gBAAA,2BAAA4D,kEAAA1D,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAkE,GAAA;MAAA,MAAAhE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAmE,cAAA,EAAA3D,MAAA,MAAAR,MAAA,CAAAmE,cAAA,GAAA3D,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA4B;IAAChB,EAAA,CAAAG,YAAA,EAAW;IAChIH,EAAA,CAAAC,cAAA,kBAAyC;IAAAD,EAAA,CAAAE,MAAA,sCAAoB;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EACnE,EACL;;;;IANgFH,EAAA,CAAAgC,SAAA,GAAoB;IAApBhC,EAAA,CAAA6C,kBAAA,KAAArC,MAAA,CAAAoE,aAAA,MAAoB;IAGV5E,EAAA,CAAAgC,SAAA,GAA4B;IAA5BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAmE,cAAA,CAA4B;;;ADnFhI,OAAM,MAAOE,cAAc;EA6BzBC,YAAA;IA5BA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAA7C,IAAI,GAAgB,IAAI;IACxB,KAAA8B,UAAU,GAAgB,EAAE;IAC5B,KAAAK,cAAc,GAAuB,EAAE;IAEvC;IACA,KAAA3D,YAAY,GAAG,KAAK;IACpB,KAAAW,iBAAiB,GAAG,EAAE;IACtB,KAAAF,UAAU,GAAG,EAAE;IACf,KAAAF,WAAW,GAAG,EAAE;IAChB,KAAAW,YAAY,GAAG,CAAC;IAChB,KAAAY,eAAe,GAAG,CAAC;IACnB,KAAAwC,gBAAgB,GAAG,KAAK;IACxB,KAAAL,aAAa,GAAkB,IAAI;IACnC,KAAAD,cAAc,GAAG,EAAE;IAEnB;IACA,KAAAX,iBAAiB,GAAG,EAAE;IAEd,KAAAkB,eAAe,GAAG7F,MAAM,CAACU,eAAe,CAAC;IACzC,KAAAoF,WAAW,GAAG9F,MAAM,CAACO,WAAW,CAAC;IACjC,KAAAwF,KAAK,GAAG/F,MAAM,CAACK,cAAc,CAAC;IAC9B,KAAA2F,MAAM,GAAGhG,MAAM,CAACM,MAAM,CAAC;EAEhB;EAEf2F,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,eAAe,CAACK,YAAY,CAACC,IAAI,CACpC3F,IAAI,CAAC,CAAC,CAAC,CACR,CAAC4F,SAAS,CAACC,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACX,MAAM,GAAGW,IAAI,CAACC,EAAE;QAErB;QACA,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACJ,IAAI,CACtB3F,IAAI,CAAC,CAAC,CAAC,CACR,CAAC4F,SAAS,CAACI,MAAM,IAAG;UACnB,IAAI,CAACb,MAAM,GAAGa,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;UAC9B,IAAI,IAAI,CAACd,MAAM,EAAE;YACf,IAAI,CAACe,QAAQ,EAAE;YACf,IAAI,CAACC,cAAc,EAAE;YACrB,IAAI,CAACC,kBAAkB,EAAE;UAC3B;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAF,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACf,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACe,OAAO,CAAC,IAAI,CAAClB,MAAM,CAAC,CAACQ,IAAI,CACxC3F,IAAI,CAAC,CAAC,CAAC,CACR,CAAC4F,SAAS,CAACtD,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACb,iBAAiB,GAAGa,IAAI,CAACI,WAAW;QACzC,IAAI,CAACnB,UAAU,GAAGe,IAAI,CAACE,IAAI;QAC3B,IAAI,CAACnB,WAAW,GAAGiB,IAAI,CAACC,KAAK;QAC7B,IAAI,CAACP,YAAY,GAAGM,IAAI,CAACO,aAAa;QACtC,IAAI,CAACyD,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAACtB,MAAM,CAAC;MACvD;IACF,CAAC,CAAC;EACJ;EAEAgB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACoB,aAAa,CAAC,IAAI,CAACvB,MAAM,CAAC,CAACS,SAAS,CAACxB,UAAU,IAAG;MACjE,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC9B,CAAC,CAAC;EACJ;EAEAgC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;IAElB,IAAI,CAACG,WAAW,CAACqB,iBAAiB,CAAC,IAAI,CAACxB,MAAM,CAAC,CAACS,SAAS,CAACgB,OAAO,IAAG;MAClE,IAAI,CAACnC,cAAc,GAAGmC,OAAO;MAC7B;MACA,IAAI,CAACL,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAChE,IAAI,EAAE;IAEhB,IAAI,CAACM,eAAe,GAAG,IAAI,CAACN,IAAI,CAACQ,UAAU,GAAG,CAAC,GAC3C+D,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAE,IAAI,CAACzE,IAAI,CAACO,aAAa,GAAG,IAAI,CAACP,IAAI,CAACQ,UAAU,GAAI,GAAG,CAAC,CAAC,GACjF,CAAC;EACP;EAEAyD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjE,IAAI,EAAE;IAEhB;IACA,MAAM0E,cAAc,GAAG,IAAI,CAACpE,eAAe;IAE3C;IACA,MAAMqE,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAExC;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACzC,cAAc,CAAC0C,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAC9C,oBAAoB,CAAC;IAEvF;IACA,MAAM+C,iBAAiB,GAAGJ,UAAU,CAACK,MAAM,CAACC,SAAS,IACnDP,cAAc,IAAIO,SAAS,IAC3B,CAACL,kBAAkB,CAACM,QAAQ,CAACD,SAAS,CAAC,CACxC;IAGD;IACA,IAAIF,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC1C,aAAa,GAAGsC,iBAAiB,CAAC,CAAC,CAAC;MACzC,IAAI,CAACjC,gBAAgB,GAAG,IAAI;IAC9B,CAAC,MAAM;MACL,IAAI,CAACL,aAAa,GAAG,IAAI;MACzB,IAAI,CAACK,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEApE,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACmE,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAEhC;IACA,MAAMoF,UAAU,GAAG,mIAAmI;IACtJ,MAAMC,MAAM,GAAG,IAAI,CAACtG,WAAW,CAACuG,KAAK,CAACF,UAAU,CAAC;IACjD,MAAMG,UAAU,GAAGF,MAAM,GAAGA,MAAM,CAACA,MAAM,CAACF,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;IAE5D,MAAMK,WAAW,GAAG,IAAI,CAACvG,UAAU,CAACwG,IAAI,EAAE;IAC1C,IAAI,CAACD,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;IAEA,IAAI,CAACxC,WAAW,CAAC0C,UAAU,CAAC,IAAI,CAAC7C,MAAM,EAAE;MACvC3C,IAAI,EAAEsF,WAAW;MACjBvF,KAAK,EAAEsF,UAAU;MACjBnF,WAAW,EAAE,IAAI,CAACjB;KACnB,CAAC,CAACwG,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAC3F,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACE,IAAI,GAAGsF,WAAW;QAC5B,IAAI,CAACxF,IAAI,CAACC,KAAK,GAAGsF,UAAU;QAC5B,IAAI,CAACvF,IAAI,CAACI,WAAW,GAAG,IAAI,CAACjB,iBAAiB;MAChD;MACA,IAAI,CAACX,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC,CAACoH,KAAK,CAACzB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CAAC;EACJ;EAEA9E,UAAUA,CAAA;IAAA,IAAAwG,UAAA,EAAAC,WAAA,EAAAC,WAAA;IACR,IAAI,CAACvH,YAAY,GAAG,KAAK;IACzB,IAAI,CAACS,UAAU,GAAG,EAAA4G,UAAA,OAAI,CAAC7F,IAAI,cAAA6F,UAAA,uBAATA,UAAA,CAAW3F,IAAI,KAAI,EAAE;IACvC,IAAI,CAACnB,WAAW,GAAG,EAAA+G,WAAA,OAAI,CAAC9F,IAAI,cAAA8F,WAAA,uBAATA,WAAA,CAAW7F,KAAK,KAAI,EAAE;IACzC,IAAI,CAACd,iBAAiB,GAAG,EAAA4G,WAAA,OAAI,CAAC/F,IAAI,cAAA+F,WAAA,uBAATA,WAAA,CAAW3F,WAAW,KAAI,EAAE;EACvD;EAEAZ,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACqD,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAGhC,IAAI,CAACgD,WAAW,CAAC0C,UAAU,CAAC,IAAI,CAAC7C,MAAM,EAAE;MACvCtC,aAAa,EAAE,IAAI,CAACb;KACrB,CAAC,CAACiG,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAC3F,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACO,aAAa,GAAG,IAAI,CAACb,YAAY;QAC3C,IAAI,CAACsE,iBAAiB,EAAE;QAExB;QACA,IAAI,CAACF,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC,CAAC8B,KAAK,CAACzB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAlD,eAAeA,CAAC+E,SAAoB;IAClC,IAAI,CAACA,SAAS,CAACxC,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAACiD,yBAAyB,CAACD,SAAS,CAACxC,EAAE,CAAC,CAACmC,IAAI,CAAC,MAAK;MACjE;MACAK,SAAS,CAAC3E,SAAS,GAAG,CAAC2E,SAAS,CAAC3E,SAAS;MAC1C2E,SAAS,CAACE,YAAY,GAAGF,SAAS,CAAC3E,SAAS,GAAG,IAAI8E,IAAI,EAAE,GAAGC,SAAS;IACvE,CAAC,CAAC;EACJ;EAEAjF,eAAeA,CAAC6E,SAAoB;IAClC,IAAI,CAACA,SAAS,CAACxC,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAACqD,eAAe,CAACL,SAAS,CAACxC,EAAE,CAAC,CAACmC,IAAI,CAAC,MAAK;MACvD;MACA,IAAI,CAAC7D,UAAU,GAAG,IAAI,CAACA,UAAU,CAACkD,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKwC,SAAS,CAACxC,EAAE,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA7B,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACkB,MAAM,IAAI,CAAC,IAAI,CAAChB,iBAAiB,CAAC4D,IAAI,EAAE,EAAE;IAEpD,MAAMc,YAAY,GAA0B;MAC1CC,OAAO,EAAE,IAAI,CAAC3D,MAAM;MACpBvB,KAAK,EAAE,IAAI,CAACO,iBAAiB,CAAC4D,IAAI,EAAE;MACpCpE,SAAS,EAAE;KACZ;IAED,IAAI,CAAC2B,WAAW,CAACyD,eAAe,CAACF,YAAY,CAAC,CAACZ,IAAI,CAACnC,EAAE,IAAG;MACvD;MACA,IAAI,CAAC1B,UAAU,CAAC4E,IAAI,CAAC;QACnB,GAAGH,YAAY;QACf/C;OACD,CAAC;MAEF;MACA,IAAI,CAAC3B,iBAAiB,GAAG,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAS,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACO,MAAM,IAAI,CAAC,IAAI,CAACJ,aAAa,IAAI,CAAC,IAAI,CAACD,cAAc,CAACiD,IAAI,EAAE,EAAE;IAExE,MAAMkB,QAAQ,GAAgD;MAC5DH,OAAO,EAAE,IAAI,CAAC3D,MAAM;MACpBb,oBAAoB,EAAE,IAAI,CAACS,aAAa;MACxCR,OAAO,EAAE,IAAI,CAACO,cAAc,CAACiD,IAAI;KAClC;IAED,IAAI,CAACzC,WAAW,CAAC4D,kBAAkB,CAACD,QAAQ,CAAC,CAAChB,IAAI,CAACnC,EAAE,IAAG;MAEtD;MACA,IAAI,CAACV,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACN,cAAc,GAAG,EAAE;MAExB;MACA,IAAI,CAACsB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,CAAC8B,KAAK,CAACzB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,CAAC;EACJ;EAEAvE,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACiD,MAAM,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE;IAEhC,MAAM6G,cAAc,GAAG,CAAC,IAAI,CAAC7G,IAAI,CAACY,MAAM;IAExC,IAAI,CAACoC,WAAW,CAAC0C,UAAU,CAAC,IAAI,CAAC7C,MAAM,EAAE;MACvCjC,MAAM,EAAEiG;KACT,CAAC,CAAClB,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAC3F,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACY,MAAM,GAAGiG,cAAc;MACnC;IACF,CAAC,CAAC,CAACjB,KAAK,CAACzB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D,CAAC,CAAC;EACJ;EAEA2C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjE,MAAM,EAAE;IAElB,IAAIkE,OAAO,CAAC,0EAA0E,CAAC,EAAE;MACvF,IAAI,CAAC/D,WAAW,CAACgE,UAAU,CAAC,IAAI,CAACnE,MAAM,CAAC,CAAC8C,IAAI,CAAC,MAAK;QACjD,IAAI,CAACzC,MAAM,CAAC+D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC,CAAC,CAACrB,KAAK,CAACzB,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;;kBAhRWzB,cAAc;;mCAAdA,eAAc;AAAA;;QAAdA,eAAc;EAAAwE,SAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCdf3J,EAHZ,CAAAC,cAAA,aAAuB,aACX,aACmB,aACD;MACdD,EAAA,CAAAyB,SAAA,aAA6D;MAC7DzB,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MAErBF,EAFqB,CAAAG,YAAA,EAAO,EAClB,EACJ;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,GAAgB;MACxBF,EADwB,CAAAG,YAAA,EAAK,EACpB;MACTH,EAAA,CAAAC,cAAA,WAA+C;MAAAD,EAAA,CAAAE,MAAA,4BAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAI;MAsFvEH,EApFA,CAAA0D,UAAA,KAAAmG,kCAAA,uBAAwC,KAAAC,kCAAA,qBAqDS,KAAAC,kCAAA,qBAuBkB,KAAAC,8BAAA,kBAQQ;MAS/EhK,EAAA,CAAAG,YAAA,EAAM;MAIFH,EADJ,CAAAC,cAAA,cAAmC,kBAC6C;MAA9BD,EAAA,CAAAI,UAAA,mBAAA6J,iDAAA;QAAA,OAASL,GAAA,CAAAX,iBAAA,EAAmB;MAAA,EAAC;MAACjJ,EAAA,CAAAE,MAAA,mBAAW;MAC3FF,EAD2F,CAAAG,YAAA,EAAS,EAC9F;MAGNH,EAAA,CAAAyB,SAAA,sBAAiC;;;MAzGrBzB,EAAA,CAAAgC,SAAA,GAAgB;MAAhBhC,EAAA,CAAAsC,iBAAA,CAAAsH,GAAA,CAAAzH,IAAA,kBAAAyH,GAAA,CAAAzH,IAAA,CAAAE,IAAA,CAAgB;MAErBrC,EAAA,CAAAgC,SAAA,EAAyB;MAAzBhC,EAAA,CAAA8C,UAAA,eAAA9C,EAAA,CAAAkK,eAAA,IAAAC,GAAA,EAAyB;MAEAnK,EAAA,CAAAgC,SAAA,GAAU;MAAVhC,EAAA,CAAA8C,UAAA,SAAA8G,GAAA,CAAAzH,IAAA,CAAU;MAqDDnC,EAAA,CAAAgC,SAAA,EAAU;MAAVhC,EAAA,CAAA8C,UAAA,SAAA8G,GAAA,CAAAzH,IAAA,CAAU;MAuBbnC,EAAA,CAAAgC,SAAA,EAA+B;MAA/BhC,EAAA,CAAA8C,UAAA,SAAA8G,GAAA,CAAAtF,cAAA,CAAAgD,MAAA,KAA+B;MAQ/BtH,EAAA,CAAAgC,SAAA,EAAuC;MAAvChC,EAAA,CAAA8C,UAAA,SAAA8G,GAAA,CAAA3E,gBAAA,IAAA2E,GAAA,CAAAhF,aAAA,CAAuC;;;iBDjFjEpF,WAAW,EAAA4K,EAAA,CAAAC,0BAAA,EAAE/K,YAAY,EAAAgL,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjL,WAAW,EAAAkL,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,mBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAExL,YAAY,EAAAyL,EAAA,CAAAC,UAAA,EAAErL,mBAAmB;EAAAsL,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}