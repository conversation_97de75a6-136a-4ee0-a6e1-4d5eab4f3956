{"ast": null, "code": "var _GoalDetailPage;\nimport { inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = [\"journalEditor\"];\nconst _c1 = () => [\"/goals\"];\nfunction GoalDetailPage_section_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 12)(1, \"div\", 13)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"p\", 15);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showEditForm = true);\n    });\n    i0.ɵɵtext(8, \"Edit\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 17)(10, \"form\", 18);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateGoalInfo());\n    });\n    i0.ɵɵelementStart(11, \"div\", 19)(12, \"input\", 20);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedEmoji, $event) || (ctx_r1.editedEmoji = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 21);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedName, $event) || (ctx_r1.editedName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"textarea\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_textarea_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.editedDescription, $event) || (ctx_r1.editedDescription = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"button\", 24);\n    i0.ɵɵtext(17, \"Save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_11_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelEdit());\n    });\n    i0.ɵɵtext(19, \"Cancel\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 26)(21, \"div\", 27);\n    i0.ɵɵelement(22, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 29)(24, \"span\")(25, \"strong\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 30);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"form\", 31);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_11_Template_form_ngSubmit_30_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateProgress());\n    });\n    i0.ɵɵelementStart(31, \"label\", 32);\n    i0.ɵɵtext(32, \"Update value:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 33)(34, \"input\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_11_Template_input_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.currentValue, $event) || (ctx_r1.currentValue = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"button\", 35);\n    i0.ɵɵtext(36, \"Save\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 36)(38, \"div\", 37)(39, \"label\")(40, \"input\", 38);\n    i0.ɵɵlistener(\"change\", function GoalDetailPage_section_11_Template_input_change_40_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePublic());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Show on profile (public) \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showEditForm ? \"none\" : \"block\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.goal.emoji, \" \", ctx_r1.goal.name, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r1.showEditForm ? \"block\" : \"none\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedEmoji);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedName);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.editedDescription);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.goal.current_value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" / \", ctx_r1.goal.goal_value, \" \", ctx_r1.goal.goal_unit, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.progressPercent, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.currentValue);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"checked\", ctx_r1.goal.public);\n  }\n}\nfunction GoalDetailPage_section_12_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 45)(1, \"div\", 46)(2, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_2_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵelementStart(3, \"span\", 48);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 50)(8, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_12_li_4_Template_button_click_8_listener() {\n      const microgoal_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteMicrogoal(microgoal_r5));\n    });\n    i0.ɵɵtext(9, \"\\u2716\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const microgoal_r5 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"checked\", microgoal_r5.completed);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", microgoal_r5.completed ? \"\\u2714\" : \"\\u2610\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(microgoal_r5.title);\n  }\n}\nfunction GoalDetailPage_section_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"section\", 39)(1, \"h3\");\n    i0.ɵɵtext(2, \"Microgoals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 40);\n    i0.ɵɵtemplate(4, GoalDetailPage_section_12_li_4_Template, 10, 4, \"li\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"form\", 42);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_section_12_Template_form_ngSubmit_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addMicrogoal());\n    });\n    i0.ɵɵelementStart(6, \"input\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function GoalDetailPage_section_12_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.newMicrogoalTitle, $event) || (ctx_r1.newMicrogoalTitle = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 44);\n    i0.ɵɵtext(8, \"\\u2795 Add\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.microgoals);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.newMicrogoalTitle);\n  }\n}\nfunction GoalDetailPage_section_13_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_section_13_div_3_Template_div_click_0_listener() {\n      const entry_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openJournalEntry(entry_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p\", 58);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 59);\n    i0.ɵɵtext(8, \">\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const entry_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\uD83C\\uDFAF \", entry_r7.milestone_percentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getJournalPreview(entry_r7.content));\n  }\n}\nfunction GoalDetailPage_section_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"section\", 52)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCDD Goal Journal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GoalDetailPage_section_13_div_3_Template, 9, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.journalEntries);\n  }\n}\nfunction GoalDetailPage_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"p\");\n    i0.ɵɵtext(2, \"\\uD83C\\uDF89 \");\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Congrats!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" You've reached \");\n    i0.ɵɵelementStart(6, \"span\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" of your goal.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"form\", 62);\n    i0.ɵɵlistener(\"ngSubmit\", function GoalDetailPage_div_14_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addJournalEntry());\n    });\n    i0.ɵɵelementStart(10, \"div\", 63)(11, \"div\", 64)(12, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.formatText(\"bold\"));\n    });\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14, \"B\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.formatText(\"italic\"));\n    });\n    i0.ɵɵelementStart(16, \"em\");\n    i0.ɵɵtext(17, \"I\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.formatText(\"underline\"));\n    });\n    i0.ɵɵelementStart(19, \"u\");\n    i0.ɵɵtext(20, \"U\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.formatText(\"strikeThrough\"));\n    });\n    i0.ɵɵelementStart(22, \"s\");\n    i0.ɵɵtext(23, \"S\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 64)(25, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.formatText(\"formatBlock\", \"h3\"));\n    });\n    i0.ɵɵelementStart(26, \"strong\");\n    i0.ɵɵtext(27, \"H\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.insertList(false));\n    });\n    i0.ɵɵtext(29, \" \\u2022 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.insertList(true));\n    });\n    i0.ɵɵtext(31, \" 1. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function GoalDetailPage_div_14_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.insertQuote());\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 74, 0);\n    i0.ɵɵlistener(\"input\", function GoalDetailPage_div_14_Template_div_input_34_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onJournalContentInput($event));\n    })(\"blur\", function GoalDetailPage_div_14_Template_div_blur_34_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onJournalContentBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"button\", 75);\n    i0.ɵɵtext(37, \"\\u270D\\uFE0F Add journal entry\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.nextMilestone, \"%\");\n  }\n}\nexport class GoalDetailPage {\n  constructor() {\n    // User data\n    this.userId = null;\n    // Goal data\n    this.goalId = null;\n    this.goal = null;\n    this.microgoals = [];\n    this.journalEntries = [];\n    // UI state\n    this.showEditForm = false;\n    this.editedDescription = '';\n    this.editedName = '';\n    this.editedEmoji = '';\n    this.currentValue = 0;\n    this.progressPercent = 0;\n    this.showJournalModal = false;\n    this.nextMilestone = null;\n    this.journalContent = '';\n    // New microgoal\n    this.newMicrogoalTitle = '';\n    this.supabaseService = inject(SupabaseService);\n    this.goalService = inject(GoalService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n  }\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(take(1)).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(take(1)).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n  ngAfterViewInit() {\n    // Initialize journal editor content after view is ready\n    if (this.showJournalModal && this.journalEditor) {\n      this.journalEditor.nativeElement.innerHTML = this.journalContent;\n    }\n  }\n  loadGoal() {\n    if (!this.goalId) return;\n    this.goalService.getGoal(this.goalId).pipe(take(1)).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.editedName = goal.name;\n        this.editedEmoji = goal.emoji;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n  loadMicrogoals() {\n    if (!this.goalId) return;\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n  loadJournalEntries() {\n    if (!this.goalId) return;\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n  calculateProgress() {\n    if (!this.goal) return;\n    this.progressPercent = this.goal.goal_value > 0 ? Math.min(100, Math.round(this.goal.current_value / this.goal.goal_value * 100)) : 0;\n  }\n  checkForMilestone() {\n    if (!this.goal) return;\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone => currentPercent >= milestone && !existingMilestones.includes(milestone));\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.journalEditor) {\n          this.journalEditor.nativeElement.innerHTML = this.journalContent;\n          this.setupCheckboxListeners();\n        }\n      }, 0);\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n  updateGoalInfo() {\n    if (!this.goalId || !this.goal) return;\n    const trimmedName = this.editedName.trim();\n    if (!trimmedName) {\n      return; // Don't save if name is empty\n    }\n    // Use emoji as is (EmojiInputDirective already handles validation)\n    const finalEmoji = this.editedEmoji || '🎯';\n    this.goalService.updateGoal(this.goalId, {\n      name: trimmedName,\n      emoji: finalEmoji,\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.name = trimmedName;\n        this.goal.emoji = finalEmoji;\n        this.goal.description = this.editedDescription;\n      }\n      this.showEditForm = false;\n    }).catch(error => {\n      console.error('Error updating goal info:', error);\n    });\n  }\n  cancelEdit() {\n    var _this$goal, _this$goal2, _this$goal3;\n    this.showEditForm = false;\n    this.editedName = ((_this$goal = this.goal) === null || _this$goal === void 0 ? void 0 : _this$goal.name) || '';\n    this.editedEmoji = ((_this$goal2 = this.goal) === null || _this$goal2 === void 0 ? void 0 : _this$goal2.emoji) || '';\n    this.editedDescription = ((_this$goal3 = this.goal) === null || _this$goal3 === void 0 ? void 0 : _this$goal3.description) || '';\n  }\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n  toggleMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n  deleteMicrogoal(microgoal) {\n    if (!microgoal.id) return;\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n    const newMicrogoal = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n    const newEntry = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n    this.goalService.createJournalEntry(newEntry).then(id => {\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n  getJournalPreview(content) {\n    // Strip HTML tags and get first 100 characters\n    const textContent = content.replace(/<[^>]*>/g, '');\n    return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;\n  }\n  openJournalEntry(entry) {\n    // Navigate to journal entry detail page\n    this.router.navigate(['/goals', this.goalId, 'journal', entry.id]);\n  }\n  // Rich text formatting methods\n  formatText(command, value) {\n    document.execCommand(command, false, value);\n  }\n  insertList(ordered = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n  onJournalContentInput(event) {\n    this.journalContent = event.target.innerHTML;\n    this.setupCheckboxListeners();\n  }\n  onJournalContentBlur(event) {\n    this.journalContent = event.target.innerHTML;\n  }\n  setupCheckboxListeners() {\n    if (this.journalEditor) {\n      const checkboxes = this.journalEditor.nativeElement.querySelectorAll('.checklist-checkbox');\n      checkboxes.forEach(checkbox => {\n        checkbox.removeEventListener('change', this.handleCheckboxChange.bind(this));\n        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));\n      });\n    }\n  }\n  handleCheckboxChange() {\n    if (this.journalEditor) {\n      this.journalContent = this.journalEditor.nativeElement.innerHTML;\n    }\n  }\n  insertQuote() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const quote = document.createElement('blockquote');\n      quote.className = 'notion-quote';\n      quote.innerHTML = 'Quote text here...';\n      range.deleteContents();\n      range.insertNode(quote);\n      // Select the quote content\n      const newRange = document.createRange();\n      newRange.selectNodeContents(quote);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n    }\n  }\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n      range.deleteContents();\n      range.insertNode(checklistItem);\n      // Add event listener to the new checkbox\n      const checkbox = checklistItem.querySelector('.checklist-checkbox');\n      if (checkbox) {\n        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));\n      }\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n      // Update content\n      this.journalContent = this.journalEditor.nativeElement.innerHTML;\n    }\n  }\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n    const newPublicValue = !this.goal.public;\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n}\n_GoalDetailPage = GoalDetailPage;\n_GoalDetailPage.ɵfac = function GoalDetailPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _GoalDetailPage)();\n};\n_GoalDetailPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _GoalDetailPage,\n  selectors: [[\"app-goal-detail\"]],\n  viewQuery: function GoalDetailPage_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.journalEditor = _t.first);\n    }\n  },\n  decls: 19,\n  vars: 7,\n  consts: [[\"journalEditor\", \"\"], [1, \"container\"], [1, \"logo-wrap\"], [1, \"logo\"], [\"src\", \"assets/images/upshift_icon_mini.svg\", \"alt\", \"Upshift\"], [1, \"back-link\", 3, \"routerLink\"], [\"class\", \"goal-card\", 4, \"ngIf\"], [\"class\", \"microgoals-section\", 4, \"ngIf\"], [\"class\", \"journal-section\", 4, \"ngIf\"], [\"class\", \"journal-modal fancy\", 4, \"ngIf\"], [1, \"delete-goal-container\"], [\"type\", \"button\", 1, \"delete-goal-btn\", 3, \"click\"], [1, \"goal-card\"], [1, \"goal-info-container\"], [1, \"description-container\"], [1, \"goal-desc\"], [1, \"edit-bio-btn\", 3, \"click\"], [1, \"edit-form\"], [3, \"ngSubmit\"], [1, \"title-inputs\"], [\"type\", \"text\", \"name\", \"emoji\", \"placeholder\", \"\\uD83C\\uDFAF\", \"appEmojiInput\", \"\", 1, \"emoji-input\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"text\", \"name\", \"name\", \"maxlength\", \"100\", \"placeholder\", \"Goal name\", 1, \"name-input\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"description\", \"maxlength\", \"500\", \"placeholder\", \"Enter goal description\", 1, \"description-textarea\", 3, \"ngModelChange\", \"ngModel\"], [1, \"form-buttons\"], [\"type\", \"submit\", 1, \"save-btn\"], [\"type\", \"button\", 1, \"cancel-btn\", 3, \"click\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-info\"], [1, \"percent\"], [1, \"progress-update\", 3, \"ngSubmit\"], [\"for\", \"current_value\"], [1, \"save-bar\"], [\"type\", \"number\", \"name\", \"current_value\", \"step\", \"any\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"update_progress\", \"type\", \"submit\"], [1, \"goal-settings\"], [1, \"public-toggle\"], [\"type\", \"checkbox\", 3, \"change\", \"checked\"], [1, \"microgoals-section\"], [1, \"microgoals-list\"], [\"class\", \"microgoal-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"microgoal-add\", 3, \"ngSubmit\"], [\"name\", \"microgoal_title\", \"type\", \"text\", \"placeholder\", \"New microgoal...\", 3, \"ngModelChange\", \"ngModel\"], [\"name\", \"add_microgoal\", \"type\", \"submit\"], [1, \"microgoal-item\"], [1, \"microgoal-form\"], [\"type\", \"button\", 1, \"micro-btn\", 3, \"click\"], [1, \"checkmark\"], [1, \"microgoal-title\"], [1, \"delete-form\"], [\"type\", \"button\", 1, \"delete-btn\", 3, \"click\"], [1, \"journal-section\"], [\"class\", \"journal-entry\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"journal-entry\", 3, \"click\"], [1, \"journal-entry-content\"], [1, \"journal-entry-header\"], [1, \"milestone\"], [1, \"journal-preview\"], [1, \"journal-arrow\"], [1, \"journal-modal\", \"fancy\"], [1, \"milestone-highlight\"], [1, \"inline-journal-form\", 3, \"ngSubmit\"], [1, \"toolbar\"], [1, \"toolbar-group\"], [\"type\", \"button\", \"title\", \"Bold\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Italic\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Underline\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Strikethrough\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Heading\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Bullet List\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Numbered List\", 1, \"toolbar-btn\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Quote\", 1, \"toolbar-btn\", 3, \"click\"], [\"name\", \"chatbox-outline\"], [\"contenteditable\", \"true\", \"placeholder\", \"Write how you're progressing...\", 1, \"rich-editor-small\", 3, \"input\", \"blur\"], [\"type\", \"submit\", \"name\", \"add_journal\"]],\n  template: function GoalDetailPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 1)(1, \"header\")(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵelement(4, \"img\", 4);\n      i0.ɵɵelementStart(5, \"span\");\n      i0.ɵɵtext(6, \"Upshift\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(7, \"h1\");\n      i0.ɵɵtext(8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"a\", 5);\n      i0.ɵɵtext(10, \"\\u2190 Back to goals\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, GoalDetailPage_section_11_Template, 42, 18, \"section\", 6)(12, GoalDetailPage_section_12_Template, 9, 2, \"section\", 7)(13, GoalDetailPage_section_13_Template, 4, 1, \"section\", 8)(14, GoalDetailPage_div_14_Template, 38, 1, \"div\", 9);\n      i0.ɵɵelementStart(15, \"div\", 10)(16, \"button\", 11);\n      i0.ɵɵlistener(\"click\", function GoalDetailPage_Template_button_click_16_listener() {\n        return ctx.confirmDeleteGoal();\n      });\n      i0.ɵɵtext(17, \"Remove Goal\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(18, \"app-navigation\");\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵtextInterpolate(ctx.goal == null ? null : ctx.goal.name);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(6, _c1));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.goal);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.journalEntries.length > 0);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"ngIf\", ctx.showJournalModal && ctx.nextMilestone);\n    }\n  },\n  dependencies: [IonicModule, i1.IonIcon, i1.RouterLinkWithHrefDelegate, CommonModule, i2.NgForOf, i2.NgIf, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.MaxLengthValidator, i3.NgModel, i3.NgForm, RouterModule, i4.RouterLink, NavigationComponent, EmojiInputDirective],\n  styles: [\"[_nghost-%COMP%] {\\n  --bg: #0c0c0f;\\n  --card: #121217;\\n  --pill: #1c1c1e;\\n  --text: #fff;\\n  --text-muted: #8e8e93;\\n  --accent: #4d7bff;\\n  --radius: 16px;\\n  --background-color: #0C0C0F;\\n  --text-color: #FFFFFF;\\n  --secondary-text: #8E8E93;\\n  --accent-color: #4169E1;\\n  --quest-bg: #1C1C1E;\\n  --quest-border: #2C2C2E;\\n}\\n\\nheader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\nh1[_ngcontent-%COMP%], .page-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n\\nconti[_ngcontent-%COMP%]   body.dark-theme[_ngcontent-%COMP%] {\\n  background-color: var(--background-color);\\n  color: var(--text-color);\\n  min-height: 100vh;\\n}\\n\\n*[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  box-sizing: border-box;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Oxygen, Ubuntu, Cantarell, \\\"Open Sans\\\", \\\"Helvetica Neue\\\", sans-serif;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 480px;\\n  padding: 20px;\\n  overflow-y: auto;\\n  scrollbar-width: none;\\n}\\n\\n.container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none; \\n\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n  margin-top: 10px;\\n}\\n\\n.description-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-desc[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 10px;\\n  flex: 1;\\n}\\n\\n.edit-bio-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: var(--accent);\\n  font-size: 12px;\\n  cursor: pointer;\\n  padding: 2px 5px;\\n  margin-left: 10px;\\n}\\n\\n.description-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.description-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n}\\n\\n.save-btn[_ngcontent-%COMP%], .cancel-btn[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n  margin-right: 10px;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n}\\n\\n.cancel-btn[_ngcontent-%COMP%] {\\n  background: #2c2c2e;\\n  color: white;\\n  border: none;\\n}\\n\\n.delete-goal-container[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  text-align: center;\\n  margin-bottom: 100px;\\n}\\n\\n.delete-goal-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 59, 48, 0.2);\\n  color: #FF3B30;\\n  border: 1px solid #FF3B30;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 6px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 14px;\\n}\\n\\n.progress-update[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-top: 10px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  font-weight: 600;\\n  border: none;\\n  padding: 10px 16px;\\n  border-radius: var(--radius);\\n  cursor: pointer;\\n  margin-top: 10px;\\n}\\n\\n.microgoals-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.microgoals-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding-left: 0;\\n  margin-bottom: 12px;\\n  margin-top: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 6px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 8px;\\n}\\n\\n.microgoal-add[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.journal-section[_ngcontent-%COMP%] {\\n  background: var(--pill);\\n  padding: 16px;\\n  border-radius: var(--radius);\\n}\\n\\n.journal-entry[_ngcontent-%COMP%] {\\n  border-top: 1px solid #2c2c2e;\\n  padding: 10px;\\n  margin-top: 10px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.journal-entry[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.05);\\n}\\n\\n.journal-entry[_ngcontent-%COMP%]:first-child {\\n  border-top: none;\\n  padding-top: 10px;\\n  margin-top: 0;\\n}\\n\\n.journal-entry-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.journal-entry-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 6px;\\n}\\n\\n.milestone[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--accent);\\n  margin-right: 8px;\\n}\\n\\n.journal-arrow[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 18px;\\n  font-weight: bold;\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n}\\n\\n.journal-preview[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 14px;\\n  line-height: 1.4;\\n  margin: 0;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  max-width: 50%;\\n}\\n\\n.journal-modal[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  background: var(--card);\\n  padding: 14px;\\n  border-radius: var(--radius);\\n  text-align: center;\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  padding: 8px;\\n  background: var(--background);\\n  border-radius: 6px;\\n  margin-bottom: 12px;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n  border-right: 1px solid #3a3a3c;\\n  padding-right: 12px;\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar-group[_ngcontent-%COMP%]:last-child {\\n  border-right: none;\\n  padding-right: 0;\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 1px solid #3a3a3c;\\n  border-radius: 4px;\\n  padding: 6px 10px;\\n  color: var(--text);\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 32px;\\n  height: 32px;\\n  font-size: 12px;\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--card);\\n}\\n.journal-modal[_ngcontent-%COMP%]   .toolbar-btn[_ngcontent-%COMP%]:active {\\n  background: #3a3a3c;\\n}\\n\\n.rich-editor-small[_ngcontent-%COMP%] {\\n  min-height: 120px;\\n  padding: 12px;\\n  border: 1px solid #3a3a3c;\\n  border-radius: 6px;\\n  background: var(--background);\\n  color: var(--text);\\n  font-size: 14px;\\n  line-height: 1.5;\\n  outline: none;\\n  margin-bottom: 12px;\\n  text-align: left;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent);\\n}\\n.rich-editor-small[contenteditable][_ngcontent-%COMP%]:empty::before {\\n  content: attr(placeholder);\\n  color: var(--text-muted);\\n  font-style: italic;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 8px 0 4px 0;\\n  font-weight: 600;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 6px 0;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .rich-editor-small[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 6px 0;\\n  padding-left: 20px;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 3px 0;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%] {\\n  border-left: 4px solid var(--accent);\\n  padding: 8px 12px;\\n  margin: 12px 0;\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 0 6px 6px 0;\\n  font-style: italic;\\n  color: var(--text-muted);\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .notion-quote[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  background: rgba(0, 122, 255, 0.15);\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin: 8px 0;\\n  padding: 4px 0;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   .checklist-checkbox[_ngcontent-%COMP%] {\\n  margin-top: 2px;\\n  width: 16px;\\n  height: 16px;\\n  accent-color: var(--accent);\\n  cursor: pointer;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  outline: none;\\n  color: var(--text);\\n  line-height: 1.4;\\n}\\n.rich-editor-small[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:focus {\\n  background: rgba(0, 122, 255, 0.1);\\n  border-radius: 4px;\\n  padding: 2px 4px;\\n  margin: -2px -4px;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  margin-top: 10px;\\n  color: var(--accent);\\n  text-decoration: underline;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 480px;\\n  padding: 20px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n\\n.top-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 22px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.top-date[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: var(--text-muted);\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n\\n.goal-card[_ngcontent-%COMP%] {\\n  background: var(--card);\\n  padding: 20px;\\n  border-radius: var(--radius);\\n  margin-bottom: 20px;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.goal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.goal-percent[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--accent);\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  height: 10px;\\n  background: #2c2c2e;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--accent);\\n  transition: width 0.3s ease-in-out;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  margin-bottom: 12px;\\n}\\n\\n.goal-link[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--accent);\\n  text-decoration: none;\\n  display: inline-block;\\n}\\n\\n.goal-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.no-goals[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: var(--text-muted);\\n  margin-top: 40px;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #ff4d4d;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  font-size: 16px;\\n  vertical-align: middle;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%]:hover {\\n  color: red;\\n}\\n\\n.microgoal-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: var(--pill);\\n  padding: 10px 14px;\\n  margin-bottom: 8px;\\n  border-radius: 12px;\\n}\\n\\n.microgoal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  flex: 1;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 18px;\\n  color: var(--accent);\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: var(--text-muted);\\n}\\n\\n.checkmark.checked[_ngcontent-%COMP%] {\\n  color: #4cd964;\\n}\\n\\n.microgoal-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  color: var(--text);\\n}\\n\\n.delete-form[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  font-size: 18px;\\n  color: #ff4d6d;\\n  cursor: pointer;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border-radius: 16px;\\n  padding: 20px;\\n  margin-top: 24px;\\n  text-align: center;\\n  box-shadow: 0 0 0 1px #1e1e1e;\\n}\\n\\n.journal-modal.fancy[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n  margin-bottom: 12px;\\n}\\n\\n.journal-link[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 14px;\\n  text-decoration: underline;\\n  color: var(--accent);\\n}\\n\\n.milestone-highlight[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 600;\\n}\\n\\n.micro-btn[_ngcontent-%COMP%], .delete-btn[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  background-color: var(--pill);\\n  border: 1px solid var(--quest-border);\\n  border-radius: 10px;\\n  padding: 10px;\\n  color: var(--text);\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  align-self: flex-start;\\n  background-color: var(--accent);\\n  border: none;\\n  padding: 10px 16px;\\n  color: white;\\n  font-weight: bold;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  transition: opacity 0.2s ease;\\n}\\n\\n.inline-journal-form[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  opacity: 0.9;\\n}\\n\\n.save-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 0 16px 16px 0;\\n}\\n\\n.save-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  border-radius: 16px 0 0 16px;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: inherit;\\n  display: block;\\n}\\n\\n.goal-card-link[_ngcontent-%COMP%]:hover   .goal-card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 0 1px var(--accent-color);\\n  transition: box-shadow 0.2s;\\n}\\n\\n.goal-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.logo-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  gap: 4px;\\n}\\n\\n.back-link[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: var(--text-muted);\\n  text-decoration: none;\\n  margin-left: 2px;\\n  transition: color 0.2s ease;\\n  margin-bottom: 10px;\\n}\\n\\n.back-link[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\n.goal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n\\n.goal-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.goal-emoji[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  line-height: 1;\\n}\\n\\n.goal-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.add-quest-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: var(--accent);\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  padding: 6px 12px;\\n  border-radius: 6px;\\n  background-color: rgba(77, 123, 255, 0.1);\\n  transition: background-color 0.2s;\\n}\\n\\n.add-quest-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(77, 123, 255, 0.2);\\n}\\n\\n.add-quest-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0, 0, 0, 0.9);\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  margin: 5% auto;\\n  padding: 20px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  color: white;\\n  max-height: 80vh;\\n  overflow-y: auto;\\n}\\n\\n.close-modal[_ngcontent-%COMP%] {\\n  color: #666;\\n  position: absolute;\\n  top: 5px;\\n  right: 10px;\\n  font-size: 20px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close-modal[_ngcontent-%COMP%]:hover {\\n  color: white;\\n}\\n\\n.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 18px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  color: #CCC;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  background-color: #1C1C1E;\\n  border: 1px solid #3C3C3E;\\n  border-radius: 8px;\\n  color: white;\\n  font-size: 14px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  height: 80px;\\n  width: 95%;\\n  resize: vertical;\\n}\\n\\n#emoji[_ngcontent-%COMP%] {\\n  width: 60px;\\n  text-align: center;\\n  background-color: #1C1C1E;\\n  border-radius: 8px;\\n  padding: 10px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 80px;\\n}\\n\\n.goal-inputs[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background: var(--accent);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 16px;\\n  font-weight: 600;\\n  width: 100%;\\n  margin-top: 20px;\\n  transition: background 0.2s;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #3A57C2;\\n}\\n\\n\\n\\n.goal-settings[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding-top: 15px;\\n  border-top: 1px solid var(--quest-border);\\n}\\n\\n.public-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.public-toggle[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 14px;\\n  color: var(--text);\\n  cursor: pointer;\\n}\\n\\n.public-toggle[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  accent-color: var(--accent);\\n  cursor: pointer;\\n}\\n\\n\\n\\n.goal-info-container[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.goal-info-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.description-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 10px;\\n}\\n\\n.edit-form[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.title-inputs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.emoji-input[_ngcontent-%COMP%] {\\n  width: 60px;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  text-align: center;\\n  font-size: 16px;\\n}\\n\\n.name-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  font-size: 14px;\\n}\\n\\n.description-textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  background: var(--pill);\\n  color: white;\\n  border: 1px solid #2c2c2e;\\n  border-radius: var(--radius);\\n  margin-bottom: 10px;\\n  min-height: 80px;\\n  resize: vertical;\\n  font-size: 14px;\\n}\\n\\n.form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["inject", "CommonModule", "FormsModule", "IonicModule", "RouterModule", "ActivatedRoute", "Router", "GoalService", "take", "NavigationComponent", "SupabaseService", "EmojiInputDirective", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GoalDetailPage_section_11_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showEditForm", "GoalDetailPage_section_11_Template_form_ngSubmit_10_listener", "updateGoalInfo", "ɵɵtwoWayListener", "GoalDetailPage_section_11_Template_input_ngModelChange_12_listener", "$event", "ɵɵtwoWayBindingSet", "edited<PERSON><PERSON><PERSON>", "GoalDetailPage_section_11_Template_input_ngModelChange_13_listener", "editedName", "GoalDetailPage_section_11_Template_textarea_ngModelChange_14_listener", "editedDescription", "GoalDetailPage_section_11_Template_button_click_18_listener", "cancelEdit", "ɵɵelement", "GoalDetailPage_section_11_Template_form_ngSubmit_30_listener", "updateProgress", "GoalDetailPage_section_11_Template_input_ngModelChange_34_listener", "currentValue", "GoalDetailPage_section_11_Template_input_change_40_listener", "togglePublic", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtextInterpolate2", "goal", "emoji", "name", "ɵɵtextInterpolate", "description", "ɵɵtwoWayProperty", "progressPercent", "current_value", "goal_value", "goal_unit", "ɵɵtextInterpolate1", "ɵɵproperty", "public", "GoalDetailPage_section_12_li_4_Template_button_click_2_listener", "microgoal_r5", "_r4", "$implicit", "toggleMicrogoal", "GoalDetailPage_section_12_li_4_Template_button_click_8_listener", "deleteMicrogoal", "ɵɵclassProp", "completed", "title", "ɵɵtemplate", "GoalDetailPage_section_12_li_4_Template", "GoalDetailPage_section_12_Template_form_ngSubmit_5_listener", "_r3", "addMicrogoal", "GoalDetailPage_section_12_Template_input_ngModelChange_6_listener", "newMicrogoalTitle", "microgoals", "GoalDetailPage_section_13_div_3_Template_div_click_0_listener", "entry_r7", "_r6", "openJournalEntry", "milestone_percentage", "getJournalPreview", "content", "GoalDetailPage_section_13_div_3_Template", "journalEntries", "GoalDetailPage_div_14_Template_form_ngSubmit_9_listener", "_r8", "addJournalEntry", "GoalDetailPage_div_14_Template_button_click_12_listener", "formatText", "GoalDetailPage_div_14_Template_button_click_15_listener", "GoalDetailPage_div_14_Template_button_click_18_listener", "GoalDetailPage_div_14_Template_button_click_21_listener", "GoalDetailPage_div_14_Template_button_click_25_listener", "GoalDetailPage_div_14_Template_button_click_28_listener", "insertList", "GoalDetailPage_div_14_Template_button_click_30_listener", "GoalDetailPage_div_14_Template_button_click_32_listener", "insertQuote", "GoalDetailPage_div_14_Template_div_input_34_listener", "onJournalContentInput", "GoalDetailPage_div_14_Template_div_blur_34_listener", "onJournalContentBlur", "nextMilestone", "GoalDetailPage", "constructor", "userId", "goalId", "showJournalModal", "journalContent", "supabaseService", "goalService", "route", "router", "ngOnInit", "currentUser$", "pipe", "subscribe", "user", "id", "paramMap", "params", "get", "loadGoal", "loadMicrogoals", "loadJournalEntries", "ngAfterViewInit", "journalEditor", "nativeElement", "innerHTML", "getGoal", "calculateProgress", "checkForMilestone", "console", "error", "getMicroGoals", "getJournalEntries", "entries", "Math", "min", "round", "currentPercent", "milestones", "existingMilestones", "map", "entry", "reachedMilestones", "filter", "milestone", "includes", "length", "setTimeout", "setupCheckboxListeners", "trimmedName", "trim", "<PERSON><PERSON><PERSON><PERSON>", "updateGoal", "then", "catch", "_this$goal", "_this$goal2", "_this$goal3", "microgoal", "toggleMicroGoalCompletion", "completed_at", "Date", "undefined", "deleteMicroGoal", "m", "newMicrogoal", "goal_id", "createMicroGoal", "push", "newEntry", "createJournalEntry", "textContent", "replace", "substring", "navigate", "command", "value", "document", "execCommand", "ordered", "event", "target", "checkboxes", "querySelectorAll", "for<PERSON>ach", "checkbox", "removeEventListener", "handleCheckboxChange", "bind", "addEventListener", "selection", "window", "getSelection", "rangeCount", "range", "getRangeAt", "quote", "createElement", "className", "deleteContents", "insertNode", "newRange", "createRange", "selectNodeContents", "removeAllRanges", "addRange", "insertCheckList", "checklistItem", "querySelector", "textSpan", "newPublicValue", "confirmDeleteGoal", "confirm", "deleteGoal", "selectors", "viewQuery", "GoalDetailPage_Query", "rf", "ctx", "GoalDetailPage_section_11_Template", "GoalDetailPage_section_12_Template", "GoalDetailPage_section_13_Template", "GoalDetailPage_div_14_Template", "GoalDetailPage_Template_button_click_16_listener", "ɵɵpureFunction0", "_c1", "i1", "IonIcon", "RouterLinkWithHrefDelegate", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "MaxLengthValidator", "NgModel", "NgForm", "i4", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.ts", "C:\\Users\\<USER>\\Desktop\\Upshift\\upshift\\src\\app\\pages\\goals\\goal-detail\\goal-detail.page.html"], "sourcesContent": ["import { Component, OnInit, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule, ActivatedRoute, Router } from '@angular/router';\nimport { GoalService } from '../../../services/goal.service';\nimport { Goal, GoalJournalEntry, MicroGoal } from '../../../models/goal.model';\nimport { take } from 'rxjs';\nimport { NavigationComponent } from '../../../components/navigation/navigation.component';\nimport { SupabaseService } from '../../../services/supabase.service';\nimport { EmojiInputDirective } from '../../../directives/emoji-input.directive';\n\n@Component({\n  selector: 'app-goal-detail',\n  templateUrl: './goal-detail.page.html',\n  styleUrls: ['./goal-detail.page.scss'],\n  standalone: true,\n  imports: [IonicModule, CommonModule, FormsModule, RouterModule, NavigationComponent, EmojiInputDirective]\n})\nexport class GoalDetailPage implements OnInit, AfterViewInit {\n  @ViewChild('journalEditor', { static: false }) journalEditor!: ElementRef<HTMLDivElement>;\n  // User data\n  userId: string | null = null;\n\n  // Goal data\n  goalId: string | null = null;\n  goal: Goal | null = null;\n  microgoals: MicroGoal[] = [];\n  journalEntries: GoalJournalEntry[] = [];\n\n  // UI state\n  showEditForm = false;\n  editedDescription = '';\n  editedName = '';\n  editedEmoji = '';\n  currentValue = 0;\n  progressPercent = 0;\n  showJournalModal = false;\n  nextMilestone: number | null = null;\n  journalContent = '';\n\n  // New microgoal\n  newMicrogoalTitle = '';\n\n  private supabaseService = inject(SupabaseService);\n  private goalService = inject(GoalService);\n  private route = inject(ActivatedRoute);\n  private router = inject(Router);\n\n  constructor() {}\n\n  ngOnInit() {\n    // Get the current user\n    this.supabaseService.currentUser$.pipe(\n      take(1)\n    ).subscribe(user => {\n      if (user) {\n        this.userId = user.id;\n\n        // Get the goal ID from the route\n        this.route.paramMap.pipe(\n          take(1)\n        ).subscribe(params => {\n          this.goalId = params.get('id');\n          if (this.goalId) {\n            this.loadGoal();\n            this.loadMicrogoals();\n            this.loadJournalEntries();\n          }\n        });\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    // Initialize journal editor content after view is ready\n    if (this.showJournalModal && this.journalEditor) {\n      this.journalEditor.nativeElement.innerHTML = this.journalContent;\n    }\n  }\n\n  loadGoal() {\n    if (!this.goalId) return;\n\n    this.goalService.getGoal(this.goalId).pipe(\n      take(1)\n    ).subscribe(goal => {\n      if (goal) {\n        this.goal = goal;\n        this.editedDescription = goal.description;\n        this.editedName = goal.name;\n        this.editedEmoji = goal.emoji;\n        this.currentValue = goal.current_value;\n        this.calculateProgress();\n        this.checkForMilestone();\n      } else {\n        console.error('Goal not found with ID:', this.goalId);\n      }\n    });\n  }\n\n  loadMicrogoals() {\n    if (!this.goalId) return;\n\n    this.goalService.getMicroGoals(this.goalId).subscribe(microgoals => {\n      this.microgoals = microgoals;\n    });\n  }\n\n  loadJournalEntries() {\n    if (!this.goalId) return;\n\n    this.goalService.getJournalEntries(this.goalId).subscribe(entries => {\n      this.journalEntries = entries;\n      // After loading entries, check if we need to show milestone modal\n      this.checkForMilestone();\n    });\n  }\n\n  calculateProgress() {\n    if (!this.goal) return;\n\n    this.progressPercent = this.goal.goal_value > 0\n      ? Math.min(100, Math.round((this.goal.current_value / this.goal.goal_value) * 100))\n      : 0;\n  }\n\n  checkForMilestone() {\n    if (!this.goal) return;\n\n    // Calculate current progress percentage\n    const currentPercent = this.progressPercent;\n\n    // Define milestone percentages\n    const milestones = [20, 40, 60, 80, 100];\n\n    // Get all milestone percentages that already have journal entries\n    const existingMilestones = this.journalEntries.map(entry => entry.milestone_percentage);\n\n    // Find all milestones that have been reached but don't have journal entries\n    const reachedMilestones = milestones.filter(milestone =>\n      currentPercent >= milestone &&\n      !existingMilestones.includes(milestone)\n    );\n\n\n    // Take the first one (lowest percentage) as the next milestone to show\n    if (reachedMilestones.length > 0) {\n      this.nextMilestone = reachedMilestones[0];\n      this.showJournalModal = true;\n\n      // Set content after view updates\n      setTimeout(() => {\n        if (this.journalEditor) {\n          this.journalEditor.nativeElement.innerHTML = this.journalContent;\n          this.setupCheckboxListeners();\n        }\n      }, 0);\n    } else {\n      this.nextMilestone = null;\n      this.showJournalModal = false;\n    }\n  }\n\n  updateGoalInfo() {\n    if (!this.goalId || !this.goal) return;\n\n    const trimmedName = this.editedName.trim();\n    if (!trimmedName) {\n      return; // Don't save if name is empty\n    }\n\n    // Use emoji as is (EmojiInputDirective already handles validation)\n    const finalEmoji = this.editedEmoji || '🎯';\n\n    this.goalService.updateGoal(this.goalId, {\n      name: trimmedName,\n      emoji: finalEmoji,\n      description: this.editedDescription\n    }).then(() => {\n      if (this.goal) {\n        this.goal.name = trimmedName;\n        this.goal.emoji = finalEmoji;\n        this.goal.description = this.editedDescription;\n      }\n      this.showEditForm = false;\n    }).catch(error => {\n      console.error('Error updating goal info:', error);\n    });\n  }\n\n  cancelEdit() {\n    this.showEditForm = false;\n    this.editedName = this.goal?.name || '';\n    this.editedEmoji = this.goal?.emoji || '';\n    this.editedDescription = this.goal?.description || '';\n  }\n\n  updateProgress() {\n    if (!this.goalId || !this.goal) return;\n\n\n    this.goalService.updateGoal(this.goalId, {\n      current_value: this.currentValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.current_value = this.currentValue;\n        this.calculateProgress();\n\n        // Reload journal entries to ensure we have the latest data before checking milestones\n        this.loadJournalEntries();\n      }\n    }).catch(error => {\n      console.error('Error updating progress:', error);\n    });\n  }\n\n  toggleMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.toggleMicroGoalCompletion(microgoal.id).then(() => {\n      // Update the local state\n      microgoal.completed = !microgoal.completed;\n      microgoal.completed_at = microgoal.completed ? new Date() : undefined;\n    });\n  }\n\n  deleteMicrogoal(microgoal: MicroGoal) {\n    if (!microgoal.id) return;\n\n    this.goalService.deleteMicroGoal(microgoal.id).then(() => {\n      // Remove from local array\n      this.microgoals = this.microgoals.filter(m => m.id !== microgoal.id);\n    });\n  }\n\n  addMicrogoal() {\n    if (!this.goalId || !this.newMicrogoalTitle.trim()) return;\n\n    const newMicrogoal: Omit<MicroGoal, 'id'> = {\n      goal_id: this.goalId,\n      title: this.newMicrogoalTitle.trim(),\n      completed: false\n    };\n\n    this.goalService.createMicroGoal(newMicrogoal).then(id => {\n      // Add to local array\n      this.microgoals.push({\n        ...newMicrogoal,\n        id\n      });\n\n      // Clear the input\n      this.newMicrogoalTitle = '';\n    });\n  }\n\n  addJournalEntry() {\n    if (!this.goalId || !this.nextMilestone || !this.journalContent.trim()) return;\n\n    const newEntry: Omit<GoalJournalEntry, 'id' | 'created_at'> = {\n      goal_id: this.goalId,\n      milestone_percentage: this.nextMilestone,\n      content: this.journalContent.trim()\n    };\n\n    this.goalService.createJournalEntry(newEntry).then(id => {\n\n      // Hide the modal and clear the input\n      this.showJournalModal = false;\n      this.journalContent = '';\n\n      // Reload journal entries from the database to ensure we have the latest data\n      this.loadJournalEntries();\n    }).catch(error => {\n      console.error('Error creating journal entry:', error);\n    });\n  }\n\n  getJournalPreview(content: string): string {\n    // Strip HTML tags and get first 100 characters\n    const textContent = content.replace(/<[^>]*>/g, '');\n    return textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;\n  }\n\n  openJournalEntry(entry: GoalJournalEntry) {\n    // Navigate to journal entry detail page\n    this.router.navigate(['/goals', this.goalId, 'journal', entry.id]);\n  }\n\n  // Rich text formatting methods\n  formatText(command: string, value?: string) {\n    document.execCommand(command, false, value);\n  }\n\n  insertList(ordered: boolean = false) {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    document.execCommand(command, false);\n  }\n\n  onJournalContentInput(event: any) {\n    this.journalContent = event.target.innerHTML;\n    this.setupCheckboxListeners();\n  }\n\n  onJournalContentBlur(event: any) {\n    this.journalContent = event.target.innerHTML;\n  }\n\n  setupCheckboxListeners() {\n    if (this.journalEditor) {\n      const checkboxes = this.journalEditor.nativeElement.querySelectorAll('.checklist-checkbox');\n      checkboxes.forEach(checkbox => {\n        checkbox.removeEventListener('change', this.handleCheckboxChange.bind(this));\n        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));\n      });\n    }\n  }\n\n  handleCheckboxChange() {\n    if (this.journalEditor) {\n      this.journalContent = this.journalEditor.nativeElement.innerHTML;\n    }\n  }\n\n  insertQuote() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const quote = document.createElement('blockquote');\n      quote.className = 'notion-quote';\n      quote.innerHTML = 'Quote text here...';\n\n      range.deleteContents();\n      range.insertNode(quote);\n\n      // Select the quote content\n      const newRange = document.createRange();\n      newRange.selectNodeContents(quote);\n      selection.removeAllRanges();\n      selection.addRange(newRange);\n    }\n  }\n\n  insertCheckList() {\n    const selection = window.getSelection();\n    if (selection && selection.rangeCount > 0) {\n      const range = selection.getRangeAt(0);\n      const checklistItem = document.createElement('div');\n      checklistItem.className = 'checklist-item';\n      checklistItem.innerHTML = '<input type=\"checkbox\" class=\"checklist-checkbox\"> <span contenteditable=\"true\">New item</span>';\n\n      range.deleteContents();\n      range.insertNode(checklistItem);\n\n      // Add event listener to the new checkbox\n      const checkbox = checklistItem.querySelector('.checklist-checkbox') as HTMLInputElement;\n      if (checkbox) {\n        checkbox.addEventListener('change', this.handleCheckboxChange.bind(this));\n      }\n\n      // Move cursor to the text span\n      const textSpan = checklistItem.querySelector('span');\n      if (textSpan) {\n        const newRange = document.createRange();\n        newRange.selectNodeContents(textSpan);\n        selection.removeAllRanges();\n        selection.addRange(newRange);\n      }\n\n      // Update content\n      this.journalContent = this.journalEditor.nativeElement.innerHTML;\n    }\n  }\n\n  togglePublic() {\n    if (!this.goalId || !this.goal) return;\n\n    const newPublicValue = !this.goal.public;\n\n    this.goalService.updateGoal(this.goalId, {\n      public: newPublicValue\n    }).then(() => {\n      if (this.goal) {\n        this.goal.public = newPublicValue;\n      }\n    }).catch(error => {\n      console.error('Error updating goal public status:', error);\n    });\n  }\n\n  confirmDeleteGoal() {\n    if (!this.goalId) return;\n\n    if (confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {\n      this.goalService.deleteGoal(this.goalId).then(() => {\n        this.router.navigate(['/goals']);\n      }).catch(error => {\n        console.error('Error deleting goal:', error);\n      });\n    }\n  }\n\n\n}\n", "<!-- Exact HTML from Django template with Angular syntax -->\r\n<div class=\"container\">\r\n    <header>\r\n        <div class=\"logo-wrap\">\r\n            <div class=\"logo\">\r\n                <img src=\"assets/images/upshift_icon_mini.svg\" alt=\"Upshift\">\r\n                <span>Upshift</span>\r\n            </div>\r\n        </div>\r\n        <h1>{{ goal?.name }}</h1>\r\n    </header>\r\n    <a [routerLink]=\"['/goals']\" class=\"back-link\">&larr; Back to goals</a>\r\n\r\n    <section class=\"goal-card\" *ngIf=\"goal\">\r\n        <!-- Goal Info Section -->\r\n        <div class=\"goal-info-container\" [style.display]=\"showEditForm ? 'none' : 'block'\">\r\n            <h2>{{goal.emoji}} {{ goal.name }}</h2>\r\n            <div class=\"description-container\">\r\n                <p class=\"goal-desc\">{{ goal.description }}</p>\r\n                <button class=\"edit-bio-btn\" (click)=\"showEditForm = true\">Edit</button>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Edit Form -->\r\n        <div class=\"edit-form\" [style.display]=\"showEditForm ? 'block' : 'none'\">\r\n            <form (ngSubmit)=\"updateGoalInfo()\">\r\n                <div class=\"title-inputs\">\r\n                    <input type=\"text\" name=\"emoji\" placeholder=\"🎯\" [(ngModel)]=\"editedEmoji\" class=\"emoji-input\" appEmojiInput>\r\n                    <input type=\"text\" name=\"name\" maxlength=\"100\" placeholder=\"Goal name\" [(ngModel)]=\"editedName\" class=\"name-input\">\r\n                </div>\r\n                <textarea name=\"description\" maxlength=\"500\" placeholder=\"Enter goal description\" [(ngModel)]=\"editedDescription\" class=\"description-textarea\"></textarea>\r\n                <div class=\"form-buttons\">\r\n                    <button type=\"submit\" class=\"save-btn\">Save</button>\r\n                    <button type=\"button\" class=\"cancel-btn\" (click)=\"cancelEdit()\">Cancel</button>\r\n                </div>\r\n            </form>\r\n        </div>\r\n\r\n        <div class=\"progress-container\">\r\n            <div class=\"progress-bar\">\r\n                <div class=\"progress-fill\" [style.width.%]=\"progressPercent\"></div>\r\n            </div>\r\n            <div class=\"progress-info\">\r\n                <span><strong>{{ goal.current_value }}</strong> / {{ goal.goal_value }} {{ goal.goal_unit }}</span>\r\n                <span class=\"percent\">{{ progressPercent }}%</span>\r\n            </div>\r\n        </div>\r\n\r\n        <form (ngSubmit)=\"updateProgress()\" class=\"progress-update\">\r\n            <label for=\"current_value\">Update value:</label>\r\n            <div class=\"save-bar\">\r\n                <input type=\"number\" name=\"current_value\" step=\"any\" [(ngModel)]=\"currentValue\">\r\n                <button name=\"update_progress\" type=\"submit\">Save</button>\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"goal-settings\">\r\n            <div class=\"public-toggle\">\r\n                <label>\r\n                    <input type=\"checkbox\" [checked]=\"goal.public\" (change)=\"togglePublic()\">\r\n                    Show on profile (public)\r\n                </label>\r\n            </div>\r\n        </div>\r\n    </section>\r\n\r\n    <section class=\"microgoals-section\" *ngIf=\"goal\">\r\n        <h3>Microgoals</h3>\r\n        <ul class=\"microgoals-list\">\r\n            <li class=\"microgoal-item\" *ngFor=\"let microgoal of microgoals\">\r\n                <div class=\"microgoal-form\">\r\n                    <button class=\"micro-btn\" type=\"button\" (click)=\"toggleMicrogoal(microgoal)\">\r\n                        <span class=\"checkmark\" [class.checked]=\"microgoal.completed\">\r\n                            {{ microgoal.completed ? '✔' : '☐' }}\r\n                        </span>\r\n                    </button>\r\n                    <span class=\"microgoal-title\">{{ microgoal.title }}</span>\r\n                </div>\r\n                <div class=\"delete-form\">\r\n                    <button class=\"delete-btn\" type=\"button\" (click)=\"deleteMicrogoal(microgoal)\">✖</button>\r\n                </div>\r\n            </li>\r\n        </ul>\r\n        <form (ngSubmit)=\"addMicrogoal()\" class=\"microgoal-add\">\r\n            <input name=\"microgoal_title\" type=\"text\" placeholder=\"New microgoal...\" [(ngModel)]=\"newMicrogoalTitle\">\r\n            <button name=\"add_microgoal\" type=\"submit\">➕ Add</button>\r\n        </form>\r\n    </section>\r\n\r\n    <section class=\"journal-section\" *ngIf=\"journalEntries.length > 0\">\r\n        <h3>📝 Goal Journal:</h3>\r\n        <div class=\"journal-entry\" *ngFor=\"let entry of journalEntries\" (click)=\"openJournalEntry(entry)\">\r\n            <div class=\"journal-entry-content\">\r\n                <div class=\"journal-entry-header\">\r\n                    <div class=\"milestone\">🎯 {{ entry.milestone_percentage }}%</div>\r\n                </div>\r\n                <p class=\"journal-preview\">{{ getJournalPreview(entry.content) }}</p>\r\n            </div>\r\n            <div class=\"journal-arrow\">></div>\r\n        </div>\r\n    </section>\r\n\r\n    <div class=\"journal-modal fancy\" *ngIf=\"showJournalModal && nextMilestone\">\r\n        <p>🎉 <strong>Congrats!</strong> You've reached <span class=\"milestone-highlight\">{{ nextMilestone }}%</span> of your goal.</p>\r\n\r\n        <form (ngSubmit)=\"addJournalEntry()\" class=\"inline-journal-form\">\r\n            <!-- Rich Text Toolbar -->\r\n            <div class=\"toolbar\">\r\n                <div class=\"toolbar-group\">\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('bold')\" title=\"Bold\">\r\n                        <strong>B</strong>\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('italic')\" title=\"Italic\">\r\n                        <em>I</em>\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('underline')\" title=\"Underline\">\r\n                        <u>U</u>\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('strikeThrough')\" title=\"Strikethrough\">\r\n                        <s>S</s>\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"toolbar-group\">\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"formatText('formatBlock', 'h3')\" title=\"Heading\">\r\n                        <strong>H</strong>\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(false)\" title=\"Bullet List\">\r\n                        •\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertList(true)\" title=\"Numbered List\">\r\n                        1.\r\n                    </button>\r\n                    <button type=\"button\" class=\"toolbar-btn\" (click)=\"insertQuote()\" title=\"Quote\">\r\n                        <ion-icon name=\"chatbox-outline\"></ion-icon>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- Rich Text Editor -->\r\n            <div\r\n                class=\"rich-editor-small\"\r\n                contenteditable=\"true\"\r\n                (input)=\"onJournalContentInput($event)\"\r\n                (blur)=\"onJournalContentBlur($event)\"\r\n                #journalEditor\r\n                placeholder=\"Write how you're progressing...\">\r\n            </div>\r\n\r\n            <button type=\"submit\" name=\"add_journal\">✍️ Add journal entry</button>\r\n        </form>\r\n    </div>\r\n\r\n    <!-- Remove Goal Button -->\r\n    <div class=\"delete-goal-container\">\r\n        <button type=\"button\" class=\"delete-goal-btn\" (click)=\"confirmDeleteGoal()\">Remove Goal</button>\r\n    </div>\r\n</div>\r\n\r\n<!-- Navigation -->\r\n<app-navigation></app-navigation>\r\n"], "mappings": ";AAAA,SAA4BA,MAAM,QAA8C,eAAe;AAC/F,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACtE,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,mBAAmB,QAAQ,qDAAqD;AACzF,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,mBAAmB,QAAQ,2CAA2C;;;;;;;;;;;ICMnEC,EAHR,CAAAC,cAAA,kBAAwC,cAE+C,SAC3E;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEnCH,EADJ,CAAAC,cAAA,cAAmC,YACV;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,iBAA2D;IAA9BD,EAAA,CAAAI,UAAA,mBAAAC,2DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,YAAA,GAAwB,IAAI;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,WAAI;IAEvEF,EAFuE,CAAAG,YAAA,EAAS,EACtE,EACJ;IAIFH,EADJ,CAAAC,cAAA,cAAyE,gBACjC;IAA9BD,EAAA,CAAAI,UAAA,sBAAAQ,6DAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC;IAE3Bb,EADJ,CAAAC,cAAA,eAA0B,iBACuF;IAA5DD,EAAA,CAAAc,gBAAA,2BAAAC,mEAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAU,WAAA,EAAAF,MAAA,MAAAR,MAAA,CAAAU,WAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAAyB;IAA1EhB,EAAA,CAAAG,YAAA,EAA6G;IAC7GH,EAAA,CAAAC,cAAA,iBAAmH;IAA5CD,EAAA,CAAAc,gBAAA,2BAAAK,mEAAAH,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAY,UAAA,EAAAJ,MAAA,MAAAR,MAAA,CAAAY,UAAA,GAAAJ,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAAwB;IACnGhB,EADI,CAAAG,YAAA,EAAmH,EACjH;IACNH,EAAA,CAAAC,cAAA,oBAA+I;IAA7DD,EAAA,CAAAc,gBAAA,2BAAAO,sEAAAL,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAc,iBAAA,EAAAN,MAAA,MAAAR,MAAA,CAAAc,iBAAA,GAAAN,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAA8BhB,EAAA,CAAAG,YAAA,EAAW;IAEtJH,EADJ,CAAAC,cAAA,eAA0B,kBACiB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAgE;IAAvBD,EAAA,CAAAI,UAAA,mBAAAmB,4DAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgB,UAAA,EAAY;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,cAAM;IAGlFF,EAHkF,CAAAG,YAAA,EAAS,EAC7E,EACH,EACL;IAGFH,EADJ,CAAAC,cAAA,eAAgC,eACF;IACtBD,EAAA,CAAAyB,SAAA,eAAmE;IACvEzB,EAAA,CAAAG,YAAA,EAAM;IAEIH,EADV,CAAAC,cAAA,eAA2B,YACjB,cAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnGH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAEpDF,EAFoD,CAAAG,YAAA,EAAO,EACjD,EACJ;IAENH,EAAA,CAAAC,cAAA,gBAA4D;IAAtDD,EAAA,CAAAI,UAAA,sBAAAsB,6DAAA;MAAA1B,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAmB,cAAA,EAAgB;IAAA,EAAC;IAC/B3B,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAE5CH,EADJ,CAAAC,cAAA,eAAsB,iBAC8D;IAA3BD,EAAA,CAAAc,gBAAA,2BAAAc,mEAAAZ,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAqB,YAAA,EAAAb,MAAA,MAAAR,MAAA,CAAAqB,YAAA,GAAAb,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA0B;IAA/EhB,EAAA,CAAAG,YAAA,EAAgF;IAChFH,EAAA,CAAAC,cAAA,kBAA6C;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAEzDF,EAFyD,CAAAG,YAAA,EAAS,EACxD,EACH;IAKKH,EAHZ,CAAAC,cAAA,eAA2B,eACI,aAChB,iBACsE;IAA1BD,EAAA,CAAAI,UAAA,oBAAA0B,4DAAA;MAAA9B,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAuB,YAAA,EAAc;IAAA,EAAC;IAAxE/B,EAAA,CAAAG,YAAA,EAAyE;IACzEH,EAAA,CAAAE,MAAA,kCACJ;IAGZF,EAHY,CAAAG,YAAA,EAAQ,EACN,EACJ,EACA;;;;IAjD2BH,EAAA,CAAAgC,SAAA,EAAiD;IAAjDhC,EAAA,CAAAiC,WAAA,YAAAzB,MAAA,CAAAG,YAAA,oBAAiD;IAC1EX,EAAA,CAAAgC,SAAA,GAA8B;IAA9BhC,EAAA,CAAAkC,kBAAA,KAAA1B,MAAA,CAAA2B,IAAA,CAAAC,KAAA,OAAA5B,MAAA,CAAA2B,IAAA,CAAAE,IAAA,KAA8B;IAETrC,EAAA,CAAAgC,SAAA,GAAsB;IAAtBhC,EAAA,CAAAsC,iBAAA,CAAA9B,MAAA,CAAA2B,IAAA,CAAAI,WAAA,CAAsB;IAM5BvC,EAAA,CAAAgC,SAAA,GAAiD;IAAjDhC,EAAA,CAAAiC,WAAA,YAAAzB,MAAA,CAAAG,YAAA,oBAAiD;IAGXX,EAAA,CAAAgC,SAAA,GAAyB;IAAzBhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAU,WAAA,CAAyB;IACHlB,EAAA,CAAAgC,SAAA,EAAwB;IAAxBhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAY,UAAA,CAAwB;IAEjBpB,EAAA,CAAAgC,SAAA,EAA+B;IAA/BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAc,iBAAA,CAA+B;IAUtFtB,EAAA,CAAAgC,SAAA,GAAiC;IAAjChC,EAAA,CAAAiC,WAAA,UAAAzB,MAAA,CAAAiC,eAAA,MAAiC;IAG9CzC,EAAA,CAAAgC,SAAA,GAAwB;IAAxBhC,EAAA,CAAAsC,iBAAA,CAAA9B,MAAA,CAAA2B,IAAA,CAAAO,aAAA,CAAwB;IAAU1C,EAAA,CAAAgC,SAAA,EAA4C;IAA5ChC,EAAA,CAAAkC,kBAAA,QAAA1B,MAAA,CAAA2B,IAAA,CAAAQ,UAAA,OAAAnC,MAAA,CAAA2B,IAAA,CAAAS,SAAA,KAA4C;IACtE5C,EAAA,CAAAgC,SAAA,GAAsB;IAAtBhC,EAAA,CAAA6C,kBAAA,KAAArC,MAAA,CAAAiC,eAAA,MAAsB;IAOSzC,EAAA,CAAAgC,SAAA,GAA0B;IAA1BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAqB,YAAA,CAA0B;IAQpD7B,EAAA,CAAAgC,SAAA,GAAuB;IAAvBhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAA2B,IAAA,CAAAY,MAAA,CAAuB;;;;;;IAY9C/C,EAFR,CAAAC,cAAA,aAAgE,cAChC,iBACqD;IAArCD,EAAA,CAAAI,UAAA,mBAAA4C,gEAAA;MAAA,MAAAC,YAAA,GAAAjD,EAAA,CAAAM,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4C,eAAA,CAAAH,YAAA,CAA0B;IAAA,EAAC;IACxEjD,EAAA,CAAAC,cAAA,eAA8D;IAC1DD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAO,EACF;IACTH,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;IAEFH,EADJ,CAAAC,cAAA,cAAyB,iBACyD;IAArCD,EAAA,CAAAI,UAAA,mBAAAiD,gEAAA;MAAA,MAAAJ,YAAA,GAAAjD,EAAA,CAAAM,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8C,eAAA,CAAAL,YAAA,CAA0B;IAAA,EAAC;IAACjD,EAAA,CAAAE,MAAA,aAAC;IAEvFF,EAFuF,CAAAG,YAAA,EAAS,EACtF,EACL;;;;IAT+BH,EAAA,CAAAgC,SAAA,GAAqC;IAArChC,EAAA,CAAAuD,WAAA,YAAAN,YAAA,CAAAO,SAAA,CAAqC;IACzDxD,EAAA,CAAAgC,SAAA,EACJ;IADIhC,EAAA,CAAA6C,kBAAA,MAAAI,YAAA,CAAAO,SAAA,4BACJ;IAE0BxD,EAAA,CAAAgC,SAAA,GAAqB;IAArBhC,EAAA,CAAAsC,iBAAA,CAAAW,YAAA,CAAAQ,KAAA,CAAqB;;;;;;IAT/DzD,EADJ,CAAAC,cAAA,kBAAiD,SACzC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,aAA4B;IACxBD,EAAA,CAAA0D,UAAA,IAAAC,uCAAA,kBAAgE;IAapE3D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAAwD;IAAlDD,EAAA,CAAAI,UAAA,sBAAAwD,4DAAA;MAAA5D,EAAA,CAAAM,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAsD,YAAA,EAAc;IAAA,EAAC;IAC7B9D,EAAA,CAAAC,cAAA,gBAAyG;IAAhCD,EAAA,CAAAc,gBAAA,2BAAAiD,kEAAA/C,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAiB,kBAAA,CAAAT,MAAA,CAAAwD,iBAAA,EAAAhD,MAAA,MAAAR,MAAA,CAAAwD,iBAAA,GAAAhD,MAAA;MAAA,OAAAhB,EAAA,CAAAU,WAAA,CAAAM,MAAA;IAAA,EAA+B;IAAxGhB,EAAA,CAAAG,YAAA,EAAyG;IACzGH,EAAA,CAAAC,cAAA,iBAA2C;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAExDF,EAFwD,CAAAG,YAAA,EAAS,EACtD,EACD;;;;IAlB+CH,EAAA,CAAAgC,SAAA,GAAa;IAAbhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAAyD,UAAA,CAAa;IAeWjE,EAAA,CAAAgC,SAAA,GAA+B;IAA/BhC,EAAA,CAAAwC,gBAAA,YAAAhC,MAAA,CAAAwD,iBAAA,CAA+B;;;;;;IAO5GhE,EAAA,CAAAC,cAAA,cAAkG;IAAlCD,EAAA,CAAAI,UAAA,mBAAA8D,8DAAA;MAAA,MAAAC,QAAA,GAAAnE,EAAA,CAAAM,aAAA,CAAA8D,GAAA,EAAAjB,SAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6D,gBAAA,CAAAF,QAAA,CAAuB;IAAA,EAAC;IAGrFnE,EAFR,CAAAC,cAAA,cAAmC,cACG,cACP;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAC/DF,EAD+D,CAAAG,YAAA,EAAM,EAC/D;IACNH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IACrEF,EADqE,CAAAG,YAAA,EAAI,EACnE;IACNH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAChCF,EADgC,CAAAG,YAAA,EAAM,EAChC;;;;;IAL6BH,EAAA,CAAAgC,SAAA,GAAoC;IAApChC,EAAA,CAAA6C,kBAAA,kBAAAsB,QAAA,CAAAG,oBAAA,MAAoC;IAEpCtE,EAAA,CAAAgC,SAAA,GAAsC;IAAtChC,EAAA,CAAAsC,iBAAA,CAAA9B,MAAA,CAAA+D,iBAAA,CAAAJ,QAAA,CAAAK,OAAA,EAAsC;;;;;IANzExE,EADJ,CAAAC,cAAA,kBAAmE,SAC3D;IAAAD,EAAA,CAAAE,MAAA,iCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAA0D,UAAA,IAAAe,wCAAA,kBAAkG;IAStGzE,EAAA,CAAAG,YAAA,EAAU;;;;IATuCH,EAAA,CAAAgC,SAAA,GAAiB;IAAjBhC,EAAA,CAAA8C,UAAA,YAAAtC,MAAA,CAAAkE,cAAA,CAAiB;;;;;;IAY9D1E,EADJ,CAAAC,cAAA,cAA2E,QACpE;IAAAD,EAAA,CAAAE,MAAA,oBAAG;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/HH,EAAA,CAAAC,cAAA,eAAiE;IAA3DD,EAAA,CAAAI,UAAA,sBAAAuE,wDAAA;MAAA3E,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAqE,eAAA,EAAiB;IAAA,EAAC;IAIxB7E,EAFR,CAAAC,cAAA,eAAqB,eACU,kBAC6D;IAA1CD,EAAA,CAAAI,UAAA,mBAAA0E,wDAAA;MAAA9E,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuE,UAAA,CAAW,MAAM,CAAC;IAAA,EAAC;IAClE/E,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACbF,EADa,CAAAG,YAAA,EAAS,EACb;IACTH,EAAA,CAAAC,cAAA,kBAAwF;IAA9CD,EAAA,CAAAI,UAAA,mBAAA4E,wDAAA;MAAAhF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuE,UAAA,CAAW,QAAQ,CAAC;IAAA,EAAC;IACpE/E,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACTF,EADS,CAAAG,YAAA,EAAK,EACL;IACTH,EAAA,CAAAC,cAAA,kBAA8F;IAApDD,EAAA,CAAAI,UAAA,mBAAA6E,wDAAA;MAAAjF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuE,UAAA,CAAW,WAAW,CAAC;IAAA,EAAC;IACvE/E,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACRF,EADQ,CAAAG,YAAA,EAAI,EACH;IACTH,EAAA,CAAAC,cAAA,kBAAsG;IAA5DD,EAAA,CAAAI,UAAA,mBAAA8E,wDAAA;MAAAlF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuE,UAAA,CAAW,eAAe,CAAC;IAAA,EAAC;IAC3E/E,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAEZF,EAFY,CAAAG,YAAA,EAAI,EACH,EACP;IAGFH,EADJ,CAAAC,cAAA,eAA2B,kBAC6E;IAA1DD,EAAA,CAAAI,UAAA,mBAAA+E,wDAAA;MAAAnF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuE,UAAA,CAAW,aAAa,EAAE,IAAI,CAAC;IAAA,EAAC;IAC/E/E,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAE,MAAA,SAAC;IACbF,EADa,CAAAG,YAAA,EAAS,EACb;IACTH,EAAA,CAAAC,cAAA,kBAA0F;IAAhDD,EAAA,CAAAI,UAAA,mBAAAgF,wDAAA;MAAApF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6E,UAAA,CAAW,KAAK,CAAC;IAAA,EAAC;IACjErF,EAAA,CAAAE,MAAA,gBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAjDD,EAAA,CAAAI,UAAA,mBAAAkF,wDAAA;MAAAtF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6E,UAAA,CAAW,IAAI,CAAC;IAAA,EAAC;IAChErF,EAAA,CAAAE,MAAA,YACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAgF;IAAtCD,EAAA,CAAAI,UAAA,mBAAAmF,wDAAA;MAAAvF,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgF,WAAA,EAAa;IAAA,EAAC;IAC7DxF,EAAA,CAAAyB,SAAA,oBAA4C;IAGxDzB,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;IAGNH,EAAA,CAAAC,cAAA,kBAMkD;IAF9CD,EADA,CAAAI,UAAA,mBAAAqF,qDAAAzE,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAkF,qBAAA,CAAA1E,MAAA,CAA6B;IAAA,EAAC,kBAAA2E,oDAAA3E,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAC/BF,MAAA,CAAAoF,oBAAA,CAAA5E,MAAA,CAA4B;IAAA,EAAC;IAGzChB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAAyC;IAAAD,EAAA,CAAAE,MAAA,sCAAoB;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EACnE,EACL;;;;IAhDgFH,EAAA,CAAAgC,SAAA,GAAoB;IAApBhC,EAAA,CAAA6C,kBAAA,KAAArC,MAAA,CAAAqF,aAAA,MAAoB;;;ADpF9G,OAAM,MAAOC,cAAc;EA8BzBC,YAAA;IA5BA;IACA,KAAAC,MAAM,GAAkB,IAAI;IAE5B;IACA,KAAAC,MAAM,GAAkB,IAAI;IAC5B,KAAA9D,IAAI,GAAgB,IAAI;IACxB,KAAA8B,UAAU,GAAgB,EAAE;IAC5B,KAAAS,cAAc,GAAuB,EAAE;IAEvC;IACA,KAAA/D,YAAY,GAAG,KAAK;IACpB,KAAAW,iBAAiB,GAAG,EAAE;IACtB,KAAAF,UAAU,GAAG,EAAE;IACf,KAAAF,WAAW,GAAG,EAAE;IAChB,KAAAW,YAAY,GAAG,CAAC;IAChB,KAAAY,eAAe,GAAG,CAAC;IACnB,KAAAyD,gBAAgB,GAAG,KAAK;IACxB,KAAAL,aAAa,GAAkB,IAAI;IACnC,KAAAM,cAAc,GAAG,EAAE;IAEnB;IACA,KAAAnC,iBAAiB,GAAG,EAAE;IAEd,KAAAoC,eAAe,GAAGhH,MAAM,CAACU,eAAe,CAAC;IACzC,KAAAuG,WAAW,GAAGjH,MAAM,CAACO,WAAW,CAAC;IACjC,KAAA2G,KAAK,GAAGlH,MAAM,CAACK,cAAc,CAAC;IAC9B,KAAA8G,MAAM,GAAGnH,MAAM,CAACM,MAAM,CAAC;EAEhB;EAEf8G,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,eAAe,CAACK,YAAY,CAACC,IAAI,CACpC9G,IAAI,CAAC,CAAC,CAAC,CACR,CAAC+G,SAAS,CAACC,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACZ,MAAM,GAAGY,IAAI,CAACC,EAAE;QAErB;QACA,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACJ,IAAI,CACtB9G,IAAI,CAAC,CAAC,CAAC,CACR,CAAC+G,SAAS,CAACI,MAAM,IAAG;UACnB,IAAI,CAACd,MAAM,GAAGc,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;UAC9B,IAAI,IAAI,CAACf,MAAM,EAAE;YACf,IAAI,CAACgB,QAAQ,EAAE;YACf,IAAI,CAACC,cAAc,EAAE;YACrB,IAAI,CAACC,kBAAkB,EAAE;UAC3B;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAClB,gBAAgB,IAAI,IAAI,CAACmB,aAAa,EAAE;MAC/C,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACpB,cAAc;IAClE;EACF;EAEAc,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAChB,MAAM,EAAE;IAElB,IAAI,CAACI,WAAW,CAACmB,OAAO,CAAC,IAAI,CAACvB,MAAM,CAAC,CAACS,IAAI,CACxC9G,IAAI,CAAC,CAAC,CAAC,CACR,CAAC+G,SAAS,CAACxE,IAAI,IAAG;MACjB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACA,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACb,iBAAiB,GAAGa,IAAI,CAACI,WAAW;QACzC,IAAI,CAACnB,UAAU,GAAGe,IAAI,CAACE,IAAI;QAC3B,IAAI,CAACnB,WAAW,GAAGiB,IAAI,CAACC,KAAK;QAC7B,IAAI,CAACP,YAAY,GAAGM,IAAI,CAACO,aAAa;QACtC,IAAI,CAAC+E,iBAAiB,EAAE;QACxB,IAAI,CAACC,iBAAiB,EAAE;MAC1B,CAAC,MAAM;QACLC,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAAC3B,MAAM,CAAC;MACvD;IACF,CAAC,CAAC;EACJ;EAEAiB,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;IAElB,IAAI,CAACI,WAAW,CAACwB,aAAa,CAAC,IAAI,CAAC5B,MAAM,CAAC,CAACU,SAAS,CAAC1C,UAAU,IAAG;MACjE,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC9B,CAAC,CAAC;EACJ;EAEAkD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAClB,MAAM,EAAE;IAElB,IAAI,CAACI,WAAW,CAACyB,iBAAiB,CAAC,IAAI,CAAC7B,MAAM,CAAC,CAACU,SAAS,CAACoB,OAAO,IAAG;MAClE,IAAI,CAACrD,cAAc,GAAGqD,OAAO;MAC7B;MACA,IAAI,CAACL,iBAAiB,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAD,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACtF,IAAI,EAAE;IAEhB,IAAI,CAACM,eAAe,GAAG,IAAI,CAACN,IAAI,CAACQ,UAAU,GAAG,CAAC,GAC3CqF,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,KAAK,CAAE,IAAI,CAAC/F,IAAI,CAACO,aAAa,GAAG,IAAI,CAACP,IAAI,CAACQ,UAAU,GAAI,GAAG,CAAC,CAAC,GACjF,CAAC;EACP;EAEA+E,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvF,IAAI,EAAE;IAEhB;IACA,MAAMgG,cAAc,GAAG,IAAI,CAAC1F,eAAe;IAE3C;IACA,MAAM2F,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAExC;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAC3D,cAAc,CAAC4D,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACjE,oBAAoB,CAAC;IAEvF;IACA,MAAMkE,iBAAiB,GAAGJ,UAAU,CAACK,MAAM,CAACC,SAAS,IACnDP,cAAc,IAAIO,SAAS,IAC3B,CAACL,kBAAkB,CAACM,QAAQ,CAACD,SAAS,CAAC,CACxC;IAGD;IACA,IAAIF,iBAAiB,CAACI,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC/C,aAAa,GAAG2C,iBAAiB,CAAC,CAAC,CAAC;MACzC,IAAI,CAACtC,gBAAgB,GAAG,IAAI;MAE5B;MACA2C,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACxB,aAAa,EAAE;UACtB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACC,SAAS,GAAG,IAAI,CAACpB,cAAc;UAChE,IAAI,CAAC2C,sBAAsB,EAAE;QAC/B;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,MAAM;MACL,IAAI,CAACjD,aAAa,GAAG,IAAI;MACzB,IAAI,CAACK,gBAAgB,GAAG,KAAK;IAC/B;EACF;EAEArF,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACoF,MAAM,IAAI,CAAC,IAAI,CAAC9D,IAAI,EAAE;IAEhC,MAAM4G,WAAW,GAAG,IAAI,CAAC3H,UAAU,CAAC4H,IAAI,EAAE;IAC1C,IAAI,CAACD,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;IAEA;IACA,MAAME,UAAU,GAAG,IAAI,CAAC/H,WAAW,IAAI,IAAI;IAE3C,IAAI,CAACmF,WAAW,CAAC6C,UAAU,CAAC,IAAI,CAACjD,MAAM,EAAE;MACvC5D,IAAI,EAAE0G,WAAW;MACjB3G,KAAK,EAAE6G,UAAU;MACjB1G,WAAW,EAAE,IAAI,CAACjB;KACnB,CAAC,CAAC6H,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAChH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACE,IAAI,GAAG0G,WAAW;QAC5B,IAAI,CAAC5G,IAAI,CAACC,KAAK,GAAG6G,UAAU;QAC5B,IAAI,CAAC9G,IAAI,CAACI,WAAW,GAAG,IAAI,CAACjB,iBAAiB;MAChD;MACA,IAAI,CAACX,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC,CAACyI,KAAK,CAACxB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CAAC;EACJ;EAEApG,UAAUA,CAAA;IAAA,IAAA6H,UAAA,EAAAC,WAAA,EAAAC,WAAA;IACR,IAAI,CAAC5I,YAAY,GAAG,KAAK;IACzB,IAAI,CAACS,UAAU,GAAG,EAAAiI,UAAA,OAAI,CAAClH,IAAI,cAAAkH,UAAA,uBAATA,UAAA,CAAWhH,IAAI,KAAI,EAAE;IACvC,IAAI,CAACnB,WAAW,GAAG,EAAAoI,WAAA,OAAI,CAACnH,IAAI,cAAAmH,WAAA,uBAATA,WAAA,CAAWlH,KAAK,KAAI,EAAE;IACzC,IAAI,CAACd,iBAAiB,GAAG,EAAAiI,WAAA,OAAI,CAACpH,IAAI,cAAAoH,WAAA,uBAATA,WAAA,CAAWhH,WAAW,KAAI,EAAE;EACvD;EAEAZ,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACsE,MAAM,IAAI,CAAC,IAAI,CAAC9D,IAAI,EAAE;IAGhC,IAAI,CAACkE,WAAW,CAAC6C,UAAU,CAAC,IAAI,CAACjD,MAAM,EAAE;MACvCvD,aAAa,EAAE,IAAI,CAACb;KACrB,CAAC,CAACsH,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAChH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACO,aAAa,GAAG,IAAI,CAACb,YAAY;QAC3C,IAAI,CAAC4F,iBAAiB,EAAE;QAExB;QACA,IAAI,CAACN,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC,CAACiC,KAAK,CAACxB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAxE,eAAeA,CAACoG,SAAoB;IAClC,IAAI,CAACA,SAAS,CAAC3C,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAACoD,yBAAyB,CAACD,SAAS,CAAC3C,EAAE,CAAC,CAACsC,IAAI,CAAC,MAAK;MACjE;MACAK,SAAS,CAAChG,SAAS,GAAG,CAACgG,SAAS,CAAChG,SAAS;MAC1CgG,SAAS,CAACE,YAAY,GAAGF,SAAS,CAAChG,SAAS,GAAG,IAAImG,IAAI,EAAE,GAAGC,SAAS;IACvE,CAAC,CAAC;EACJ;EAEAtG,eAAeA,CAACkG,SAAoB;IAClC,IAAI,CAACA,SAAS,CAAC3C,EAAE,EAAE;IAEnB,IAAI,CAACR,WAAW,CAACwD,eAAe,CAACL,SAAS,CAAC3C,EAAE,CAAC,CAACsC,IAAI,CAAC,MAAK;MACvD;MACA,IAAI,CAAClF,UAAU,GAAG,IAAI,CAACA,UAAU,CAACwE,MAAM,CAACqB,CAAC,IAAIA,CAAC,CAACjD,EAAE,KAAK2C,SAAS,CAAC3C,EAAE,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA/C,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACmC,MAAM,IAAI,CAAC,IAAI,CAACjC,iBAAiB,CAACgF,IAAI,EAAE,EAAE;IAEpD,MAAMe,YAAY,GAA0B;MAC1CC,OAAO,EAAE,IAAI,CAAC/D,MAAM;MACpBxC,KAAK,EAAE,IAAI,CAACO,iBAAiB,CAACgF,IAAI,EAAE;MACpCxF,SAAS,EAAE;KACZ;IAED,IAAI,CAAC6C,WAAW,CAAC4D,eAAe,CAACF,YAAY,CAAC,CAACZ,IAAI,CAACtC,EAAE,IAAG;MACvD;MACA,IAAI,CAAC5C,UAAU,CAACiG,IAAI,CAAC;QACnB,GAAGH,YAAY;QACflD;OACD,CAAC;MAEF;MACA,IAAI,CAAC7C,iBAAiB,GAAG,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEAa,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACoB,MAAM,IAAI,CAAC,IAAI,CAACJ,aAAa,IAAI,CAAC,IAAI,CAACM,cAAc,CAAC6C,IAAI,EAAE,EAAE;IAExE,MAAMmB,QAAQ,GAAgD;MAC5DH,OAAO,EAAE,IAAI,CAAC/D,MAAM;MACpB3B,oBAAoB,EAAE,IAAI,CAACuB,aAAa;MACxCrB,OAAO,EAAE,IAAI,CAAC2B,cAAc,CAAC6C,IAAI;KAClC;IAED,IAAI,CAAC3C,WAAW,CAAC+D,kBAAkB,CAACD,QAAQ,CAAC,CAAChB,IAAI,CAACtC,EAAE,IAAG;MAEtD;MACA,IAAI,CAACX,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,cAAc,GAAG,EAAE;MAExB;MACA,IAAI,CAACgB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,CAACiC,KAAK,CAACxB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,CAAC;EACJ;EAEArD,iBAAiBA,CAACC,OAAe;IAC/B;IACA,MAAM6F,WAAW,GAAG7F,OAAO,CAAC8F,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACnD,OAAOD,WAAW,CAACzB,MAAM,GAAG,GAAG,GAAGyB,WAAW,CAACE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGF,WAAW;EACvF;EAEAhG,gBAAgBA,CAACkE,KAAuB;IACtC;IACA,IAAI,CAAChC,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,EAAE,IAAI,CAACvE,MAAM,EAAE,SAAS,EAAEsC,KAAK,CAAC1B,EAAE,CAAC,CAAC;EACpE;EAEA;EACA9B,UAAUA,CAAC0F,OAAe,EAAEC,KAAc;IACxCC,QAAQ,CAACC,WAAW,CAACH,OAAO,EAAE,KAAK,EAAEC,KAAK,CAAC;EAC7C;EAEArF,UAAUA,CAACwF,OAAA,GAAmB,KAAK;IACjC,MAAMJ,OAAO,GAAGI,OAAO,GAAG,mBAAmB,GAAG,qBAAqB;IACrEF,QAAQ,CAACC,WAAW,CAACH,OAAO,EAAE,KAAK,CAAC;EACtC;EAEA/E,qBAAqBA,CAACoF,KAAU;IAC9B,IAAI,CAAC3E,cAAc,GAAG2E,KAAK,CAACC,MAAM,CAACxD,SAAS;IAC5C,IAAI,CAACuB,sBAAsB,EAAE;EAC/B;EAEAlD,oBAAoBA,CAACkF,KAAU;IAC7B,IAAI,CAAC3E,cAAc,GAAG2E,KAAK,CAACC,MAAM,CAACxD,SAAS;EAC9C;EAEAuB,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACzB,aAAa,EAAE;MACtB,MAAM2D,UAAU,GAAG,IAAI,CAAC3D,aAAa,CAACC,aAAa,CAAC2D,gBAAgB,CAAC,qBAAqB,CAAC;MAC3FD,UAAU,CAACE,OAAO,CAACC,QAAQ,IAAG;QAC5BA,QAAQ,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5EH,QAAQ,CAACI,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3E,CAAC,CAAC;IACJ;EACF;EAEAD,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAChE,aAAa,EAAE;MACtB,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACkB,aAAa,CAACC,aAAa,CAACC,SAAS;IAClE;EACF;EAEA/B,WAAWA,CAAA;IACT,MAAMgG,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMC,KAAK,GAAGnB,QAAQ,CAACoB,aAAa,CAAC,YAAY,CAAC;MAClDD,KAAK,CAACE,SAAS,GAAG,cAAc;MAChCF,KAAK,CAACvE,SAAS,GAAG,oBAAoB;MAEtCqE,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACJ,KAAK,CAAC;MAEvB;MACA,MAAMK,QAAQ,GAAGxB,QAAQ,CAACyB,WAAW,EAAE;MACvCD,QAAQ,CAACE,kBAAkB,CAACP,KAAK,CAAC;MAClCN,SAAS,CAACc,eAAe,EAAE;MAC3Bd,SAAS,CAACe,QAAQ,CAACJ,QAAQ,CAAC;IAC9B;EACF;EAEAK,eAAeA,CAAA;IACb,MAAMhB,SAAS,GAAGC,MAAM,CAACC,YAAY,EAAE;IACvC,IAAIF,SAAS,IAAIA,SAAS,CAACG,UAAU,GAAG,CAAC,EAAE;MACzC,MAAMC,KAAK,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAC,CAAC;MACrC,MAAMY,aAAa,GAAG9B,QAAQ,CAACoB,aAAa,CAAC,KAAK,CAAC;MACnDU,aAAa,CAACT,SAAS,GAAG,gBAAgB;MAC1CS,aAAa,CAAClF,SAAS,GAAG,iGAAiG;MAE3HqE,KAAK,CAACK,cAAc,EAAE;MACtBL,KAAK,CAACM,UAAU,CAACO,aAAa,CAAC;MAE/B;MACA,MAAMtB,QAAQ,GAAGsB,aAAa,CAACC,aAAa,CAAC,qBAAqB,CAAqB;MACvF,IAAIvB,QAAQ,EAAE;QACZA,QAAQ,CAACI,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,oBAAoB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3E;MAEA;MACA,MAAMqB,QAAQ,GAAGF,aAAa,CAACC,aAAa,CAAC,MAAM,CAAC;MACpD,IAAIC,QAAQ,EAAE;QACZ,MAAMR,QAAQ,GAAGxB,QAAQ,CAACyB,WAAW,EAAE;QACvCD,QAAQ,CAACE,kBAAkB,CAACM,QAAQ,CAAC;QACrCnB,SAAS,CAACc,eAAe,EAAE;QAC3Bd,SAAS,CAACe,QAAQ,CAACJ,QAAQ,CAAC;MAC9B;MAEA;MACA,IAAI,CAAChG,cAAc,GAAG,IAAI,CAACkB,aAAa,CAACC,aAAa,CAACC,SAAS;IAClE;EACF;EAEAxF,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACkE,MAAM,IAAI,CAAC,IAAI,CAAC9D,IAAI,EAAE;IAEhC,MAAMyK,cAAc,GAAG,CAAC,IAAI,CAACzK,IAAI,CAACY,MAAM;IAExC,IAAI,CAACsD,WAAW,CAAC6C,UAAU,CAAC,IAAI,CAACjD,MAAM,EAAE;MACvClD,MAAM,EAAE6J;KACT,CAAC,CAACzD,IAAI,CAAC,MAAK;MACX,IAAI,IAAI,CAAChH,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACY,MAAM,GAAG6J,cAAc;MACnC;IACF,CAAC,CAAC,CAACxD,KAAK,CAACxB,KAAK,IAAG;MACfD,OAAO,CAACC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D,CAAC,CAAC;EACJ;EAEAiF,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC5G,MAAM,EAAE;IAElB,IAAI6G,OAAO,CAAC,0EAA0E,CAAC,EAAE;MACvF,IAAI,CAACzG,WAAW,CAAC0G,UAAU,CAAC,IAAI,CAAC9G,MAAM,CAAC,CAACkD,IAAI,CAAC,MAAK;QACjD,IAAI,CAAC5C,MAAM,CAACiE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC,CAAC,CAACpB,KAAK,CAACxB,KAAK,IAAG;QACfD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;;kBA9XW9B,cAAc;;mCAAdA,eAAc;AAAA;;QAAdA,eAAc;EAAAkH,SAAA;EAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;;;;;;;;;;;;;MCffnN,EAHZ,CAAAC,cAAA,aAAuB,aACX,aACmB,aACD;MACdD,EAAA,CAAAyB,SAAA,aAA6D;MAC7DzB,EAAA,CAAAC,cAAA,WAAM;MAAAD,EAAA,CAAAE,MAAA,cAAO;MAErBF,EAFqB,CAAAG,YAAA,EAAO,EAClB,EACJ;MACNH,EAAA,CAAAC,cAAA,SAAI;MAAAD,EAAA,CAAAE,MAAA,GAAgB;MACxBF,EADwB,CAAAG,YAAA,EAAK,EACpB;MACTH,EAAA,CAAAC,cAAA,WAA+C;MAAAD,EAAA,CAAAE,MAAA,4BAAoB;MAAAF,EAAA,CAAAG,YAAA,EAAI;MA2FvEH,EAzFA,CAAA0D,UAAA,KAAA2J,kCAAA,uBAAwC,KAAAC,kCAAA,qBAqDS,KAAAC,kCAAA,qBAuBkB,KAAAC,8BAAA,kBAaQ;MAqDvExN,EADJ,CAAAC,cAAA,eAAmC,kBAC6C;MAA9BD,EAAA,CAAAI,UAAA,mBAAAqN,iDAAA;QAAA,OAASL,GAAA,CAAAP,iBAAA,EAAmB;MAAA,EAAC;MAAC7M,EAAA,CAAAE,MAAA,mBAAW;MAE/FF,EAF+F,CAAAG,YAAA,EAAS,EAC9F,EACJ;MAGNH,EAAA,CAAAyB,SAAA,sBAAiC;;;MAvJrBzB,EAAA,CAAAgC,SAAA,GAAgB;MAAhBhC,EAAA,CAAAsC,iBAAA,CAAA8K,GAAA,CAAAjL,IAAA,kBAAAiL,GAAA,CAAAjL,IAAA,CAAAE,IAAA,CAAgB;MAErBrC,EAAA,CAAAgC,SAAA,EAAyB;MAAzBhC,EAAA,CAAA8C,UAAA,eAAA9C,EAAA,CAAA0N,eAAA,IAAAC,GAAA,EAAyB;MAEA3N,EAAA,CAAAgC,SAAA,GAAU;MAAVhC,EAAA,CAAA8C,UAAA,SAAAsK,GAAA,CAAAjL,IAAA,CAAU;MAqDDnC,EAAA,CAAAgC,SAAA,EAAU;MAAVhC,EAAA,CAAA8C,UAAA,SAAAsK,GAAA,CAAAjL,IAAA,CAAU;MAuBbnC,EAAA,CAAAgC,SAAA,EAA+B;MAA/BhC,EAAA,CAAA8C,UAAA,SAAAsK,GAAA,CAAA1I,cAAA,CAAAkE,MAAA,KAA+B;MAa/B5I,EAAA,CAAAgC,SAAA,EAAuC;MAAvChC,EAAA,CAAA8C,UAAA,SAAAsK,GAAA,CAAAlH,gBAAA,IAAAkH,GAAA,CAAAvH,aAAA,CAAuC;;;iBDrFjEtG,WAAW,EAAAqO,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,0BAAA,EAAEzO,YAAY,EAAA0O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3O,WAAW,EAAA4O,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,oBAAA,EAAAF,EAAA,CAAAG,mBAAA,EAAAH,EAAA,CAAAI,eAAA,EAAAJ,EAAA,CAAAK,oBAAA,EAAAL,EAAA,CAAAM,kBAAA,EAAAN,EAAA,CAAAO,OAAA,EAAAP,EAAA,CAAAQ,MAAA,EAAElP,YAAY,EAAAmP,EAAA,CAAAC,UAAA,EAAE/O,mBAAmB,EAAEE,mBAAmB;EAAA8O,MAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}